﻿using Gateway.Biometrics.Application.Appeal.DTO;
using System;
using System.Collections.Generic;

namespace Gateway.Biometrics.Api.Models.Appeal
{
    public class BaseAppealRequestModel
    {
        public BaseAppealRequestModel()
        {
            AppealDetails = new List<BaseAppealDetailRequestModel>();
        }

        public int Id { get; set; }

        public string PassportNumber { get; set; }

        public string ReferenceNumber { get; set; }

        public string Name { get; set; }

        public string Surname { get; set; }

        public int Gender { get; set; }

        public string MotherName { get; set; }

        public string FatherName { get; set; }

        public DateTime BirthDate { get; set; }

        public string MaidenName { get; set; }

        public int AppealCountryId { get; set; }

        public int AppealCityId { get; set; }

        public int AppealOfficeId { get; set; }

        public int AppealCabinId { get; set; }

        public int Status { get; set; }

        public DateTime TakenDate { get; set; }

        public string HostName { get; set; }

        public string XmlData { get; set; }
        public bool FromOffline { get; set; }
        public List<BaseAppealDetailRequestModel> AppealDetails { get; set; }
    }
}
