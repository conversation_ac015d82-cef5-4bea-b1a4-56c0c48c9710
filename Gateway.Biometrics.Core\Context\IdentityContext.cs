﻿using Microsoft.AspNetCore.Http;
using System.Collections.Generic;

namespace Gateway.Biometrics.Core.Context
{
    internal class IdentityContext : IIdentityContext
    {
        public int UserId { get; set; }
        public string UserEmail { get; }
        public string Token { get; }
        public List<string> Roles { get; set; }

        public IdentityContext(HttpContext context)
        {           
        }
    }
}
