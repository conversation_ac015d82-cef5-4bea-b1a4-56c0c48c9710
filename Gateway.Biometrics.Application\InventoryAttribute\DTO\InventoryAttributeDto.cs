﻿using Gateway.Biometrics.Entity.Entities.Inventory;
using System;
using System.ComponentModel.DataAnnotations;

namespace Gateway.Biometrics.Application.InventoryAttribute.DTO
{
    public class InventoryAttributeDto
    {
        public int Id { get; set; }
        public string Name { get; set; }

        public string FieldType { get; set; }

        public string DataType { get; set; }

        public string DefaultValue { get; set; }

        public int MaxLength { get; set; }

        public bool IsRequired { get; set; }

        public bool IsSingleValue { get; set; }

        public int SortOrder { get; set; }

        public int Status { get; set; }
    }
}
