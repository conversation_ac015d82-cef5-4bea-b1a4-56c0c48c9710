﻿using Gateway.Cargo.Application.CargoTrack.Dto.Request;
using Gateway.Cargo.Application.CargoTrack.Dto.Response;
using Gateway.Cargo.Dto.Request;
using Gateway.Cargo.Dto.Response;
using System.Threading.Tasks;

namespace Gateway.Cargo.Application.CargoTrack
{
    public interface ICargoTrackService
    {
        Task<CargoTrackingResultDto> Track(TrackingRequest request);
        Task<GetListCargoTrackResult> ListCargoToCheckStatus(int? cargoProviderId);
        Task<GetCargoAreaResult> GetArea(GetAreaRequest request);
        Task<GetCargoGovernorateResult> GetGovernorate(GetGovernorateRequest request);
    }
}
