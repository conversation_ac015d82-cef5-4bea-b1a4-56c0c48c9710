﻿using System.Threading.Tasks;
using Gateway.Biometrics.Application.InventoryDefinition.DTO;

namespace Gateway.Biometrics.Application.InventoryDefinition
{
    public interface IInventoryDefinitionService
    {
        Task<GetInventoryDefinitionResult> GetInventoryDefinition(GetInventoryDefinitionRequest request);

        Task<CreateInventoryDefinitionResult> CreateInventoryDefinition(CreateInventoryDefinitionRequest request);

        Task<UpdateInventoryDefinitionResult> UpdateInventoryDefinition(UpdateInventoryDefinitionRequest request);
        
        Task<DeleteInventoryDefinitionResult> DeleteInventoryDefinition(DeleteInventoryDefinitionRequest request);

        Task<GetPaginatedInventoryDefinitionsResult> GetPaginatedInventoryDefinitions(GetPaginatedInventoryDefinitionsRequest request);
    }
}