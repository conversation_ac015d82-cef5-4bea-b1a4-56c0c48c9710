﻿using System.Collections.Generic;
using Gateway.Biometrics.Application.Office.DTO;

namespace Gateway.Biometrics.Application.Parameter.DTO
{
    public class OfficesDto
    {
        public IList<OfficeSelectDto> Offices { get; set; }
    }
    public class OfficeSelectDto
    {
        public int Id { get; set; }
        public string Name { get; set; }


    }

    public class OfficesRequestDto
    {
        public int? CountryId { get; set; }
    }

    public class OfficesResult : BaseServiceDataResult<OfficesDto>
    {
        public int Id { get; set; }
    }
}
