﻿using System.Threading.Tasks;
using Gateway.Biometrics.Application.Category.DTO;

namespace Gateway.Biometrics.Application.Category
{
    public interface ICategoryService
    {
        Task<CreateCategoryResult> CreateCategory(CreateCategoryRequest request);

        Task<UpdateCategoryResult> UpdateCategory(UpdateCategoryRequest request);

        Task<DeleteCategoryResult> DeleteCategory(DeleteCategoryRequest request);

        Task<GetPaginatedCategoriesResult> GetPaginatedCategories(GetPaginatedCategoriesRequest request);

        Task<GetCategoriesByParentIdResult> GetCategoriesByParentId(GetCategoriesByParentIdRequest request);
    }
}
