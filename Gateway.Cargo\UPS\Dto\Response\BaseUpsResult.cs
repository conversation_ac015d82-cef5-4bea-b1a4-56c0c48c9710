﻿namespace Gateway.Cargo.UPS.Dto.Response
{
    public class BaseUpsResult<TStatus>
    {
        public string Message { get; set; }
        public List<string> ValidationMessages { get; set; }
        public TStatus Status { get; set; }

        protected BaseUpsResult()
        {
            ValidationMessages = new List<string>();
        }
    }

    public class Response
    {
        public ResponseStatus ResponseStatus { get; set; }
        public Error[] Errors { get; set; }
        public Alert Alert { get; set; }
        public TransactionReference TransactionReference { get; set; }
    }

    public class ResponseStatus
    {
        public string Code { get; set; }
        public string Description { get; set; }
    }

    public class Error
    {
        public string Code { get; set; }
        public string Message { get; set; }
    }

    public class Alert
    {
        public string Code { get; set; }
        public string Description { get; set; }
    }

    public class TransactionReference
    {
        public string CustomerContext { get; set; }
        public string TransactionIdentifier { get; set; }
    }
}
