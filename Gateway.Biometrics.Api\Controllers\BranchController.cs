﻿using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Api.Models.Branch;
using Gateway.Biometrics.Application.Branch;
using Gateway.Biometrics.Application.Branch.DTO;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Resources;
using Gateway.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Api.Controllers
{
    [Authorize]
    [Route("api")]
    [ApiController]
    public class BranchController : Controller
    {
        private readonly IBranchService _branchService;
        private readonly IContext _context;

        #region ctor

        public BranchController(IBranchService branchService, IContext context)
        {
            _branchService = branchService;
            _context = context;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets Branches
        /// </summary>
        [SwaggerOperation(Summary = "Get Branches with given parameters",
            Description = "Get Branches with given parameters")]
        [HttpGet]
        [Route("branches")]
        public async Task<IActionResult> GetBranches()
        {
            var request = new GetBranchesRequest
            {
                Context = _context,
            };

            var result = await _branchService.GetBranches(request);

            return BranchResponseFactory.BranchesResponse(result);
        }


        #endregion
    }
}
