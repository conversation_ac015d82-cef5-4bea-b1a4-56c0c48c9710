﻿using Gateway.Cargo.Resources;
using Gateway.Core.CustomAttributes;
using Gateway.Core.Responses.Models;

namespace Gateway.Cargo.LastMile.Dto
{
    public class LastMileBaseServiceResult<TStatus, TData> where TData : class
    {
        public string Message { get; set; }
        public string StatusText { get; set; }
        public string Timestamp { get; set; }
        public int StatusCode { get; set; }
        public TStatus Status { get; set; }
        public TData Data { get; set; }
    }

    public class LastMileBaseServiceResult<TStatus>
    {
        public string Message { get; set; }
        public string StatusText { get; set; }
        public string Timestamp { get; set; }
        public int StatusCode { get; set; }
        public TStatus Status { get; set; }
        public bool Data { get; set; }
    }

    public class LastMileBaseServiceListResult<TStatus, TData, TMeta> where TMeta : class where TData : class
    {
        public string StatusText { get; set; }
        public string Message { get; set; }
        public string Timestamp { get; set; }
        public int StatusCode { get; set; }
        public TStatus Status { get; set; }
        public List<TData> Data { get; set; }
        public TMeta Meta { get; set; }
    }

    public class LastMileBaseServiceListResult<TStatus, TData> where TData : class
    {
        public string StatusText { get; set; }
        public string Message { get; set; }
        public string Timestamp { get; set; }
        public int StatusCode { get; set; }
        public TStatus Status { get; set; }
        public List<TData> Data { get; set; }
    }

    public class LastMileBaseMetaResult
    {
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
        public int ItemCount { get; set; }
        public int Page { get; set; }
        public int PageCount { get; set; }
        public int Take { get; set; }
    }

    public enum BaseLastMileStatus
    {
        [CustomHttpStatus(Code = "SUCCESS", Resources = typeof(ServiceResources), Status = "SUCCESS", StatusCode = HttpStatusCodes.Ok)]
        Successful,
        [CustomHttpStatus(Code = "INVALID_INPUT_ERROR", Resources = typeof(ServiceResources), Status = "INVALID_INPUT_ERROR", StatusCode = HttpStatusCodes.InvalidInput)]
        InvalidInput,
        [CustomHttpStatus(Code = "BAD_REQUEST", Resources = typeof(ServiceResources), Status = "BAD_REQUEST", StatusCode = HttpStatusCodes.BadRequest)]
        BadRequest,
        [CustomHttpStatus(Code = "RESOURCE_NOT_FOUND", Resources = typeof(ServiceResources), Status = "RESOURCE_NOT_FOUND", StatusCode = HttpStatusCodes.ResourceNotFound)]
        NotFound,
        [CustomHttpStatus(Code = "INTERNAL_SERVICE_ERROR", Resources = typeof(ServiceResources), Status = "INTERNAL_SERVICE_ERROR", StatusCode = HttpStatusCodes.InternalServerError)]
        InternalServerError,
        [CustomHttpStatus(Code = "EXTERNAL_SERVICE_ERROR", Resources = typeof(ServiceResources), Status = "EXTERNAL_SERVICE_ERROR", StatusCode = HttpStatusCodes.ServiceUnavailable)]
        ExternalServiceError
    }
}
