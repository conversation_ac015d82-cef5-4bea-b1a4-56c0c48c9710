﻿using System.Threading.Tasks;
using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.Office.DTO;

namespace Gateway.Biometrics.Application.Office
{
    public interface IOfficeService
    {
        Task<GetOfficeResult> GetOffice(GetOfficeRequest request);
        Task<CreateOfficeResult> CreateOffice(CreateOfficeRequest request);

        Task<UpdateOfficeResult> UpdateOffice(UpdateOfficeRequest request);
        
        Task<DeleteOfficeResult> DeleteOffice(DeleteOfficeRequest request);

        Task<GetPaginatedOfficesResult> GetPaginatedOffices(GetPaginatedOfficesRequest request);
    }
}