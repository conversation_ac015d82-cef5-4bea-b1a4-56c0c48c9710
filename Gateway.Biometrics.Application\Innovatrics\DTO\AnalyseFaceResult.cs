﻿using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Application;

namespace Gateway.Biometrics.Application.Innovatrics.DTO
{
    public class AnalyseFaceResult : BaseServiceResult<AnalyseFaceStatus>
    {
        //public IEnumerable<string> FaceImperfections { get; set; }
        public InnovatricsIFaceData IFaceData { get; set; }
    }

    public enum AnalyseFaceStatus
    {
        Successful,
        InvalidInput,
        ThereIsNoFace,
        ServerError
    }
}
