﻿using Gateway.Biometrics.Application;
using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.InventoryStatusLog.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.Biometrics.Api.Models.InventoryStatusLog
{
    public static class InventoryStatusLogResponseFactory
    {
        public static ObjectResult GetInventoryStatusLogResponse(GetInventoryStatusLogResult result)
        {
            switch (result.Status)
            {
                case GetInventoryStatusLogStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.InventoryStatusLog
                    })
                    { StatusCode = HttpStatusCodes.Created };
                case GetInventoryStatusLogStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case GetInventoryStatusLogStatus.NotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.CABIN_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.CABIN_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case GetInventoryStatusLogStatus.OfficeNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.OFFICE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.OFFICE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult CreateInventoryStatusLogResponse(CreateInventoryStatusLogResult result)
        {
            switch (result.Status)
            {
                case CreateInventoryStatusLogStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_CREATED),
                        Message = result.Message,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.Created };
                case CreateInventoryStatusLogStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case CreateInventoryStatusLogStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };
                case CreateInventoryStatusLogStatus.InventoryNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INVENTORY_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.INVENTORY_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }



        public static ObjectResult GetPaginatedInventoryStatusLogsResponse(GetPaginatedInventoryStatusLogsResult result)
        {
            switch (result.Status)
            {
                case GetPaginatedInventoryStatusLogsStatus.Successful:
                    return new ObjectResult(new BasePaginationApiResponse()
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.InventoryStatusLogs,
                        TotalNumberOfPages = result.TotalNumberOfPages,
                        TotalNumberOfRecords = result.TotalNumberOfRecords
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case GetPaginatedInventoryStatusLogsStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case GetPaginatedInventoryStatusLogsStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult GetLastInventoryStatusLogsForEachInventoriesOfCabinResponse(GetLastInventoryStatusLogsForEachInventoriesOfCabinResult result)
        {
            switch (result.Status)
            {
                case GetLastInventoryStatusLogsForEachInventoriesOfCabinStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.LastInventoryStatusLogsOfCabin,
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case GetLastInventoryStatusLogsForEachInventoriesOfCabinStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case GetLastInventoryStatusLogsForEachInventoriesOfCabinStatus.NotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }
    }
}