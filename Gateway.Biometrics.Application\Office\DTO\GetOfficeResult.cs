﻿using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.Office.DTO
{

    public class GetOfficeResult : BaseServiceResult<GetOfficeStatus>
    {
        public OfficeDto Office { get; set; }
    }

  
    public enum GetOfficeStatus
    {
        Successful,
        InvalidInput,
        NotFound,
        CountryNotFound
    }
}
