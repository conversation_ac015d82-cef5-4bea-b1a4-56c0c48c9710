﻿using System;
using System.Collections.Generic;

namespace Gateway.Biometrics.Application.InventoryAttribute.DTO
{
    public class GetPaginatedInventoryAttributesResult : BaseServiceResult<GetPaginatedInventoryAttributesStatus>
    {
        public IEnumerable<InventoryAttributeDto> InventoryAttributes { get; set; }
    }
    public enum GetPaginatedInventoryAttributesStatus
    {
        Successful,
        InvalidInput,
        ResourceNotFound
    }
}
