﻿using Gateway.Biometrics.Application.InventoryStatusLog.DTO;
using Microsoft.AspNetCore.SignalR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Gateway.Biometrics.Application.InventoryStatusLog;
using AutoMapper;
using Gateway.Biometrics.Api.Models.InventoryStatusLog;
using Gateway.Biometrics.Core.Context;

namespace Gateway.Biometrics.Api.Hubs
{
    public class InventoryStatusHub : Hub
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private IInventoryStatusLogService _inventoryStatusLogService;

        public InventoryStatusHub(IContext context, IMapper mapper, IInventoryStatusLogService inventoryStatusLogService)
        {
            _context = context;
            _mapper = mapper;
            _inventoryStatusLogService = inventoryStatusLogService;
        }
        /// <summary>
        /// Testing SignalR
        /// </summary>
        /// <param name="message"></param>
        /// <returns></returns>
        public async Task SendMessageAsync(string message)
        {
            await Clients.All.SendAsync("receiveMessage", message);
        }

        /// <summary>
        /// Add InventoryStatusLog
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task AddLogAsync(CreateInventoryStatusLogRequestModel request)
        {
            var serviceRequest = _mapper.Map<CreateInventoryStatusLogRequest>(request);
            serviceRequest.Context = _context;

            var result = await _inventoryStatusLogService.CreateInventoryStatusLog(serviceRequest);
            var response = InventoryStatusLogResponseFactory.CreateInventoryStatusLogResponse(result);

            await Clients.Caller.SendAsync("receiveMessage", result.Message);
        }
    }
}
