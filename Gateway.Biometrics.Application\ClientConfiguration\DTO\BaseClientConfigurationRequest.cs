﻿using Gateway.Biometrics.Entity.Entities.ClientConfiguration;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gateway.Biometrics.Application.ClientConfiguration.DTO
{
    public class BaseClientConfigurationRequest : BaseServiceRequest
    {
        public string HostName { get; set; }

        public string Description { get; set; }

        public int Status { get; set; }

        public int CountryId { get; set; }

        public int ProvinceId { get; set; }

        public int OfficeId { get; set; }

        public int CabinId { get; set; }

        public int LicenseId { get; set; }

        public string NeurotecLicenseNumber { get; set; }

        public virtual List<ClientConfigurationInventory> ClientConfigurationInventories { get; set; }

        public BaseClientConfigurationRequest()
        {

        }
    }


}

