﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gateway.Cargo.Entity.Entities.Application
{
    public class ApplicationStatusHistory
    {
        public ApplicationStatusHistory()
        {
            IsActive = true;
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public int ApplicationId { get; set; }

        [Required]
        public int ApplicationStatusId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public DateTimeOffset CreatedAt { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }


        public Application Application { get; set; }
        public ApplicationStatus ApplicationStatus { get; set; }
    }
}
