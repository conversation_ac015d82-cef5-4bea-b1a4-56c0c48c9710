﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Gateway.Biometrics.Entity.Entities.Inventory;

namespace Gateway.Biometrics.Application.Office.DTO
{
    public class BaseOfficeRequest : BaseServiceRequest
    {
        public string Name { get; set; }
        public string OfficeCode { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public int Status { get; set; }
        public int CountryId { get; set; }
        public int BranchId { get; set; }
        public BaseOfficeRequest()
        {

        }
    }


}

