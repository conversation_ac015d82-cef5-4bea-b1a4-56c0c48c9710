﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gateway.Biometrics.Entity.ExternalDbEntities
{
    public class BranchApplicationCountry
    {
        public BranchApplicationCountry()
        {
            IsActive = true;
        }

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Branch id from Branch entity
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// Branch authorized country id from Country entity
        /// </summary>
        public int CountryId { get; set; }

        [Column(TypeName = "citext"), MaxLength(1000)]
        public string Note { get; set; }

        public Country Country { get; set; }

        public ICollection<Application> Applications { get; set; }

        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }

    }
}
