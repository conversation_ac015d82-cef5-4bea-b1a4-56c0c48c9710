﻿{
  "$schema": "https://json.schemastore.org/launchsettings.json",
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:20285",
      "sslPort": 0
    }
  },
  "profiles": {
    "DigitalScreenApiGateway": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      //"launchUrl": "swagger",
      "applicationUrl": "https://localhost:8001",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": true,
      //"launchUrl": "swagger",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
