﻿using Gateway.Cargo.Resources;
using Gateway.Core.CustomAttributes;
using Gateway.Core.Responses.Models;

namespace Gateway.Cargo.UPS.Dto.Response
{
    public class UPSShipmentResult : BaseUpsResult<GetStatus>
    {
        public Response Response { get; set; }
        public ShipmentResponse ShipmentResponse { get; set; }
    }

    public class ShipmentResponse
    {
        public Response Response { get; set; }
        public ShipmentResults ShipmentResults { get; set; }
    }

    public class ShipmentResults
    {
        public Disclaimer Disclaimer { get; set; }
        public ShipmentCharges ShipmentCharges { get; set; }
        public string RatingMethod { get; set; }
        public string BillableWeightCalculationMethod { get; set; }
        public BillingWeight BillingWeight { get; set; }
        public string ShipmentIdentificationNumber { get; set; }
        public PackageResult PackageResults { get; set; }
    }

    public class Disclaimer
    {
        public string Code { get; set; }
        public string Description { get; set; }
    }

    public class ShipmentCharges
    {
        public TransportationCharges TransportationCharges { get; set; }
        public ServiceOptionsCharges ServiceOptionsCharges { get; set; }
        public TotalCharges TotalCharges { get; set; }
    }

    public class TransportationCharges
    {
        public string CurrencyCode { get; set; }
        public string MonetaryValue { get; set; }
    }

    public class ServiceOptionsCharges
    {
        public string CurrencyCode { get; set; }
        public string MonetaryValue { get; set; }
    }

    public class TotalCharges
    {
        public string CurrencyCode { get; set; }
        public string MonetaryValue { get; set; }
    }

    public class BillingWeight
    {
        public UnitOfMeasurement UnitOfMeasurement { get; set; }
        public string Weight { get; set; }
    }

    public class UnitOfMeasurement
    {
        public string Code { get; set; }
        public string Description { get; set; }
    }

    public class PackageResult
    {
        public string TrackingNumber { get; set; }
        public BaseServiceCharge BaseServiceCharge { get; set; }
        public ServiceOptionsCharges ServiceOptionsCharges { get; set; }
        public ShippingLabel ShippingLabel { get; set; }
        public ItemizedCharge[] ItemizedCharges { get; set; }
    }

    public class BaseServiceCharge
    {
        public string CurrencyCode { get; set; }
        public string MonetaryValue { get; set; }
    }

    public class ShippingLabel
    {
        public ImageFormat ImageFormat { get; set; }
        public string GraphicImage { get; set; }
    }

    public class ImageFormat
    {
        public string Code { get; set; }
        public string Description { get; set; }
    }

    public class ItemizedCharge
    {
        public string Code { get; set; }
        public string CurrencyCode { get; set; }
        public string MonetaryValue { get; set; }
        public string SubType { get; set; }
    }

    public enum GetStatus
    {
        [CustomHttpStatus(Code = "SUCCESS", Resources = typeof(ServiceResources), Status = "SUCCESS", StatusCode = HttpStatusCodes.Ok)]
        Successful,

        [CustomHttpStatus(Code = "INVALID_INPUT_ERROR", Resources = typeof(ServiceResources), Status = "INVALID_INPUT_ERROR", StatusCode = HttpStatusCodes.InvalidInput)]
        InvalidInput,

        [CustomHttpStatus(Code = "RESOURCE_NOT_FOUND", Resources = typeof(ServiceResources), Status = "RESOURCE_NOT_FOUND", StatusCode = HttpStatusCodes.ResourceNotFound)]
        NotFound,

        [CustomHttpStatus(Code = "INTERNAL_SERVICE_ERROR", Resources = typeof(ServiceResources), Status = "INTERNAL_SERVICE_ERROR", StatusCode = HttpStatusCodes.InternalServerError)]
        InternalServerError,

        [CustomHttpStatus(Code = "EXTERNAL_SERVICE_ERROR", Resources = typeof(ServiceResources), Status = "EXTERNAL_SERVICE_ERROR", StatusCode = HttpStatusCodes.ServiceUnavailable)]
        ExternalServiceError
    }
}
