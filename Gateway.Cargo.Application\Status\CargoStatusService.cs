﻿using System;
using System.Threading.Tasks;
using Gateway.Cargo.Application.Status.Dto.Request;
using Gateway.Cargo.Application.Status.Dto.Response;
using Gateway.Cargo.Application.Status.Validator;
using Gateway.Cargo.Dto.Response;
using Gateway.Cargo.Persistence;
using Gateway.Cargo.Resources;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Serilog;

namespace Gateway.Cargo.Application.Status
{
    public class CargoStatusService:ICargoStatusService
    {
        private readonly IValidationService _validationService;
        private readonly CargoDbContext _dbContext;
        private static readonly ILogger Logger = Log.ForContext<CargoStatusService>();
        private IConfiguration Configuration { get; }

        public CargoStatusService(IConfiguration configuration, IValidationService validationService, CargoDbContext dbContext)
        {
            _validationService = validationService;
            _dbContext = dbContext;
            Configuration = configuration;
        }
        public async Task<UpdateStatusResult> UpdateStatus(UpdateStatusRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateStatusRequestValidator), request);
            if (!validationResult.IsValid)
                return new UpdateStatusResult
                {
                    Status = UpdateCargoStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var existingCargo = await _dbContext.CargoTrack.FirstOrDefaultAsync(w =>
                    w.ApplicationId == request.ApplicationId && !w.IsDeleted &&
                    w.Description.Trim().ToLower().Equals("under processing") && w.ShipmentId != null &&
                    w.CargoProviderId == (int)Enums.Enums.CargoProviderType.LastMile);

                if (existingCargo == null)
                    return new UpdateStatusResult
                    {
                        Status = UpdateCargoStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    };

                var cargoBranch = await _dbContext.CargoBranch.FirstOrDefaultAsync(c => c.BranchId == existingCargo.BranchId && c.IsActive && !c.IsDeleted);
                if (cargoBranch == null)
                    return new UpdateStatusResult
                    {
                        Status = UpdateCargoStatus.InternalServerError,
                        Message = ServiceResources.BRANCH_NOT_FOUND
                    };

                var cargoService = CargoServiceProviderFactory.GetInstance(cargoBranch, Configuration);

                var result = await cargoService.UpdateStatus(new Cargo.Dto.Request.UpdateStatusRequest()
                {
                    ShipmentId = existingCargo.ShipmentId,
                    StatusId = request.StatusId
                });

                if (result is not { Status: UpdateCargoStatus.Successful })
                    return new UpdateStatusResult
                    {
                        Status = result.Status,
                        Message = result.Message,
                    };

                existingCargo.UserAgent = request.Context?.UserAgent;
                existingCargo.UpdatedAt = DateTime.Now;
                existingCargo.Description = "Ready to ship ";

                _dbContext.CargoTrack.Update(existingCargo);

                await _dbContext.SaveChangesAsync();
                
                return new UpdateStatusResult
                {
                    Status = UpdateCargoStatus.Successful,
                    Data = true
                };
            }
            catch (Exception ex)
            {
                Logger.Error(ex, ex.Message);

                return new UpdateStatusResult
                {
                    Status = UpdateCargoStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }
    }
}
