﻿using AutoMapper;
using Gateway.External.Api.Factories.ResponseFactory;
using Gateway.External.Api.Models.Payment;
using Gateway.External.Application.Payment;
using Gateway.External.Application.Payment.Dto.Requests;
using Gateway.External.Application.Payment.Dto.Response;
using Gateway.External.Core.Context;
using Gateway.External.Resources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;

namespace Gateway.External.Api.Controllers
{
	[Authorize]
	[Route("api")]
	[ApiController]
	public class PaymentController : Controller
	{
		private readonly IContext _context;
		private readonly IMapper _mapper;
		private readonly IPaymentService _paymentService;

		public PaymentController(IMapper mapper, IPaymentService paymentService, IContext context)
		{
			_mapper = mapper;
			_paymentService = paymentService;
			_context = context;
		}

		#region Public Methods

		/// <summary>
		/// Init3ds
		/// </summary>
		[SwaggerOperation(Summary = "returns html that bank returns for approving payment operation",
			Description = "returns html that bank returns for approving payment operation")]
		[HttpPost]
		[Route("payment/init3ds")]

		public async Task<IActionResult> Init3ds(Payment3dRequestModel requestModel)
		{
			if (requestModel == null)
				return BaseResponseFactory.CreateResponse(new Payment3dResult()
				{
					Status = Payment3dStatus.BadRequest,
					Message = ServiceResources.INVALID_REQUEST
				});

			var serviceRequest = _mapper.Map<Payment3dRequestModel, Payment3dRequest>(requestModel);

			var result = await _paymentService.Init3ds(serviceRequest, _context);

			return BaseResponseFactory.CreateResponse(result, result?.Data);
		}

		[HttpPost]
		[Route("payment/bin-info")]
		public async Task<IActionResult> BinInfo(BinInfoRequestModel requestModel)
		{
			if (requestModel == null)
				return BaseResponseFactory.CreateResponse(new BinInfoResult()
				{
					Status = BinInfoStatus.BadRequest,
					Message = ServiceResources.INVALID_REQUEST
				});

			var serviceRequest = _mapper.Map<BinInfoRequestModel, BinInfoRequest>(requestModel);

			var result = await _paymentService.BinInfo(serviceRequest);

			return BaseResponseFactory.CreateResponse(result, result?.BinInfo);
		}

		#endregion
	}
}
