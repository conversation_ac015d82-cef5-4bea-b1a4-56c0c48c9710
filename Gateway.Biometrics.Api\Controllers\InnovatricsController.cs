﻿using AutoMapper;
using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Api.Models.Innovatrics;
using Gateway.Biometrics.Application.Innovatrics;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;
using Gateway.Biometrics.Application.Innovatrics.DTO;

namespace Gateway.Biometrics.Api.Controllers
{
    [Authorize]
    [Route("api")]
    [ApiController]
    public class InnovatricsController : Controller
    {
        private readonly IInnovatricsService _innovatricsService;
        private readonly IContext _context;
        private readonly IMapper _mapper;

        #region ctor

        public InnovatricsController(IInnovatricsService innovatricsService,
            IContext context,
            IMapper mapper)
        {
            _innovatricsService = innovatricsService;
            _context = context;
            _mapper = mapper;
        }
        #endregion


        #region Public Methods

        /// <summary>
        /// Analyse the given face image for ICAO standarts
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Analyse the given face image for ICAO standarts",
            Description = "Analyse the given face image for ICAO standarts")]
        [HttpPost]
        [Route("iface/analyseFace")]
        public async Task<IActionResult> AnalyseFace(AnalyseFaceRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<AnalyseFaceRequest>(request);
            serviceRequest.Context = _context;

            var result =  _innovatricsService.AnalyseFace(serviceRequest);

            return InnovatricsResponseFactory.AnalyseFaceResponse(result);
        }

        /// <summary>
        /// Analyse the given face image for ICAO standarts
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Analyse the given face image for ICAO standarts",
            Description = "Analyse the given face image for ICAO standarts")]
        [HttpPost]
        [Route("iface/analyseFace2")]
        public async Task<IActionResult> AnalyseFace2(AnalyseFaceRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<AnalyseFaceRequest>(request);
            serviceRequest.Context = _context;

            var result = _innovatricsService.AnalyseFace2(serviceRequest);

            return InnovatricsResponseFactory.AnalyseFace2Response(result);
        }

        /// <summary>
        /// Analyse the given face image for ICAO standarts Using Digital Identity Service
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Analyse the given face image for ICAO standarts Using Digital Identity Service",
            Description = "Analyse the given face image for ICAO standarts Using Digital Identity Service")]
        [HttpPost]
        [Route("iface/analyseFaceDis")]
        public async Task<IActionResult> AnalyseFaceDis(AnalyseFaceRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<AnalyseFaceRequest>(request);
            serviceRequest.Context = _context;

            var result = await _innovatricsService.AnalyseFaceDis(serviceRequest);

            return InnovatricsResponseFactory.AnalyseFaceDisResponse(result);
        }

        #endregion
    }
}
