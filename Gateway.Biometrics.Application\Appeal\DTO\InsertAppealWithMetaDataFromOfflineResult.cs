﻿using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.Appeal.DTO
{
    public class InsertAppealWithMetaDataFromOfflineResult : BaseServiceResult<InsertAppealWithMetaDataFromOfflineStatus>
    {
        public string OfflineId { get; set; }
    }

    public enum InsertAppealWithMetaDataFromOfflineStatus
    {
        Successful,
        ResourceExists,
        InvalidInput,
        ResourceNotFound,
        InternalError
    }
}
