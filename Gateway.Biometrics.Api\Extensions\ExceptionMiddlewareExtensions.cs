﻿using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Resources;
using Gateway.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using System.Net;

namespace Gateway.Biometrics.Api.Extensions
{
    public static class ExceptionMiddlewareExtensions
    {
        public static void ConfigureExceptionHandler(this IApplicationBuilder app)
        {
            app.UseExceptionHandler(appError =>
            {
                appError.Run(async context =>
                {
                    context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    context.Response.ContentType = "application/json";

                    var contextFeature = context.Features.Get<IExceptionHandlerFeature>();
                    if (contextFeature != null)
                        await context.Response.WriteAsync(
                            ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INTERNAL_SERVER_ERROR,
                            Resource.GetKey(ServiceResources.INTERNAL_SERVER_ERROR),
                            ServiceResources.INTERNAL_SERVER_ERROR).ToJson());
                });
            });
        }
    }
}
