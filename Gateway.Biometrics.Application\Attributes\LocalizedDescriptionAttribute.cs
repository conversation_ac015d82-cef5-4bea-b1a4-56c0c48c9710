﻿using Gateway.Biometrics.Resources;
using System.ComponentModel;
using System.Resources; 

namespace Gateway.Biometrics.Application.Attributes
{
    public class LocalizedDescriptionAttribute : DescriptionAttribute
    {
        private readonly ResourceManager _resourceManager;

        public LocalizedDescriptionAttribute(string resourceKey)
        {
            _resourceManager = new ResourceManager(typeof(EnumResources));
            ResourceKey = resourceKey;
        }

        public override string Description
        {
            get
            {
                var description = _resourceManager.GetString(ResourceKey);
                return string.IsNullOrWhiteSpace(description) ? $"[[{ResourceKey}]]" : description;
            }
        }

        public string ResourceKey { get; }
    }
}