﻿using System.Threading.Tasks;
using Gateway.Biometrics.Application.InventoryAttribute.DTO;

namespace Gateway.Biometrics.Application.InventoryAttribute
{
    public interface IInventoryAttributeService
    {
        Task<CreateInventoryAttributeResult> CreateInventoryAttribute(CreateInventoryAttributeRequest request);

        Task<UpdateInventoryAttributeResult> UpdateInventoryAttribute(UpdateInventoryAttributeRequest request);
        
        Task<DeleteInventoryAttributeResult> DeleteInventoryAttribute(DeleteInventoryAttributeRequest request);

        Task<GetPaginatedInventoryAttributesResult> GetPaginatedInventoryAttributes(GetPaginatedInventoryAttributesRequest request);
    }
}