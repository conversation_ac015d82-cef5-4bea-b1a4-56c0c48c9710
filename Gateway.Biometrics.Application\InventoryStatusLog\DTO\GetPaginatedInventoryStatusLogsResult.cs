﻿using System.Collections.Generic;

namespace Gateway.Biometrics.Application.InventoryStatusLog.DTO
{
    public class GetPaginatedInventoryStatusLogsResult : BasePaginationServiceListResult<GetPaginatedInventoryStatusLogsStatus>
    {
        public List<InventoryStatusLogDto> InventoryStatusLogs { get; set; }
    }
}
public enum GetPaginatedInventoryStatusLogsStatus
{
    Successful,
    InvalidInput,
    ResourceNotFound
}
