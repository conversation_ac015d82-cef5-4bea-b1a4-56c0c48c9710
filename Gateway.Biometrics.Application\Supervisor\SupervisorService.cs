﻿using Gateway.Biometrics.Application.Supervisor.DTO;
using Gateway.Biometrics.Application.Supervisor.Validator;
using Gateway.Biometrics.Persistence;
using Gateway.Biometrics.Resources;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Portal.Gateway.Common.Utility.Extensions;
using Gateway.Biometrics.Entity.ExternalDbEntities;
using System;
using System.Threading.Tasks;
using Portal.Gateway.Contracts.Settings.AppSettings;
using Portal.Gateway.ExternalServices.Contracts;
using Microsoft.Extensions.Options;
using Gateway.Biometrics.Application.User.DTO;
using Portal.Gateway.ExternalServices.Models.Ldap;

namespace Gateway.Biometrics.Application.Supervisor
{
    public class SupervisorService : ISupervisorService
    {
        private readonly LdapSettings _ldapSettings;
        private readonly ILdapService _ldapService;
        private readonly ExternalDbContext _dbContext;
        private readonly IValidationService _validationService;

        public SupervisorService(
            IOptions<LdapSettings> ldapSettings,
            ILdapService ldapService,
            IValidationService validationService, 
            ExternalDbContext dbContext)
        {
            _ldapSettings = ldapSettings.Value;
            _ldapService = ldapService;
            _validationService = validationService;
            _dbContext = dbContext;
        }

        public async Task<SupervisorApprovalResult> SupervisorApproval(SupervisorApprovalRequest request)
        {
            var validationResult = _validationService.Validate(typeof(SupervisorValidator.SupervisorApprovalValidator), request);
            if (!validationResult.IsValid)
                return new SupervisorApprovalResult
                {
                    Status = SupervisorApprovalStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var ldapResponse = await _ldapService.AuthenticateLdapUser(request.UserName, request.Password);

            if (!ldapResponse.Success)
            {
                if (ldapResponse.ErrorCode == AuthenticateLdapUserResponse.ErrAccountLocked)
                {
                    return new SupervisorApprovalResult
                    {
                        Status = SupervisorApprovalStatus.UserNotFound,
                        Message = ServiceResources.LDAP_USER_ACCOUNT_LOCKED
                    };
                }
                else if (ldapResponse.ErrorCode == AuthenticateLdapUserResponse.ErrInvalidCredentials)
                {
                    return new SupervisorApprovalResult
                    {
                        Status = SupervisorApprovalStatus.UserNotFound,
                        Message = ServiceResources.LDAP_USER_INVALID_CREDENTIALS
                    };
                }
                else if (ldapResponse.ErrorCode == AuthenticateLdapUserResponse.ErrAccountExpired)
                {
                    return new SupervisorApprovalResult
                    {
                        Status = SupervisorApprovalStatus.UserNotFound,
                        Message = ServiceResources.LDAP_USER_ACCOUNT_EXPIRED
                    };
                }

                return new SupervisorApprovalResult
                {
                    Status = SupervisorApprovalStatus.UserNotFound,
                    Message = ServiceResources.LDAP_USER_ACCOUNT_NOT_FOUND
                };
            }

            Entity.ExternalDbEntities.User user = null;
            try
            {
                user = await _dbContext.User.FirstOrDefaultAsync(n => n.Email == ldapResponse.Email && n.IsActive && !n.IsDeleted);
            }
            catch (Exception ex)
            {
                return new SupervisorApprovalResult
                {
                    Status = SupervisorApprovalStatus.ResourceNotFound,
                    Message = ex.Message
                };
            }
            
            
            if (user == null)
            {
                return new SupervisorApprovalResult
                {
                    Status = SupervisorApprovalStatus.UserNotFound,
                    Message = ServiceResources.USER_NOT_FOUND
                };
            }

            var isAuthorized = await _dbContext.BranchSupervisorDefinition.AnyAsync(n => n.UserId == user.Id && n.BranchId == request.BranchId && n.IsActive && !n.IsDeleted);
            if (!isAuthorized) 
            {
                return new SupervisorApprovalResult
                {
                    Status = SupervisorApprovalStatus.UserNotAuthorized,
                    Message = ServiceResources.USER_NOT_AUTHORIZED
                };
            }

            var supervisorInfo = new SuperVisorInfoDto() { SuperVisorId = user.Id};           


            return new SupervisorApprovalResult
            {
                Status = SupervisorApprovalStatus.Successful,
                Message = ServiceResources.RESOURCE_RETRIEVED,
                SuperVisorInfo = supervisorInfo
            };
        }


    }
}
