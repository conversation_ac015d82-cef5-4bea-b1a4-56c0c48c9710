﻿using AutoMapper;
using Gateway.Cargo.Api.Models.Report;
using Gateway.Cargo.Core.Context;
using Gateway.Cargo.Resources;
using Gateway.Core.Responses;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Gateway.Cargo.Application.Report;
using Gateway.Cargo.Application.Report.Dto.Request;

namespace Gateway.Cargo.Api.Controllers
{
    [Route("api")]
    [ApiController]
    public class ReportController:Controller
    {
        private readonly IReportService _reportService;
        private readonly IContext _context;
        private readonly IMapper _mapper;

        public ReportController(IMapper mapper, IContext context, IReportService reportService)
        {
            _mapper = mapper;
            _context = context;
            _reportService = reportService;
        }

        [HttpPost]
        [Route("Report/DeliveredToCourier")]
        public async Task<IActionResult> DeliveredToCourier(BaseReportRequestModel<ReportRequestModelByBranches> requestModel)
        {
            if (requestModel == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST) ?? string.Empty,
                    ServiceResources.INVALID_REQUEST));

            var request = _mapper.Map<BaseReportRequest<ReportRequestByBranches>>(requestModel);
            request.Context = _context;

            var result = await _reportService.GetCargoTrackReportByStatus(request);

            return BaseResponseFactory.CreateResponse(result, result.ResultList);
        }

        [HttpPost]
        [Route("Report/CourierCheckReport")]
        public async Task<IActionResult> CourierCheckReport(BaseReportRequestModel<ReportRequestModelByBranches> requestModel)
        {
            if (requestModel == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST) ?? string.Empty,
                    ServiceResources.INVALID_REQUEST));

            var request = _mapper.Map<BaseReportRequest<ReportRequestByBranches>>(requestModel);
            request.Context = _context;

            var result = await _reportService.GetCourierCheckReportByStatus(request);

            return BaseResponseFactory.CreateResponse(result, result.Branches);
        }
    }
}
