﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gateway.Biometrics.Entity.Entities.ClientConfiguration
{
    public class ClientConfiguration : BaseEntity
    {  
        public string HostName { get; set; }

        public string Description { get; set; }

        public int Status { get; set; }

        public int CountryId { get; set; }

        public int ProvinceId { get; set; }

        public int OfficeId { get; set; }

        public int CabinId { get; set; }

        public int LicenseId { get; set; }

        public virtual Office.Office Office { get; set; }
        public virtual Cabin.Cabin Cabin { get; set; }

        [Obsolete("This property is obsolete. Use Cabin.Inventories instead of this.")]
        [ForeignKey("ClientConfigurationId")]
        public virtual IEnumerable<ClientConfigurationInventory> ClientConfigurationInventories { get; set; }
        //public virtual IEnumerable<Inventory.Inventory> Inventories { get; set; }
    }
}
