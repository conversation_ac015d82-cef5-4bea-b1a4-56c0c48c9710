﻿namespace Gateway.Cargo.LastMile.Dto.Result
{
    public class LastMilePackageDetailResult : LastMileBaseServiceListResult<BaseLastMileStatus, LastMilePackageDetailResult.PackageDetailResult, LastMileBaseMetaResult>
    {
        public class PackageDetailResult
        {
            public PackageDetailStatus Status { get; set; }
            public string Address { get; set; }
            public string AreaId { get; set; }
            public string AreaName { get; set; }
            public string CreatedAt { get; set; }
            public string CustomerId { get; set; }
            public string CustomerSignature { get; set; }
            public string DeletedAt { get; set; }
            public string Destination { get; set; }
            public string Distance { get; set; }
            public string DocId { get; set; }
            public string ExternalStatus { get; set; }
            public List<PackageDetailItems> GwItems { get; set; }
            public string HolderName { get; set; }
            public string Id { get; set; }
            public string InternalStatus { get; set; }
            public string IsActive { get; set; }
            public string Notes { get; set; }
            public string OriginShipmentId { get; set; }
            public string PackageType { get; set; }
            public string PaymentType { get; set; }
            public string PickupLocation { get; set; }
            public string Price { get; set; }
            public string QrCodeImage { get; set; }
            public string RecipientPhoneNumber { get; set; }
            public string ScannedAt { get; set; }
            public string SecondRecipientPhoneNumber { get; set; }
            public string ShipmentId { get; set; }
            public string SizeId { get; set; }
            public string Source { get; set; }
            public string StatusId { get; set; }
            public string TrackNumber { get; set; }
            public string TrackingNumberSegments { get; set; }
            public string UpdatedAt { get; set; }
            public string UserId { get; set; }

            public class PackageDetailItems
            {
                public string Address { get; set; }
                public string DocItem { get; set; }
                public string GwCode { get; set; } //CompanyId
                public string HolderName { get; set; } 
                public string Id { get; set; } 
                public string Notes { get; set; } 
                public string PackageId { get; set; } 
            }

            public class PackageDetailStatus
            {
                public string Id { get; set; }
                public string CreatedAt { get; set; }
                public string DeletedAt { get; set; }
                public List<PackageDetailStatusDescription> Description { get; set; }
                public List<PackageDetailStatusDescription> Internal { get; set; }
                public List<PackageDetailStatusDescription> External { get; set; }
                public string Slug { get; set; }
                public string UpdatedAt { get; set; }

                public class PackageDetailStatusDescription
                {
                    public string LanguageCode { get; set; }
                    public string Title { get; set; }
                }
            }
        }
    }
}
