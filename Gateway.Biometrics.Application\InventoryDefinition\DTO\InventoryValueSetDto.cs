﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;
using Gateway.Biometrics.Application.InventoryAttribute.DTO;

namespace Gateway.Biometrics.Application.InventoryDefinition.DTO
{
    public class InventoryValueSetDto
    {
        public int Id { get; set; }
        public int InventoryDefinitionId { get; set; }
        public int InventoryAttributeId { get; set; }
        public string Value { get; set; }
        public virtual InventoryAttributeDto InventoryAttribute { get; set; }
    }
}
