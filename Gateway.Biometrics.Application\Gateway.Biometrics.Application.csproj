﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="DLL\iface.dll" />
    <None Remove="DLL\Innovatrics.IFace.dll" />
    <None Remove="DLL\Innovatrics.IFace.xml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="DLL\iface.dll" />
    <Content Include="DLL\Innovatrics.IFace.dll" />
    <Content Include="DLL\Innovatrics.IFace.xml" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Gateway.Biometrics.Core\Gateway.Biometrics.Core.csproj" />
    <ProjectReference Include="..\Gateway.Biometrics.Persistence\Gateway.Biometrics.Persistence.csproj" />
    <ProjectReference Include="..\Gateway.Biometrics.Resources\Gateway.Biometrics.Resources.csproj" />
    <ProjectReference Include="..\Gateway.Core\Gateway.Core.csproj" />
    <ProjectReference Include="..\Gateway.IO\Gateway.IO.csproj" />
    <ProjectReference Include="..\Gateway.ObjectStoring.Minio\Gateway.ObjectStoring.Minio.csproj" />
    <ProjectReference Include="..\Gateway.ObjectStoring\Gateway.ObjectStoring.csproj" />
    <ProjectReference Include="..\Gateway.Validation\Gateway.Validation.csproj" />
    <ProjectReference Include="..\Portal.Gateway.Common.Utility\Portal.Gateway.Common.Utility.csproj" />
    <ProjectReference Include="..\Portal.Gateway.ExternalServices\Portal.Gateway.ExternalServices.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.12" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.4" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.8.3" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.8.0" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Innovatrics.IFace">
      <HintPath>DLL\Innovatrics.IFace.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>