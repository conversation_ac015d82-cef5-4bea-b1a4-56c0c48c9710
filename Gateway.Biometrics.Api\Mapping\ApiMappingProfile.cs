﻿using AutoMapper;
using Gateway.Biometrics.Api.Models.Appeal;
using Gateway.Biometrics.Api.Models.Cabin;
using Gateway.Biometrics.Api.Models.ClientConfiguration;
using Gateway.Biometrics.Api.Models.Innovatrics;
using Gateway.Biometrics.Api.Models.Inventory;
using Gateway.Biometrics.Api.Models.InventoryDefinition;
using Gateway.Biometrics.Api.Models.InventoryStatusLog;
using Gateway.Biometrics.Api.Models.Neurotec;
using Gateway.Biometrics.Api.Models.Office;
using Gateway.Biometrics.Api.Models.Supervisor;
using Gateway.Biometrics.Api.Models.User;
using Gateway.Biometrics.Application.Appeal.DTO;
using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.Category.DTO;
using Gateway.Biometrics.Application.ClientConfiguration.DTO;
using Gateway.Biometrics.Application.Innovatrics.DTO;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Application.InventoryAttribute.DTO;
using Gateway.Biometrics.Application.InventoryDefinition.DTO;
using Gateway.Biometrics.Application.InventoryStatusLog.DTO;
using Gateway.Biometrics.Application.Neurotec.DTO;
using Gateway.Biometrics.Application.Office.DTO;
using Gateway.Biometrics.Application.Parameter.DTO;
using Gateway.Biometrics.Application.Plugin.DTO;
using Gateway.Biometrics.Application.Supervisor.DTO;
using Gateway.Biometrics.Application.User.DTO;
using Gateway.Biometrics.Entity.Entities.Appeal;
using Gateway.Biometrics.Entity.Entities.Cabin;
using Gateway.Biometrics.Entity.Entities.Category;
using Gateway.Biometrics.Entity.Entities.ClientConfiguration;
using Gateway.Biometrics.Entity.Entities.Inventory;
using Gateway.Biometrics.Entity.Entities.NeuroTec;
using Gateway.Biometrics.Entity.Entities.Office;
using Gateway.Biometrics.Entity.Entities.Plugin;
using Gateway.Biometrics.Entity.ExternalDbEntities;

namespace Gateway.Biometrics.Api.Mapping
{
    public class ApiMappingProfile : Profile
    {
        public ApiMappingProfile()
        {
            #region Dto Mapping

            CreateMap<ClientConfiguration, ClientConfigurationDto>().ReverseMap();
            CreateMap<ClientConfiguration, ClientConfigurationForHostDto>().ReverseMap();
            CreateMap<InventoryDto, Inventory>().ReverseMap();
            CreateMap<InventoryTypeDto, InventoryType>().ReverseMap();
            CreateMap<InventoryDefinitionDto, InventoryDefinition>().ReverseMap();
            CreateMap<InventoryValueSetDto, InventoryValueSet>().ReverseMap();
            CreateMap<InventoryAttributeDto, InventoryAttribute>().ReverseMap();
            CreateMap<InventoryIpCameraDto, InventoryIpCamera>().ReverseMap();
            CreateMap<Plugin, PluginDto>().ReverseMap();
            CreateMap<GenericAttribute, GenericAttributeDto>().ReverseMap();
            CreateMap<ClientConfigurationInventory, ClientConfigurationInventoryDto>().ReverseMap();
            CreateMap<Cabin, CabinDto>().ReverseMap();
            CreateMap<Office, OfficeDto>().ReverseMap();
            CreateMap<InventoryStatusLog, InventoryStatusLogDto>().ReverseMap();
            CreateMap<Appeal, AppealDto>().ReverseMap();
            CreateMap<AppealMetaData, AppealMetaDataDto>().ReverseMap();
            CreateMap<User, PortalUserDto>().ReverseMap();
            CreateMap<NeurotecLicense, NeurotecLicenseSelectDto>().ReverseMap();
            CreateMap<NeurotecLicense, NeurotecLicenseDto>().ReverseMap();
            #endregion

            #region Request Mapping

            CreateMap<InsertAppealWithMetaDataRequestModel, InsertAppealWithMetaDataRequest>().ReverseMap();
            CreateMap<InsertAppealWithMetaDataFromOfflineRequestModel, InsertAppealWithMetaDataFromOfflineRequest>().ReverseMap();
            CreateMap<InsertAppealWithMetaDataFastRequestModel, InsertAppealWithMetaDataFastRequest>().ReverseMap();
            CreateMap<SaveAppealMetaDataFullRequestModel, SaveAppealMetaDataFullRequest>().ReverseMap();
            CreateMap<CreateAppealRequestModel, CreateAppealRequest>().ReverseMap();
            CreateMap<BaseAppealRequestModel, BaseAppealRequest>().ReverseMap();
            CreateMap<BaseAppealDetailRequest, BaseAppealDetailRequestModel>().ReverseMap();
            CreateMap<GetAppealsByXmlRequestModel, GetAppealsByXmlRequest>().ReverseMap();
            CreateMap<InsertAppealMetaDataRequest, InsertAppealMetaDataRequestModel>().ReverseMap();
            
            CreateMap<GetPaginatedCabinsRequestModel, GetPaginatedCabinsRequest>().ReverseMap();
            CreateMap<CreateCabinRequestModel, CreateCabinRequest>().ReverseMap();
            CreateMap<UpdateCabinRequestModel, UpdateCabinRequest>().ReverseMap();
            CreateMap<GetPaginatedCabinsRequestModel, GetPaginatedCabinsRequest>().ReverseMap();

            CreateMap<GetPaginatedOfficesRequestModel, GetPaginatedOfficesRequest>().ReverseMap();
            CreateMap<CreateOfficeRequestModel, CreateOfficeRequest>().ReverseMap();
            CreateMap<UpdateOfficeRequestModel, UpdateOfficeRequest>().ReverseMap();
            CreateMap<GetPaginatedOfficesRequestModel, GetPaginatedOfficesRequest>().ReverseMap();

            CreateMap<GetPaginatedClientConfigurationsRequestModel, GetPaginatedClientConfigurationsRequest>().ReverseMap();
            CreateMap<CreateClientConfigurationRequestModel, CreateClientConfigurationRequest>().ReverseMap();
            CreateMap<UpdateClientConfigurationRequestModel, UpdateClientConfigurationRequest>().ReverseMap();
            CreateMap<GetPaginatedClientConfigurationsRequestModel, GetPaginatedClientConfigurationsRequest>().ReverseMap();

            CreateMap<GetPaginatedInventoryDefinitionsRequestModel, GetPaginatedInventoryDefinitionsRequest>().ReverseMap();
            CreateMap<CreateInventoryDefinitionRequestModel, CreateInventoryDefinitionRequest>().ReverseMap();
            CreateMap<UpdateInventoryDefinitionRequestModel, UpdateInventoryDefinitionRequest>().ReverseMap();
            CreateMap<GetPaginatedInventoryDefinitionsRequestModel, GetPaginatedInventoryDefinitionsRequest>().ReverseMap();

            CreateMap<GetPaginatedInventoriesRequestModel, GetPaginatedInventoriesRequest>().ReverseMap();
            CreateMap<CreateInventoryRequestModel, CreateInventoryRequest>().ReverseMap();
            CreateMap<UpdateInventoryRequestModel, UpdateInventoryRequest>().ReverseMap();
            CreateMap<GetPaginatedInventoriesRequestModel, GetPaginatedInventoriesRequest>().ReverseMap();
            
            CreateMap<AnalyseFaceRequestModel, AnalyseFaceRequest>().ReverseMap();

            CreateMap<GetPaginatedInventoryStatusLogsRequestModel, GetPaginatedInventoryStatusLogsRequest>().ReverseMap();
            CreateMap<CreateInventoryStatusLogRequestModel, CreateInventoryStatusLogRequest>().ReverseMap();
            CreateMap<GetPaginatedInventoryStatusLogsRequestModel, GetPaginatedInventoryStatusLogsRequest>().ReverseMap();

            CreateMap<SupervisorApprovalRequestModel, SupervisorApprovalRequest>().ReverseMap();
            CreateMap<PortalUserLoginRequestModel, PortalUserLoginRequest>().ReverseMap();

            CreateMap<GetPaginatedNeurotecLicensesRequestModel, GetPaginatedNeurotecLicensesRequest>().ReverseMap(); 
            CreateMap<UpdateNeurotecLicenseRequestModel, UpdateNeurotecLicenseRequest>().ReverseMap();
            CreateMap<GetPaginatedNeurotecLicensesRequestModel, GetPaginatedNeurotecLicensesRequest>().ReverseMap();

            #endregion
        }
    }
}
