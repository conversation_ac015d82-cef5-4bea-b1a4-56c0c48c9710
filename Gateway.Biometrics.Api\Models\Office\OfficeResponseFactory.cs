﻿using Gateway.Biometrics.Application.Office.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.Biometrics.Api.Models.Office
{
    public static class OfficeResponseFactory
    {
        public static ObjectResult GetOfficeResponse(GetOfficeResult result)
        {
            switch (result.Status)
            {
                case GetOfficeStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.Office
                    })
                    { StatusCode = HttpStatusCodes.Created };
                case GetOfficeStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case GetOfficeStatus.NotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.OFFICE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.OFFICE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case GetOfficeStatus.CountryNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.COUNTRY_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.COUNTRY_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }


        public static ObjectResult CreateOfficeResponse(CreateOfficeResult result)
        {
            switch (result.Status)
            {
                case CreateOfficeStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_CREATED),
                        Message = result.Message,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.Created };
                case CreateOfficeStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case CreateOfficeStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };
                case CreateOfficeStatus.CountryNotfound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.COUNTRY_NOT_FOUND,
                            Code = Resource.GetKey(ServiceResources.COUNTRY_NOT_FOUND),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult UpdateOfficeResponse(UpdateOfficeResult result)
        {
            switch (result.Status)
            {
                case UpdateOfficeStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_UPDATED),
                        Message = result.Message,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case UpdateOfficeStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case UpdateOfficeStatus.OfficeNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.OFFICE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.OFFICE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateOfficeStatus.CountryNotfound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.COUNTRY_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.COUNTRY_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateOfficeStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult DeleteOfficeResponse(DeleteOfficeResult result)
        {
            switch (result.Status)
            {
                case DeleteOfficeStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_DELETED),
                        Message = result.Message,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case DeleteOfficeStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case DeleteOfficeStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult GetPaginatedOfficesResponse(GetPaginatedOfficesResult result)
        {
            switch (result.Status)
            {
                case GetPaginatedOfficesStatus.Successful:
                    return new ObjectResult(new BasePaginationApiResponse()
                        {
                            Status = ServiceResources.SUCCESS,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                            Message = result.Message,
                            Data = result.Offices,
                            TotalNumberOfPages = result.TotalNumberOfPages,
                            TotalNumberOfRecords = result.TotalNumberOfRecords
                        })
                        { StatusCode = HttpStatusCodes.Ok };
                case GetPaginatedOfficesStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case GetPaginatedOfficesStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }
    }
}