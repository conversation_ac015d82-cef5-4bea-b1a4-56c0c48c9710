﻿using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.InventoryDefinition.DTO
{

    public class GetInventoryDefinitionResult : BaseServiceResult<GetInventoryDefinitionStatus>
    {
        public InventoryDefinitionDto InventoryDefinition { get; set; }
    }

  
    public enum GetInventoryDefinitionStatus
    {
        Successful,
        InvalidInput,
        NotFound,
        InventoryTypeNotFound
    }
}
