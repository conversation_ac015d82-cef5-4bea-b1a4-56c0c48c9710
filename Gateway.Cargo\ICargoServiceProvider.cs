﻿using Gateway.Cargo.Dto.Request;
using Gateway.Cargo.Dto.Response;

namespace Gateway.Cargo
{
    public interface ICargoServiceProvider
	{
		void Initialize<TConfig>(TConfig config);

        Task<CargoTrackingResultDto> Tracking(string inquiryNumber, string packageId);

		Task<ShipmentResultDto> Shipment(ShipmentRequest request);

        Task<ShipmentResultDto> LabelRecovery(LabelRecoveryServiceRequest request);

        Task<GetAreaResultDto> GetArea(string governorateId);

        Task<GetGovernorateResultDto> GetGovernorate();

        Task<UpdateStatusDto> UpdateStatus(UpdateStatusRequest request);
	}
}
