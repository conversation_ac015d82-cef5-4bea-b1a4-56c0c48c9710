﻿using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.ClientConfiguration.DTO
{
    public class GetClientConfigurationByHostNameResult : BaseServiceResult<GetClientConfigurationByNameStatus>
    {
        public ClientConfigurationForHostDto ClientConfiguration { get; set; }
    }
     
    public enum GetClientConfigurationByNameStatus
    {
        Successful,
        InvalidInput,
        ResourceNotFound,
        CountryNotFound
    }
}
