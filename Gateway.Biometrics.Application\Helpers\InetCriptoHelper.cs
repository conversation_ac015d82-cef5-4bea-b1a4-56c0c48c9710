﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace Gateway.Biometrics.Application.Helpers
{
    public static class InetCriptoHelper
    {
        public static string EncryptMessage(string plainMessage)
        {
            string base64String;
            try
            {

                TripleDESCryptoServiceProvider cryptoServiceProvider = new TripleDESCryptoServiceProvider();
                cryptoServiceProvider.IV = new byte[8];
                byte[] md5Hash = ComputeMD5Hash("ZXCVBNMASDFGHJKLQWERTYUIOP");
                cryptoServiceProvider.Key = md5Hash;
                MemoryStream memoryStream = new MemoryStream(plainMessage.Length * 2);
                CryptoStream cryptoStream = new CryptoStream((Stream)memoryStream,
                    cryptoServiceProvider.CreateEncryptor(), CryptoStreamMode.Write);
                byte[] bytes = Encoding.UTF8.GetBytes(plainMessage);
                cryptoStream.Write(bytes, 0, bytes.Length);
                cryptoStream.FlushFinalBlock();
                byte[] numArray = new byte[memoryStream.Length];
                memoryStream.Position = 0L;
                memoryStream.Read(numArray, 0, (int)memoryStream.Length);
                cryptoStream.Close();
                base64String = Convert.ToBase64String(numArray);
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return base64String;
        }
        public static byte[] ComputeMD5Hash(string input)
        {
            using (var md5 = MD5.Create())
            {
                return md5.ComputeHash(Encoding.UTF8.GetBytes(input));
            }
        }

        public static string DecryptMessage(string encryptedBase64)
        {
            string str;
            try
            {
                TripleDESCryptoServiceProvider cryptoServiceProvider = new TripleDESCryptoServiceProvider();
                cryptoServiceProvider.IV = new byte[8];
                byte[] md5Hash = ComputeMD5Hash("ZXCVBNMASDFGHJKLQWERTYUIOP");
                cryptoServiceProvider.Key = md5Hash;
                byte[] buffer = Convert.FromBase64String(encryptedBase64);
                MemoryStream memoryStream = new MemoryStream(encryptedBase64.Length);
                CryptoStream cryptoStream = new CryptoStream((Stream)memoryStream,
                    cryptoServiceProvider.CreateDecryptor(), CryptoStreamMode.Write);
                cryptoStream.Write(buffer, 0, buffer.Length);
                cryptoStream.FlushFinalBlock();
                byte[] numArray = new byte[memoryStream.Length];
                memoryStream.Position = 0L;
                memoryStream.Read(numArray, 0, (int)memoryStream.Length);
                cryptoStream.Close();
                str = Encoding.UTF8.GetString(numArray);
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return str;
        }
    }
}
