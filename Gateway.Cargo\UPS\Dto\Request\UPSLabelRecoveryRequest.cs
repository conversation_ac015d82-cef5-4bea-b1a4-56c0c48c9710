﻿using Gateway.Http;

namespace Gateway.Cargo.UPS.Dto.Request
{
    public class UPSLabelRecoveryRequest : BaseHttpRequest
    {
        public LabelRecoveryRequest LabelRecoveryRequest { get; set; }
    }

    public class LabelRecoveryRequest
    {
        public LabelSpecification LabelSpecification { get; set; }
        public Translate Translate { get; set; }
        public LabelDelivery LabelDelivery { get; set; }
        public string TrackingNumber { get; set; }
    }

    public class Translate
    {
        public string LanguageCode { get; set; }
        public string DialectCode { get; set; }
        public string Code { get; set; }
    }

    public class LabelDelivery
    {
        public string LabelLinkIndicator { get; set; }
        public string ResendEMailIndicator { get; set; }
        public EMailMessage EMailMessage { get; set; }
    }

    public class EMailMessage
    {
        public string EMailAddress { get; set; }
    }
}
