﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Gateway.External.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class EnumResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal EnumResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Gateway.External.Resources.EnumResources", typeof(EnumResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refakat.
        /// </summary>
        public static string Accompaniment {
            get {
                return ResourceManager.GetString("Accompaniment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aktif.
        /// </summary>
        public static string Active {
            get {
                return ResourceManager.GetString("Active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ek evraklarla elçiliğe gönderildi.
        /// </summary>
        public static string AdditionalDocumentSentToEmbassy {
            get {
                return ResourceManager.GetString("AdditionalDocumentSentToEmbassy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ek evraklar elçilikte teslim alındı.
        /// </summary>
        public static string AdditionalDocumentsReceivedAtEmbassy {
            get {
                return ResourceManager.GetString("AdditionalDocumentsReceivedAtEmbassy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AED.
        /// </summary>
        public static string AED {
            get {
                return ResourceManager.GetString("AED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acente.
        /// </summary>
        public static string Agency {
            get {
                return ResourceManager.GetString("Agency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kurumsal.
        /// </summary>
        public static string AgencyCategoryCorporate {
            get {
                return ResourceManager.GetString("AgencyCategoryCorporate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bireysel.
        /// </summary>
        public static string AgencyCategoryIndividual {
            get {
                return ResourceManager.GetString("AgencyCategoryIndividual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aktif.
        /// </summary>
        public static string AgencyStatusActive {
            get {
                return ResourceManager.GetString("AgencyStatusActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beklemede.
        /// </summary>
        public static string AgencyStatusOnHold {
            get {
                return ResourceManager.GetString("AgencyStatusOnHold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasif.
        /// </summary>
        public static string AgencyStatusPassive {
            get {
                return ResourceManager.GetString("AgencyStatusPassive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sertifika.
        /// </summary>
        public static string AgencyTypeFileTypeCertificate {
            get {
                return ResourceManager.GetString("AgencyTypeFileTypeCertificate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diğer.
        /// </summary>
        public static string AgencyTypeFileTypeOther {
            get {
                return ResourceManager.GetString("AgencyTypeFileTypeOther", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tarım.
        /// </summary>
        public static string Agriculture {
            get {
                return ResourceManager.GetString("Agriculture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hava.
        /// </summary>
        public static string Air {
            get {
                return ResourceManager.GetString("Air", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yabancı pasaport.
        /// </summary>
        public static string AliensPassport {
            get {
                return ResourceManager.GetString("AliensPassport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tüm Kurumlar.
        /// </summary>
        public static string AllInstitutions {
            get {
                return ResourceManager.GetString("AllInstitutions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ambulans.
        /// </summary>
        public static string Ambulance {
            get {
                return ResourceManager.GetString("Ambulance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru sahibi isteği.
        /// </summary>
        public static string ApplicantRequest {
            get {
                return ResourceManager.GetString("ApplicantRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acente.
        /// </summary>
        public static string ApplicantTypeAgency {
            get {
                return ResourceManager.GetString("ApplicantTypeAgency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aile başvurusu.
        /// </summary>
        public static string ApplicantTypeFamily {
            get {
                return ResourceManager.GetString("ApplicantTypeFamily", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Grup başvurusu.
        /// </summary>
        public static string ApplicantTypeGroup {
            get {
                return ResourceManager.GetString("ApplicantTypeGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bireysel başvuru.
        /// </summary>
        public static string ApplicantTypeIndividual {
            get {
                return ResourceManager.GetString("ApplicantTypeIndividual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Temsilci.
        /// </summary>
        public static string ApplicantTypeRepresentative {
            get {
                return ResourceManager.GetString("ApplicantTypeRepresentative", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru.
        /// </summary>
        public static string Application {
            get {
                return ResourceManager.GetString("Application", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İptal.
        /// </summary>
        public static string ApplicationCancellationTypeCancellation {
            get {
                return ResourceManager.GetString("ApplicationCancellationTypeCancellation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kısmi iade.
        /// </summary>
        public static string ApplicationCancellationTypePartialRefund {
            get {
                return ResourceManager.GetString("ApplicationCancellationTypePartialRefund", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru alındı.
        /// </summary>
        public static string ApplicationDone {
            get {
                return ResourceManager.GetString("ApplicationDone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Biometrik fotoğraf.
        /// </summary>
        public static string ApplicationFileTypeBiometricPhoto {
            get {
                return ResourceManager.GetString("ApplicationFileTypeBiometricPhoto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Hasar) Ülkeye giriş damgası.
        /// </summary>
        public static string ApplicationFileTypeDamageEntryStamp {
            get {
                return ResourceManager.GetString("ApplicationFileTypeDamageEntryStamp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Hasar) Masraf dökümü.
        /// </summary>
        public static string ApplicationFileTypeDamageExpenseBill {
            get {
                return ResourceManager.GetString("ApplicationFileTypeDamageExpenseBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Hasar) Medikal rapor.
        /// </summary>
        public static string ApplicationFileTypeDamageMedicalReport {
            get {
                return ResourceManager.GetString("ApplicationFileTypeDamageMedicalReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Hasar) Beyan.
        /// </summary>
        public static string ApplicationFileTypeDamageStatement {
            get {
                return ResourceManager.GetString("ApplicationFileTypeDamageStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uçak rezervasyonu.
        /// </summary>
        public static string ApplicationFileTypeFlightBooking {
            get {
                return ResourceManager.GetString("ApplicationFileTypeFlightBooking", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sağlık sigortası.
        /// </summary>
        public static string ApplicationFileTypeHealthInsurance {
            get {
                return ResourceManager.GetString("ApplicationFileTypeHealthInsurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hotel rezervasyonu.
        /// </summary>
        public static string ApplicationFileTypeHotelReservation {
            get {
                return ResourceManager.GetString("ApplicationFileTypeHotelReservation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gelir belgesi.
        /// </summary>
        public static string ApplicationFileTypeIncome {
            get {
                return ResourceManager.GetString("ApplicationFileTypeIncome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diğer.
        /// </summary>
        public static string ApplicationFileTypeOther {
            get {
                return ResourceManager.GetString("ApplicationFileTypeOther", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasaport.
        /// </summary>
        public static string ApplicationFileTypePassport {
            get {
                return ResourceManager.GetString("ApplicationFileTypePassport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Ret) Data sayfası.
        /// </summary>
        public static string ApplicationFileTypeRejectionDataPage {
            get {
                return ResourceManager.GetString("ApplicationFileTypeRejectionDataPage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Ret) Pasaport.
        /// </summary>
        public static string ApplicationFileTypeRejectionPassport {
            get {
                return ResourceManager.GetString("ApplicationFileTypeRejectionPassport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Ret) İade beyannamesi.
        /// </summary>
        public static string ApplicationFileTypeRejectionReturnStatement {
            get {
                return ResourceManager.GetString("ApplicationFileTypeRejectionReturnStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diğer dokumanlar.
        /// </summary>
        public static string ApplicationFileTypeSupportDocument {
            get {
                return ResourceManager.GetString("ApplicationFileTypeSupportDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Konaklama bilgisi.
        /// </summary>
        public static string ApplicationFormElementAccomodationDetail {
            get {
                return ResourceManager.GetString("ApplicationFormElementAccomodationDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuranın Medeni Durumu.
        /// </summary>
        public static string ApplicationFormElementApplicantsMartialStatus {
            get {
                return ResourceManager.GetString("ApplicationFormElementApplicantsMartialStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Banka bakiyesi.
        /// </summary>
        public static string ApplicationFormElementBankBalance {
            get {
                return ResourceManager.GetString("ApplicationFormElementBankBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para birimi (Banka bakiyesi).
        /// </summary>
        public static string ApplicationFormElementBankBalanceCurrency {
            get {
                return ResourceManager.GetString("ApplicationFormElementBankBalanceCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ziyaret edilecek şehir.
        /// </summary>
        public static string ApplicationFormElementCityName {
            get {
                return ResourceManager.GetString("ApplicationFormElementCityName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Firma adı.
        /// </summary>
        public static string ApplicationFormElementCompanyName {
            get {
                return ResourceManager.GetString("ApplicationFormElementCompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Meslek.
        /// </summary>
        public static string ApplicationFormElementJob {
            get {
                return ResourceManager.GetString("ApplicationFormElementJob", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aylık maaş.
        /// </summary>
        public static string ApplicationFormElementMonthlySalary {
            get {
                return ResourceManager.GetString("ApplicationFormElementMonthlySalary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para birimi (Aylık maaş).
        /// </summary>
        public static string ApplicationFormElementMonthlySalaryCurrency {
            get {
                return ResourceManager.GetString("ApplicationFormElementMonthlySalaryCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasaport Geçerlilik Tarihi.
        /// </summary>
        public static string ApplicationFormElementPassportExpiryDate {
            get {
                return ResourceManager.GetString("ApplicationFormElementPassportExpiryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birlikte seyahat ettiği kişi.
        /// </summary>
        public static string ApplicationFormElementPersonTravelWith {
            get {
                return ResourceManager.GetString("ApplicationFormElementPersonTravelWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Masraf karşılama sponsor detayı.
        /// </summary>
        public static string ApplicationFormElementReimbursementSponsorDetail {
            get {
                return ResourceManager.GetString("ApplicationFormElementReimbursementSponsorDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Masraf karşılama türü.
        /// </summary>
        public static string ApplicationFormElementReimbursementType {
            get {
                return ResourceManager.GetString("ApplicationFormElementReimbursementType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Akrabanın bulunduğu yer.
        /// </summary>
        public static string ApplicationFormElementRelativeLocation {
            get {
                return ResourceManager.GetString("ApplicationFormElementRelativeLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kaç yıldır şirkette çalışıyor?.
        /// </summary>
        public static string ApplicationFormElementTotalYearInCompany {
            get {
                return ResourceManager.GetString("ApplicationFormElementTotalYearInCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kaç yıldır ülkede yaşıyor?.
        /// </summary>
        public static string ApplicationFormElementTotalYearInCountry {
            get {
                return ResourceManager.GetString("ApplicationFormElementTotalYearInCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru alınmadı.
        /// </summary>
        public static string ApplicationNotReceived {
            get {
                return ResourceManager.GetString("ApplicationNotReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru Numarası.
        /// </summary>
        public static string ApplicationNumber {
            get {
                return ResourceManager.GetString("ApplicationNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reddedilen pasaportların başvuru raporu.
        /// </summary>
        public static string ApplicationReportOfRejectedPassports {
            get {
                return ResourceManager.GetString("ApplicationReportOfRejectedPassports", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uygulama servisi.
        /// </summary>
        public static string ApplicationService {
            get {
                return ResourceManager.GetString("ApplicationService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru Statüsü.
        /// </summary>
        public static string ApplicationStatus {
            get {
                return ResourceManager.GetString("ApplicationStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aktif.
        /// </summary>
        public static string ApplicationStatusActive {
            get {
                return ResourceManager.GetString("ApplicationStatusActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İptal edildi.
        /// </summary>
        public static string ApplicationStatusCancelled {
            get {
                return ResourceManager.GetString("ApplicationStatusCancelled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tamamlandı.
        /// </summary>
        public static string ApplicationStatusCompleted {
            get {
                return ResourceManager.GetString("ApplicationStatusCompleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kısmi iade yapıldı.
        /// </summary>
        public static string ApplicationStatusPartiallyRefunded {
            get {
                return ResourceManager.GetString("ApplicationStatusPartiallyRefunded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasaport teslim edildi.
        /// </summary>
        public static string ApplicationStatusPassportsDelivered {
            get {
                return ResourceManager.GetString("ApplicationStatusPassportsDelivered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasaport teslim edilmeyi bekliyor.
        /// </summary>
        public static string ApplicationStatusPassportsWaitingToBeDelivered {
            get {
                return ResourceManager.GetString("ApplicationStatusPassportsWaitingToBeDelivered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pcr sonucu alındı.
        /// </summary>
        public static string ApplicationStatusPcrDelivered {
            get {
                return ResourceManager.GetString("ApplicationStatusPcrDelivered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pcr sonucu bekleniyor.
        /// </summary>
        public static string ApplicationStatusPcrWaiting {
            get {
                return ResourceManager.GetString("ApplicationStatusPcrWaiting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru Alındı.
        /// </summary>
        public static string ApplicationTaken {
            get {
                return ResourceManager.GetString("ApplicationTaken", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru Zamanı.
        /// </summary>
        public static string ApplicationTime {
            get {
                return ResourceManager.GetString("ApplicationTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ücretsiz başvuru.
        /// </summary>
        public static string ApplicationTypeFree {
            get {
                return ResourceManager.GetString("ApplicationTypeFree", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru dışı sigorta.
        /// </summary>
        public static string ApplicationTypeNonApplicationInsurance {
            get {
                return ResourceManager.GetString("ApplicationTypeNonApplicationInsurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru dışı PCR.
        /// </summary>
        public static string ApplicationTypeNonApplicationPcr {
            get {
                return ResourceManager.GetString("ApplicationTypeNonApplicationPcr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru Dışı Fotokopi.
        /// </summary>
        public static string ApplicationTypeNonApplicationPhotocopy {
            get {
                return ResourceManager.GetString("ApplicationTypeNonApplicationPhotocopy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru Dışı Fotoğraf.
        /// </summary>
        public static string ApplicationTypeNonApplicationPhotograph {
            get {
                return ResourceManager.GetString("ApplicationTypeNonApplicationPhotograph", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru Dışı Çıktı.
        /// </summary>
        public static string ApplicationTypeNonApplicationPrintOut {
            get {
                return ResourceManager.GetString("ApplicationTypeNonApplicationPrintOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Normal başvuru.
        /// </summary>
        public static string ApplicationTypeNormal {
            get {
                return ResourceManager.GetString("ApplicationTypeNormal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Turkuaz Başvuru.
        /// </summary>
        public static string ApplicationTypeTurquois {
            get {
                return ResourceManager.GetString("ApplicationTypeTurquois", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Turkuaz Gratis Başvuru.
        /// </summary>
        public static string ApplicationTypeTurquoisGratis {
            get {
                return ResourceManager.GetString("ApplicationTypeTurquoisGratis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Turkuaz Premium Başvuru.
        /// </summary>
        public static string ApplicationTypeTurquoisPremium {
            get {
                return ResourceManager.GetString("ApplicationTypeTurquoisPremium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvurunuz Değerlendirme Aşamasındadır.
        /// </summary>
        public static string ApplicationUnderEvaluation {
            get {
                return ResourceManager.GetString("ApplicationUnderEvaluation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasaportsuz başvuru.
        /// </summary>
        public static string ApplicationWithoutPassport {
            get {
                return ResourceManager.GetString("ApplicationWithoutPassport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasaportlu başvuru.
        /// </summary>
        public static string ApplicationWithPassport {
            get {
                return ResourceManager.GetString("ApplicationWithPassport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Randevu.
        /// </summary>
        public static string Appointment {
            get {
                return ResourceManager.GetString("Appointment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Onaylandı.
        /// </summary>
        public static string Approved {
            get {
                return ResourceManager.GetString("Approved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Onaylanan kota.
        /// </summary>
        public static string ApprovedQuota {
            get {
                return ResourceManager.GetString("ApprovedQuota", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Onaylanmış çalışma izni.
        /// </summary>
        public static string ApprovedWorkPermit {
            get {
                return ResourceManager.GetString("ApprovedWorkPermit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nisan.
        /// </summary>
        public static string April {
            get {
                return ResourceManager.GetString("April", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arapça.
        /// </summary>
        public static string Arabic {
            get {
                return ResourceManager.GetString("Arabic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arkeolojik kazı.
        /// </summary>
        public static string ArchaelogicalExcavation {
            get {
                return ResourceManager.GetString("ArchaelogicalExcavation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Silahlı güvenlik gücü.
        /// </summary>
        public static string ArmedSecurityForce {
            get {
                return ResourceManager.GetString("ArmedSecurityForce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sanatçı / oyuncu.
        /// </summary>
        public static string ArtistPerformer {
            get {
                return ResourceManager.GetString("ArtistPerformer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Asseco.
        /// </summary>
        public static string Asseco {
            get {
                return ResourceManager.GetString("Asseco", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Atanan sanatçılar.
        /// </summary>
        public static string AssignedArtists {
            get {
                return ResourceManager.GetString("AssignedArtists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Görev için atandı.
        /// </summary>
        public static string AssignedForDuty {
            get {
                return ResourceManager.GetString("AssignedForDuty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Atanan serbest bölge çalışanları.
        /// </summary>
        public static string AssignedFreeZoneWorkers {
            get {
                return ResourceManager.GetString("AssignedFreeZoneWorkers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Atanan gazeteci.
        /// </summary>
        public static string AssignedJournalist {
            get {
                return ResourceManager.GetString("AssignedJournalist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Atanan öğretim görevlileri / akademisyenler.
        /// </summary>
        public static string AssignedLecturersAcademics {
            get {
                return ResourceManager.GetString("AssignedLecturersAcademics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Atanan sporcu.
        /// </summary>
        public static string AssignedSportsperson {
            get {
                return ResourceManager.GetString("AssignedSportsperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ağustos.
        /// </summary>
        public static string August {
            get {
                return ResourceManager.GetString("August", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetki belgesi.
        /// </summary>
        public static string AuthorizationDocument {
            get {
                return ResourceManager.GetString("AuthorizationDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to B2B.
        /// </summary>
        public static string B2B {
            get {
                return ResourceManager.GetString("B2B", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to B2C.
        /// </summary>
        public static string B2C {
            get {
                return ResourceManager.GetString("B2C", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Banka havalesi ödemesi.
        /// </summary>
        public static string BankTransferPayment {
            get {
                return ResourceManager.GetString("BankTransferPayment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Biometrik.
        /// </summary>
        public static string Biometrics {
            get {
                return ResourceManager.GetString("Biometrics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BiomeTR ışık-kamera hareket.
        /// </summary>
        public static string BiomeTRLightCameraMotion {
            get {
                return ResourceManager.GetString("BiomeTRLightCameraMotion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string BirthDate {
            get {
                return ResourceManager.GetString("BirthDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Siyah.
        /// </summary>
        public static string Black {
            get {
                return ResourceManager.GetString("Black", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mavi.
        /// </summary>
        public static string Blue {
            get {
                return ResourceManager.GetString("Blue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Şube.
        /// </summary>
        public static string Branch {
            get {
                return ResourceManager.GetString("Branch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Açıklama.
        /// </summary>
        public static string BranchApplicationCountryFileTypeDescription {
            get {
                return ResourceManager.GetString("BranchApplicationCountryFileTypeDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bilgilendirme.
        /// </summary>
        public static string BranchApplicationCountryFileTypeInformation {
            get {
                return ResourceManager.GetString("BranchApplicationCountryFileTypeInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gerekli evraklar.
        /// </summary>
        public static string BranchApplicationCountryFileTypeRequirements {
            get {
                return ResourceManager.GetString("BranchApplicationCountryFileTypeRequirements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seyahat belgelerinin şartlarını ihlal etmek.
        /// </summary>
        public static string BreakingTravelDocuments {
            get {
                return ResourceManager.GetString("BreakingTravelDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İş.
        /// </summary>
        public static string Business {
            get {
                return ResourceManager.GetString("Business", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İş toplantısı / ticaret.
        /// </summary>
        public static string BusinessMeetingCommerce {
            get {
                return ResourceManager.GetString("BusinessMeetingCommerce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Randevulu.
        /// </summary>
        public static string ByAppointment {
            get {
                return ResourceManager.GetString("ByAppointment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Çağrı merkezi.
        /// </summary>
        public static string CallCenter {
            get {
                return ResourceManager.GetString("CallCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kamera.
        /// </summary>
        public static string Camera {
            get {
                return ResourceManager.GetString("Camera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İptal edilen.
        /// </summary>
        public static string Cancelled {
            get {
                return ResourceManager.GetString("Cancelled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bakıcı ve bebek bakıcısı.
        /// </summary>
        public static string CaregiverAndBabysitter {
            get {
                return ResourceManager.GetString("CaregiverAndBabysitter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kargo.
        /// </summary>
        public static string Cargo {
            get {
                return ResourceManager.GetString("Cargo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Çocuğu.
        /// </summary>
        public static string Child {
            get {
                return ResourceManager.GetString("Child", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suç komisyonu.
        /// </summary>
        public static string CommissionOfCrime {
            get {
                return ResourceManager.GetString("CommissionOfCrime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refakat.
        /// </summary>
        public static string Companion {
            get {
                return ResourceManager.GetString("Companion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tamamlandı.
        /// </summary>
        public static string Completed {
            get {
                return ResourceManager.GetString("Completed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Konferans - fuar.
        /// </summary>
        public static string ConferenceFair {
            get {
                return ResourceManager.GetString("ConferenceFair", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Konferans seminer toplantısı.
        /// </summary>
        public static string ConferenceSeminarMeeting {
            get {
                return ResourceManager.GetString("ConferenceSeminarMeeting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İstizan.
        /// </summary>
        public static string Consenting {
            get {
                return ResourceManager.GetString("Consenting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İnşaat.
        /// </summary>
        public static string Construction {
            get {
                return ResourceManager.GetString("Construction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Konsolosluk iptal iade.
        /// </summary>
        public static string ConsulateCanceledRefund {
            get {
                return ResourceManager.GetString("ConsulateCanceledRefund", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Konsolosluk kararı.
        /// </summary>
        public static string ConsulateDecision {
            get {
                return ResourceManager.GetString("ConsulateDecision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ülke.
        /// </summary>
        public static string Country {
            get {
                return ResourceManager.GetString("Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kurye.
        /// </summary>
        public static string Courier {
            get {
                return ResourceManager.GetString("Courier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kurye pasaportu ofise iade etmiştir.
        /// </summary>
        public static string CourierReturnedPassportToOffice {
            get {
                return ResourceManager.GetString("CourierReturnedPassportToOffice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kurs amacı.
        /// </summary>
        public static string CoursePurpose {
            get {
                return ResourceManager.GetString("CoursePurpose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oluşturuldu.
        /// </summary>
        public static string Created {
            get {
                return ResourceManager.GetString("Created", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suç kayıtları.
        /// </summary>
        public static string CriminalRecords {
            get {
                return ResourceManager.GetString("CriminalRecords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aşçılık.
        /// </summary>
        public static string CulinaryCookery {
            get {
                return ResourceManager.GetString("CulinaryCookery", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kültürel sanatsal etkinlik.
        /// </summary>
        public static string CulturalArtisticActivity {
            get {
                return ResourceManager.GetString("CulturalArtisticActivity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kültürel/sportif.
        /// </summary>
        public static string CulturalSportive {
            get {
                return ResourceManager.GetString("CulturalSportive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Menşe ülkeniz (vatandaşlığınız) dışında bir ülkede resmi oturma izniniz varsa, o ülkeye geri dönme izniniz var mı?.
        /// </summary>
        public static string DataTravelHistoryQuestion1 {
            get {
                return ResourceManager.GetString("DataTravelHistoryQuestion1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Farklı bir son varış noktasına seyahat ediyorsanız ve Türkiye&apos;den transit geçiş yapıyorsanız, son varış ülkeniz için giriş izniniz var mı?.
        /// </summary>
        public static string DataTravelHistoryQuestion2 {
            get {
                return ResourceManager.GetString("DataTravelHistoryQuestion2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hiç Türkiye vizesi veya oturma/çalışma izni aldınız mı?.
        /// </summary>
        public static string DataTravelHistoryQuestion3 {
            get {
                return ResourceManager.GetString("DataTravelHistoryQuestion3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hiç Türkiye vizesi veya oturma/çalışma izniniz reddedildi mi?.
        /// </summary>
        public static string DataTravelHistoryQuestion4 {
            get {
                return ResourceManager.GetString("DataTravelHistoryQuestion4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkiye&apos;ye girişiniz hiç giriş noktasında reddedildi mi?.
        /// </summary>
        public static string DataTravelHistoryQuestion5 {
            get {
                return ResourceManager.GetString("DataTravelHistoryQuestion5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hiç Türkiye&apos;den sınır dışı edildiniz mi veya Türkiye&apos;den ayrılmanız istendi mi?.
        /// </summary>
        public static string DataTravelHistoryQuestion6 {
            get {
                return ResourceManager.GetString("DataTravelHistoryQuestion6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkiye&apos;de hiç vizenizi veya oturma/çalışma izninizi aştınız mı?.
        /// </summary>
        public static string DataTravelHistoryQuestion7 {
            get {
                return ResourceManager.GetString("DataTravelHistoryQuestion7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkiye&apos;de hiç ciddi bir suç işlediniz mi?.
        /// </summary>
        public static string DataTravelHistoryQuestion8 {
            get {
                return ResourceManager.GetString("DataTravelHistoryQuestion8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vizeniz veya oturma/çalışma izniniz Türk makamları tarafından hiç iptal edildi mi?.
        /// </summary>
        public static string DataTravelHistoryQuestion9 {
            get {
                return ResourceManager.GetString("DataTravelHistoryQuestion9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tarih.
        /// </summary>
        public static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aralık.
        /// </summary>
        public static string December {
            get {
                return ResourceManager.GetString("December", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beyanname.
        /// </summary>
        public static string Declaration {
            get {
                return ResourceManager.GetString("Declaration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reddedilen.
        /// </summary>
        public static string Declined {
            get {
                return ResourceManager.GetString("Declined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Silindi.
        /// </summary>
        public static string Deleted {
            get {
                return ResourceManager.GetString("Deleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru sahibine teslim edilen.
        /// </summary>
        public static string DeliveredToApplicant {
            get {
                return ResourceManager.GetString("DeliveredToApplicant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kargoya Teslim Edildi.
        /// </summary>
        public static string DeliveredToCargo {
            get {
                return ResourceManager.GetString("DeliveredToCargo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kargoya teslim edilen.
        /// </summary>
        public static string DeliveredToPost {
            get {
                return ResourceManager.GetString("DeliveredToPost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diş Kliniği.
        /// </summary>
        public static string DentalClinic {
            get {
                return ResourceManager.GetString("DentalClinic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dijital cüzdan.
        /// </summary>
        public static string DigitalWallet {
            get {
                return ResourceManager.GetString("DigitalWallet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diplomatik pasaport.
        /// </summary>
        public static string DiplomaticPassport {
            get {
                return ResourceManager.GetString("DiplomaticPassport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Evrak Düzenleme.
        /// </summary>
        public static string DocumentEditing {
            get {
                return ResourceManager.GetString("DocumentEditing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Evrak Düzenleme Servisi.
        /// </summary>
        public static string DocumentEditingService {
            get {
                return ResourceManager.GetString("DocumentEditingService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Doküman yönetimi.
        /// </summary>
        public static string DocumentManagement {
            get {
                return ResourceManager.GetString("DocumentManagement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kalite kontrolü yapıldı.
        /// </summary>
        public static string DoneQualityCheck {
            get {
                return ResourceManager.GetString("DoneQualityCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Donor.
        /// </summary>
        public static string Donor {
            get {
                return ResourceManager.GetString("Donor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Çift geçiş.
        /// </summary>
        public static string DoubleTransit {
            get {
                return ResourceManager.GetString("DoubleTransit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taslak.
        /// </summary>
        public static string Draft {
            get {
                return ResourceManager.GetString("Draft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Şoför.
        /// </summary>
        public static string Driver {
            get {
                return ResourceManager.GetString("Driver", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kamyon Şoförü.
        /// </summary>
        public static string DriverLorry {
            get {
                return ResourceManager.GetString("DriverLorry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DZD.
        /// </summary>
        public static string DZD {
            get {
                return ResourceManager.GetString("DZD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eğitim ve öğretim.
        /// </summary>
        public static string EducationAndTraining {
            get {
                return ResourceManager.GetString("EducationAndTraining", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kuzey Kıbrıs Türk Cumhuriyeti&apos;nde eğitim.
        /// </summary>
        public static string EducationInTurkishRepublicOfNorthernCyprus {
            get {
                return ResourceManager.GetString("EducationInTurkishRepublicOfNorthernCyprus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eğitim amaçlı.
        /// </summary>
        public static string EducationPurpose {
            get {
                return ResourceManager.GetString("EducationPurpose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Emaa.
        /// </summary>
        public static string Emaa {
            get {
                return ResourceManager.GetString("Emaa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Özel istihdam amaçlı.
        /// </summary>
        public static string EmployementPurposeSpecialEmploymentPurpose {
            get {
                return ResourceManager.GetString("EmployementPurposeSpecialEmploymentPurpose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mühendis.
        /// </summary>
        public static string Engineer {
            get {
                return ResourceManager.GetString("Engineer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İngilizce.
        /// </summary>
        public static string English {
            get {
                return ResourceManager.GetString("English", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Giriş yasaklı.
        /// </summary>
        public static string EntryBanned {
            get {
                return ResourceManager.GetString("EntryBanned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Giriş Tarihi.
        /// </summary>
        public static string EntryDate {
            get {
                return ResourceManager.GetString("EntryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hata oluştu.
        /// </summary>
        public static string ErrorOccurred {
            get {
                return ResourceManager.GetString("ErrorOccurred", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EUR.
        /// </summary>
        public static string EUR {
            get {
                return ResourceManager.GetString("EUR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to E-Vize.
        /// </summary>
        public static string Evisa {
            get {
                return ResourceManager.GetString("Evisa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mevcut veri.
        /// </summary>
        public static string ExistingData {
            get {
                return ResourceManager.GetString("ExistingData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Çıkış Tarihi.
        /// </summary>
        public static string ExitDate {
            get {
                return ResourceManager.GetString("ExitDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zaman aşımı.
        /// </summary>
        public static string Expired {
            get {
                return ResourceManager.GetString("Expired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ekstra dosya.
        /// </summary>
        public static string ExtraFile {
            get {
                return ResourceManager.GetString("ExtraFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yüz analiz servisi.
        /// </summary>
        public static string FaceAnalysisService {
            get {
                return ResourceManager.GetString("FaceAnalysisService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başarısız kalite kontrolü.
        /// </summary>
        public static string FailedQualityCheck {
            get {
                return ResourceManager.GetString("FailedQualityCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aile ve arkadaş ziyareti.
        /// </summary>
        public static string FamilyAndFriendVisit {
            get {
                return ResourceManager.GetString("FamilyAndFriendVisit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aile birleşimi amaçlı.
        /// </summary>
        public static string FamilyUnificationPurpose {
            get {
                return ResourceManager.GetString("FamilyUnificationPurpose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aile birleşimi.
        /// </summary>
        public static string FamilyUnion {
            get {
                return ResourceManager.GetString("FamilyUnion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string FatherName {
            get {
                return ResourceManager.GetString("FatherName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arızalı.
        /// </summary>
        public static string Faulty {
            get {
                return ResourceManager.GetString("Faulty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hatalı belgeler.
        /// </summary>
        public static string FaultyDocumentation {
            get {
                return ResourceManager.GetString("FaultyDocumentation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Şubat.
        /// </summary>
        public static string Febraury {
            get {
                return ResourceManager.GetString("Febraury", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kadın.
        /// </summary>
        public static string Female {
            get {
                return ResourceManager.GetString("Female", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Festival fuarı sergisi.
        /// </summary>
        public static string FestivalFairExhibition {
            get {
                return ResourceManager.GetString("FestivalFairExhibition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 15-25 yaş arası.
        /// </summary>
        public static string Fifteen_TwentyFive {
            get {
                return ResourceManager.GetString("Fifteen_TwentyFive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dosya yüklenemedi.
        /// </summary>
        public static string FileCouldNotBeUploaded {
            get {
                return ResourceManager.GetString("FileCouldNotBeUploaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru Sahibi İsteği İle Dosya Çekildi.
        /// </summary>
        public static string FileWithdrewAccordingtoCustomerRequest {
            get {
                return ResourceManager.GetString("FileWithdrewAccordingtoCustomerRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Belgesel amaçlı çekim.
        /// </summary>
        public static string FilmingDocumentaryPurpose {
            get {
                return ResourceManager.GetString("FilmingDocumentaryPurpose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finans ve bankacılık.
        /// </summary>
        public static string FinanceAndBanking {
            get {
                return ResourceManager.GetString("FinanceAndBanking", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parmak izi okuyucu.
        /// </summary>
        public static string FingerprintReader {
            get {
                return ResourceManager.GetString("FingerprintReader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Esnek Randevu.
        /// </summary>
        public static string FlexibleAppointment {
            get {
                return ResourceManager.GetString("FlexibleAppointment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uçak bileti.
        /// </summary>
        public static string FlightTicket {
            get {
                return ResourceManager.GetString("FlightTicket", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GW servis ücreti alınmayan.
        /// </summary>
        public static string FreeGWServiceFee {
            get {
                return ResourceManager.GetString("FreeGWServiceFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vize servis ücreti alınmayan.
        /// </summary>
        public static string FreeVisaFee {
            get {
                return ResourceManager.GetString("FreeVisaFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Navlun vizesi.
        /// </summary>
        public static string FreightVisa {
            get {
                return ResourceManager.GetString("FreightVisa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cuma.
        /// </summary>
        public static string Friday {
            get {
                return ResourceManager.GetString("Friday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Garanti.
        /// </summary>
        public static string Garanti {
            get {
                return ResourceManager.GetString("Garanti", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Gender {
            get {
                return ResourceManager.GetString("Gender", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Memur.
        /// </summary>
        public static string Government {
            get {
                return ResourceManager.GetString("Government", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yeşil.
        /// </summary>
        public static string Green {
            get {
                return ResourceManager.GetString("Green", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasaport elçilikten elden teslim edildi.
        /// </summary>
        public static string HandDeliveredToApplicantAtEmbassy {
            get {
                return ResourceManager.GetString("HandDeliveredToApplicantAtEmbassy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru Sahibine Elden Teslim Edildi.
        /// </summary>
        public static string HandDeliveredToTheApplicant {
            get {
                return ResourceManager.GetString("HandDeliveredToTheApplicant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Having and inadmissible relative.
        /// </summary>
        public static string HavingAndInadmissibleRelative {
            get {
                return ResourceManager.GetString("HavingAndInadmissibleRelative", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sağlık.
        /// </summary>
        public static string Health {
            get {
                return ResourceManager.GetString("Health", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sağlık Alanı.
        /// </summary>
        public static string HealthGround {
            get {
                return ResourceManager.GetString("HealthGround", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sağlık Sigortası.
        /// </summary>
        public static string HealthInsurance {
            get {
                return ResourceManager.GetString("HealthInsurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sağlık / medikal.
        /// </summary>
        public static string HealthMedical {
            get {
                return ResourceManager.GetString("HealthMedical", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sağlık ve kamu güvenliği.
        /// </summary>
        public static string HealthPublicSafety {
            get {
                return ResourceManager.GetString("HealthPublicSafety", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kendisi.
        /// </summary>
        public static string HimselfHerself {
            get {
                return ResourceManager.GetString("HimselfHerself", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Evde Bakım Merkezi.
        /// </summary>
        public static string HomeHealthCare {
            get {
                return ResourceManager.GetString("HomeHealthCare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hastane.
        /// </summary>
        public static string Hospital {
            get {
                return ResourceManager.GetString("Hospital", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Şirket tarafından.
        /// </summary>
        public static string HostComapny {
            get {
                return ResourceManager.GetString("HostComapny", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başka biri tarafından.
        /// </summary>
        public static string HostPerson {
            get {
                return ResourceManager.GetString("HostPerson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Otel rezervasyonu.
        /// </summary>
        public static string HotelReservation {
            get {
                return ResourceManager.GetString("HotelReservation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eşi.
        /// </summary>
        public static string HusbandOrWife {
            get {
                return ResourceManager.GetString("HusbandOrWife", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Id.
        /// </summary>
        public static string Id {
            get {
                return ResourceManager.GetString("Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string IdentificationNumber {
            get {
                return ResourceManager.GetString("IdentificationNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ülkeye yasadışı giriş.
        /// </summary>
        public static string IllegalEntryCounrty {
            get {
                return ResourceManager.GetString("IllegalEntryCounrty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gün içerisinde tamamlanmayan/tamamlanamayan başvuru.
        /// </summary>
        public static string IncompletedApplication {
            get {
                return ResourceManager.GetString("IncompletedApplication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yanlış Başvuru Durumu Düzeltme Raporu.
        /// </summary>
        public static string IncorrectApplicationStatusReport {
            get {
                return ResourceManager.GetString("IncorrectApplicationStatusReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ICR’a yanlış giriş.
        /// </summary>
        public static string IncorrectEntryIcr {
            get {
                return ResourceManager.GetString("IncorrectEntryIcr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bireysel.
        /// </summary>
        public static string Individual {
            get {
                return ResourceManager.GetString("Individual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bilişim teknolojileri.
        /// </summary>
        public static string InformationTechnologies {
            get {
                return ResourceManager.GetString("InformationTechnologies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşleme alındı.
        /// </summary>
        public static string InProgress {
            get {
                return ResourceManager.GetString("InProgress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kalite kontrolde.
        /// </summary>
        public static string InQualityCheck {
            get {
                return ResourceManager.GetString("InQualityCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to INR.
        /// </summary>
        public static string INR {
            get {
                return ResourceManager.GetString("INR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sigorta.
        /// </summary>
        public static string Insurance {
            get {
                return ResourceManager.GetString("Insurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sigorta hatası.
        /// </summary>
        public static string InsuranceError {
            get {
                return ResourceManager.GetString("InsuranceError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Staj aiesec.
        /// </summary>
        public static string InternshipAisec {
            get {
                return ResourceManager.GetString("InternshipAisec", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Staj erasmusu.
        /// </summary>
        public static string IntershipErasmus {
            get {
                return ResourceManager.GetString("IntershipErasmus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Staj iaeste.
        /// </summary>
        public static string IntershipIaeste {
            get {
                return ResourceManager.GetString("IntershipIaeste", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Staj vizesi.
        /// </summary>
        public static string IntershipVisa {
            get {
                return ResourceManager.GetString("IntershipVisa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geçersiz işlem.
        /// </summary>
        public static string InvalidOperation {
            get {
                return ResourceManager.GetString("InvalidOperation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inventory type 1.
        /// </summary>
        public static string InventoryType1 {
            get {
                return ResourceManager.GetString("InventoryType1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inventory type 2.
        /// </summary>
        public static string InventoryType2 {
            get {
                return ResourceManager.GetString("InventoryType2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inventory type 3.
        /// </summary>
        public static string InventoryType3 {
            get {
                return ResourceManager.GetString("InventoryType3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Davetiye.
        /// </summary>
        public static string Invitation {
            get {
                return ResourceManager.GetString("Invitation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IQD.
        /// </summary>
        public static string IQD {
            get {
                return ResourceManager.GetString("IQD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Irak vatandaşı başvuru sayısı.
        /// </summary>
        public static string IraqCitizen {
            get {
                return ResourceManager.GetString("IraqCitizen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İstizan.
        /// </summary>
        public static string Istizan {
            get {
                return ResourceManager.GetString("Istizan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İstizan pasaport başvuru sahibine teslim edildi.
        /// </summary>
        public static string IstizanDelvieredToApplicant {
            get {
                return ResourceManager.GetString("IstizanDelvieredToApplicant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İstizan son karar için elçiliğe teslim edildi.
        /// </summary>
        public static string IstizanOutscanToEmbassy {
            get {
                return ResourceManager.GetString("IstizanOutscanToEmbassy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Istizan pasaport ofise teslim edildi.
        /// </summary>
        public static string IstizanOutscanToOffice {
            get {
                return ResourceManager.GetString("IstizanOutscanToOffice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Istızan ret.
        /// </summary>
        public static string IstizanRejection {
            get {
                return ResourceManager.GetString("IstizanRejection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ocak.
        /// </summary>
        public static string January {
            get {
                return ResourceManager.GetString("January", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Temmuz.
        /// </summary>
        public static string July {
            get {
                return ResourceManager.GetString("July", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Haziran.
        /// </summary>
        public static string June {
            get {
                return ResourceManager.GetString("June", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KWD.
        /// </summary>
        public static string KWD {
            get {
                return ResourceManager.GetString("KWD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Laboratuvar.
        /// </summary>
        public static string Laboratory {
            get {
                return ResourceManager.GetString("Laboratory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seyahat ve/veya sağlık sigortasının olmaması.
        /// </summary>
        public static string LackTravelHealthInsurance {
            get {
                return ResourceManager.GetString("LackTravelHealthInsurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kara.
        /// </summary>
        public static string Land {
            get {
                return ResourceManager.GetString("Land", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Son Yükleme Tarihi.
        /// </summary>
        public static string LastUploadDate {
            get {
                return ResourceManager.GetString("LastUploadDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hukuk uzmanı.
        /// </summary>
        public static string LegalProfessional {
            get {
                return ResourceManager.GetString("LegalProfessional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kabul mektubu.
        /// </summary>
        public static string LetterOfAcceptance {
            get {
                return ResourceManager.GetString("LetterOfAcceptance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Losing legal status.
        /// </summary>
        public static string LosingLegalStatus {
            get {
                return ResourceManager.GetString("LosingLegalStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LYD.
        /// </summary>
        public static string LYD {
            get {
                return ResourceManager.GetString("LYD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string MaidenName {
            get {
                return ResourceManager.GetString("MaidenName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ana başvuru.
        /// </summary>
        public static string MainApplicant {
            get {
                return ResourceManager.GetString("MainApplicant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Erkek.
        /// </summary>
        public static string Male {
            get {
                return ResourceManager.GetString("Male", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mart.
        /// </summary>
        public static string March {
            get {
                return ResourceManager.GetString("March", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Boşanmış.
        /// </summary>
        public static string MaritalStatusDivorced {
            get {
                return ResourceManager.GetString("MaritalStatusDivorced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Evli.
        /// </summary>
        public static string MaritalStatusMarried {
            get {
                return ResourceManager.GetString("MaritalStatusMarried", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bekar.
        /// </summary>
        public static string MaritalStatusSingle {
            get {
                return ResourceManager.GetString("MaritalStatusSingle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dul.
        /// </summary>
        public static string MaritalStatusWidowWidower {
            get {
                return ResourceManager.GetString("MaritalStatusWidowWidower", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mayıs.
        /// </summary>
        public static string May {
            get {
                return ResourceManager.GetString("May", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MBS.
        /// </summary>
        public static string MBS {
            get {
                return ResourceManager.GetString("MBS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Medikal Tıbbi Malzeme.
        /// </summary>
        public static string MedicalMaterial {
            get {
                return ResourceManager.GetString("MedicalMaterial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tıbbi tedavi amaçlı.
        /// </summary>
        public static string MedicalTreatmentPurposes {
            get {
                return ResourceManager.GetString("MedicalTreatmentPurposes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bakanlık Sağlık Başvurusu.
        /// </summary>
        public static string MinistryOfHealthApplication {
            get {
                return ResourceManager.GetString("MinistryOfHealthApplication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gerçeğin / bilginin yanlış beyanı.
        /// </summary>
        public static string MisrepresentationFactInformation {
            get {
                return ResourceManager.GetString("MisrepresentationFactInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yanlış beyan / yanlış bilgi.
        /// </summary>
        public static string MisrepresentationFalseInformation {
            get {
                return ResourceManager.GetString("MisrepresentationFalseInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eksik belgeler.
        /// </summary>
        public static string MissingDocuments {
            get {
                return ResourceManager.GetString("MissingDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eksik ya da geçersiz veri.
        /// </summary>
        public static string MissingOrInvalidData {
            get {
                return ResourceManager.GetString("MissingOrInvalidData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pazartesi.
        /// </summary>
        public static string Monday {
            get {
                return ResourceManager.GetString("Monday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Montaj.
        /// </summary>
        public static string Montage {
            get {
                return ResourceManager.GetString("Montage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Montaj ve onarım amaçlı.
        /// </summary>
        public static string MontageAndRepairmentPurposes {
            get {
                return ResourceManager.GetString("MontageAndRepairmentPurposes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 80 yaş üstü.
        /// </summary>
        public static string MoreThanEighty {
            get {
                return ResourceManager.GetString("MoreThanEighty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 50 ve üstü.
        /// </summary>
        public static string MoreThanFifty {
            get {
                return ResourceManager.GetString("MoreThanFifty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string MotherName {
            get {
                return ResourceManager.GetString("MotherName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mr.
        /// </summary>
        public static string Mr {
            get {
                return ResourceManager.GetString("Mr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mrs.
        /// </summary>
        public static string Mrs {
            get {
                return ResourceManager.GetString("Mrs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ms.
        /// </summary>
        public static string Ms {
            get {
                return ResourceManager.GetString("Ms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Çoklu.
        /// </summary>
        public static string Multiple {
            get {
                return ResourceManager.GetString("Multiple", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kendim.
        /// </summary>
        public static string Myself {
            get {
                return ResourceManager.GetString("Myself", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ad.
        /// </summary>
        public static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ad Soyad.
        /// </summary>
        public static string NameSurname {
            get {
                return ResourceManager.GetString("NameSurname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nansen pasaport.
        /// </summary>
        public static string NansenPassport {
            get {
                return ResourceManager.GetString("NansenPassport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Nationality {
            get {
                return ResourceManager.GetString("Nationality", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lacivert.
        /// </summary>
        public static string NavyBlue {
            get {
                return ResourceManager.GetString("NavyBlue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hayır.
        /// </summary>
        public static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru dışı sigorta.
        /// </summary>
        public static string NonApplicationInsurance {
            get {
                return ResourceManager.GetString("NonApplicationInsurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru dışı pcr.
        /// </summary>
        public static string NonApplicationPCR {
            get {
                return ResourceManager.GetString("NonApplicationPCR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Normal.
        /// </summary>
        public static string Normal {
            get {
                return ResourceManager.GetString("Normal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Normal Başvurular.
        /// </summary>
        public static string NormalApplications {
            get {
                return ResourceManager.GetString("NormalApplications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PCR normal.
        /// </summary>
        public static string NormalPCR {
            get {
                return ResourceManager.GetString("NormalPCR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alınmadı.
        /// </summary>
        public static string NotOrdered {
            get {
                return ResourceManager.GetString("NotOrdered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yüklenmedi.
        /// </summary>
        public static string NotUploaded {
            get {
                return ResourceManager.GetString("NotUploaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kasım.
        /// </summary>
        public static string November {
            get {
                return ResourceManager.GetString("November", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR.
        /// </summary>
        public static string NPR {
            get {
                return ResourceManager.GetString("NPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ekim.
        /// </summary>
        public static string October {
            get {
                return ResourceManager.GetString("October", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eksik Evraktan Ofiste Bekletme.
        /// </summary>
        public static string OfficeWaitingForMissingDocuments {
            get {
                return ResourceManager.GetString("OfficeWaitingForMissingDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rezmi vize.
        /// </summary>
        public static string OfficialVisa {
            get {
                return ResourceManager.GetString("OfficialVisa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resmi ziyaret.
        /// </summary>
        public static string OfficialVisit {
            get {
                return ResourceManager.GetString("OfficialVisit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beklemede.
        /// </summary>
        public static string OnHold {
            get {
                return ResourceManager.GetString("OnHold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlem başarılı.
        /// </summary>
        public static string OperationIsSuccessful {
            get {
                return ResourceManager.GetString("OperationIsSuccessful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Optik.
        /// </summary>
        public static string Optical {
            get {
                return ResourceManager.GetString("Optical", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Turuncu.
        /// </summary>
        public static string Orange {
            get {
                return ResourceManager.GetString("Orange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alındı.
        /// </summary>
        public static string Ordered {
            get {
                return ResourceManager.GetString("Ordered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Normal pasaport.
        /// </summary>
        public static string OrdinaryPassport {
            get {
                return ResourceManager.GetString("OrdinaryPassport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diğer.
        /// </summary>
        public static string Other {
            get {
                return ResourceManager.GetString("Other", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kargoya teslim edildi.
        /// </summary>
        public static string OutscanToCourrier {
            get {
                return ResourceManager.GetString("OutscanToCourrier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gecikmiş ziyaret.
        /// </summary>
        public static string OverstayedVisit {
            get {
                return ResourceManager.GetString("OverstayedVisit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasif.
        /// </summary>
        public static string Passive {
            get {
                return ResourceManager.GetString("Passive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasaport okunamadı.
        /// </summary>
        public static string PassportCouldNotBeRead {
            get {
                return ResourceManager.GetString("PassportCouldNotBeRead", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasaport teslim.
        /// </summary>
        public static string PassportDelivery {
            get {
                return ResourceManager.GetString("PassportDelivery", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasaport Numarası.
        /// </summary>
        public static string PassportNumber {
            get {
                return ResourceManager.GetString("PassportNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasaport fotokopisi.
        /// </summary>
        public static string PassportPhotocopy {
            get {
                return ResourceManager.GetString("PassportPhotocopy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hasta refakat.
        /// </summary>
        public static string PatientCompanion {
            get {
                return ResourceManager.GetString("PatientCompanion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ödeme onaylandı.
        /// </summary>
        public static string PaymentCompleted {
            get {
                return ResourceManager.GetString("PaymentCompleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ödeme bekleniyor.
        /// </summary>
        public static string PaymentWaiting {
            get {
                return ResourceManager.GetString("PaymentWaiting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kart ile öde.
        /// </summary>
        public static string PayWithCard {
            get {
                return ResourceManager.GetString("PayWithCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PCR.
        /// </summary>
        public static string PCR {
            get {
                return ResourceManager.GetString("PCR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PCR test iptal edildi.
        /// </summary>
        public static string PcrCanceled {
            get {
                return ResourceManager.GetString("PcrCanceled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PCR test yapıldı.
        /// </summary>
        public static string PcrIsDone {
            get {
                return ResourceManager.GetString("PcrIsDone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pcr test iptal edildi.
        /// </summary>
        public static string PCRTestCancelled {
            get {
                return ResourceManager.GetString("PCRTestCancelled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pcr test yapıldı.
        /// </summary>
        public static string PCRTestDone {
            get {
                return ResourceManager.GetString("PCRTestDone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beklemede.
        /// </summary>
        public static string Pending {
            get {
                return ResourceManager.GetString("Pending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Onay bekleyen.
        /// </summary>
        public static string PendingApproval {
            get {
                return ResourceManager.GetString("PendingApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Personel giriş hatası.
        /// </summary>
        public static string PersonalLoginError {
            get {
                return ResourceManager.GetString("PersonalLoginError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dilekçe.
        /// </summary>
        public static string Petition {
            get {
                return ResourceManager.GetString("Petition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eczane.
        /// </summary>
        public static string Pharmacy {
            get {
                return ResourceManager.GetString("Pharmacy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Telefon Numarası.
        /// </summary>
        public static string PhoneNumber {
            get {
                return ResourceManager.GetString("PhoneNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fizik Tedavi Merkezi.
        /// </summary>
        public static string PhysicalTherapyCenter {
            get {
                return ResourceManager.GetString("PhysicalTherapyCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pembe.
        /// </summary>
        public static string Pink {
            get {
                return ResourceManager.GetString("Pink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PKR.
        /// </summary>
        public static string PKR {
            get {
                return ResourceManager.GetString("PKR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Platin.
        /// </summary>
        public static string Platinum {
            get {
                return ResourceManager.GetString("Platinum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PNL Statüsü.
        /// </summary>
        public static string PnlStatus {
            get {
                return ResourceManager.GetString("PnlStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Poliklinik &amp; Tıp Merkezi.
        /// </summary>
        public static string Polyclinic {
            get {
                return ResourceManager.GetString("Polyclinic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ertelendi.
        /// </summary>
        public static string Postponed {
            get {
                return ResourceManager.GetString("Postponed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basın / medya.
        /// </summary>
        public static string PressMedia {
            get {
                return ResourceManager.GetString("PressMedia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prime time.
        /// </summary>
        public static string PrimeTime {
            get {
                return ResourceManager.GetString("PrimeTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PCR özel.
        /// </summary>
        public static string PrivatePCR {
            get {
                return ResourceManager.GetString("PrivatePCR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlem başlatılmadı.
        /// </summary>
        public static string ProcessNotStarted {
            get {
                return ResourceManager.GetString("ProcessNotStarted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profesyonel sporcu.
        /// </summary>
        public static string ProfessionalSportsperson {
            get {
                return ResourceManager.GetString("ProfessionalSportsperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alış.
        /// </summary>
        public static string Purchase {
            get {
                return ResourceManager.GetString("Purchase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mor.
        /// </summary>
        public static string Purple {
            get {
                return ResourceManager.GetString("Purple", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beklemeye alındı.
        /// </summary>
        public static string PutOnHold {
            get {
                return ResourceManager.GetString("PutOnHold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QAR.
        /// </summary>
        public static string QAR {
            get {
                return ResourceManager.GetString("QAR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tüm evraklar orijinal görülüp görülmedi kaşesi.
        /// </summary>
        public static string QCAllDocumentsAreOriginal {
            get {
                return ResourceManager.GetString("QCAllDocumentsAreOriginal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İzinli olduğu tarihler.
        /// </summary>
        public static string QCAllowedDates {
            get {
                return ResourceManager.GetString("QCAllowedDates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuran isim-soyisim ve pasaport no doğru yazılmalı.
        /// </summary>
        public static string QCApplicantNameSurnameAndPassportValidity {
            get {
                return ResourceManager.GetString("QCApplicantNameSurnameAndPassportValidity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkiye için ya da dünya geneli düzenlenmiş olmalı.
        /// </summary>
        public static string QCArrangedForTurkeyOrWorldwide {
            get {
                return ResourceManager.GetString("QCArrangedForTurkeyOrWorldwide", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arka plan beyaz.
        /// </summary>
        public static string QCBackgroundWhite {
            get {
                return ResourceManager.GetString("QCBackgroundWhite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Banka hesap dökümü.
        /// </summary>
        public static string QCBankAccountStatementControl {
            get {
                return ResourceManager.GetString("QCBankAccountStatementControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bakılıyor ise bakiyesi.
        /// </summary>
        public static string QCBankBalance {
            get {
                return ResourceManager.GetString("QCBankBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orijinal, ıslak imza-kaşeli.
        /// </summary>
        public static string QCBankDocumentValidity {
            get {
                return ResourceManager.GetString("QCBankDocumentValidity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru sahibinin adı-sayadı tam ve net yazıyor.
        /// </summary>
        public static string QCBusinessInvitationApplicantNameSurname {
            get {
                return ResourceManager.GetString("QCBusinessInvitationApplicantNameSurname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İletişim bilgileri var ve davet mektubu okunaklı.
        /// </summary>
        public static string QCBusinessInvitationContactInformation {
            get {
                return ResourceManager.GetString("QCBusinessInvitationContactInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Davet eden şirket tarafından imzalı-kaşeli.
        /// </summary>
        public static string QCBusinessInvitationDocumentValidity {
            get {
                return ResourceManager.GetString("QCBusinessInvitationDocumentValidity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ticari davet mektubu.
        /// </summary>
        public static string QCBusinessInvitationLetterControl {
            get {
                return ResourceManager.GetString("QCBusinessInvitationLetterControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Davetiye güncel tarihli.
        /// </summary>
        public static string QCBusinessInvitationUpToDate {
            get {
                return ResourceManager.GetString("QCBusinessInvitationUpToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Var ise şirket evrakları.
        /// </summary>
        public static string QCCompanyDocuments {
            get {
                return ResourceManager.GetString("QCCompanyDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Muvaffakatname.
        /// </summary>
        public static string QCConsentLetterControl {
            get {
                return ResourceManager.GetString("QCConsentLetterControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Var, standartlara uygun, güncel, orijinal.
        /// </summary>
        public static string QCConsentLetterValidity {
            get {
                return ResourceManager.GetString("QCConsentLetterValidity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yok, velayet veya ölüm belgesi var.
        /// </summary>
        public static string QCCustodyOrDeathCertificate {
            get {
                return ResourceManager.GetString("QCCustodyOrDeathCertificate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data kontrol A-Z.
        /// </summary>
        public static string QCDataControlAtoZ {
            get {
                return ResourceManager.GetString("QCDataControlAtoZ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Demografik bilgiler.
        /// </summary>
        public static string QCDemographicInformation {
            get {
                return ResourceManager.GetString("QCDemographicInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gidiş tarihi.
        /// </summary>
        public static string QCDepartureDate {
            get {
                return ResourceManager.GetString("QCDepartureDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkiye&apos;deki bir şehir.
        /// </summary>
        public static string QCDestinationCityInTurkey {
            get {
                return ResourceManager.GetString("QCDestinationCityInTurkey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşveren yazısı.
        /// </summary>
        public static string QCEmployerLetterControl {
            get {
                return ResourceManager.GetString("QCEmployerLetterControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orijinal antetli kağıda, ıslak imza-kaşeli, güncel tarihli.
        /// </summary>
        public static string QCEmployerLetterValidty {
            get {
                return ResourceManager.GetString("QCEmployerLetterValidty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Var ise eski vize kontrolü, geçerli Türkiye vizesi olmaması ya da belli bir sürenin altında olması.
        /// </summary>
        public static string QCExistingTurkeyVisaStatus {
            get {
                return ResourceManager.GetString("QCExistingTurkeyVisaStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuran isim-soyisim.
        /// </summary>
        public static string QCFlightApplicantNameSurname {
            get {
                return ResourceManager.GetString("QCFlightApplicantNameSurname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transit veya başvurusu ise asıl seyahat edeceği ülke için uçak bileti ve vizesi.
        /// </summary>
        public static string QCFlightMainDirection {
            get {
                return ResourceManager.GetString("QCFlightMainDirection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gidiş-dönüş rezervasyon.
        /// </summary>
        public static string QCFlightRoundTripReservation {
            get {
                return ResourceManager.GetString("QCFlightRoundTripReservation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uçak bilet rezervasyonu.
        /// </summary>
        public static string QCFlightTicketReservationControl {
            get {
                return ResourceManager.GetString("QCFlightTicketReservationControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cenaze masraflarını karşılamalı.
        /// </summary>
        public static string QCFuneralIncluded {
            get {
                return ResourceManager.GetString("QCFuneralIncluded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru sahibinin adı-sayadı (refakatçilerin adı soyadı) tam ve net yazıyor.
        /// </summary>
        public static string QCHospitalInvitationApplicantNameSurname {
            get {
                return ResourceManager.GetString("QCHospitalInvitationApplicantNameSurname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İletişim bilgileri var ve davet mektubu okunaklı.
        /// </summary>
        public static string QCHospitalInvitationContactInformation {
            get {
                return ResourceManager.GetString("QCHospitalInvitationContactInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hastane tarafından imzalı-kaşeli.
        /// </summary>
        public static string QCHospitalInvitationDocumentValidity {
            get {
                return ResourceManager.GetString("QCHospitalInvitationDocumentValidity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hastane davet mektubu.
        /// </summary>
        public static string QCHospitalInvitationLetterControl {
            get {
                return ResourceManager.GetString("QCHospitalInvitationLetterControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Davetiye güncel tarihli.
        /// </summary>
        public static string QCHospitalInvitationUpToDate {
            get {
                return ResourceManager.GetString("QCHospitalInvitationUpToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuran isim-soyisim.
        /// </summary>
        public static string QCHotelApplicantNameSurname {
            get {
                return ResourceManager.GetString("QCHotelApplicantNameSurname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Otel rezervasyonu.
        /// </summary>
        public static string QCHotelReservationControl {
            get {
                return ResourceManager.GetString("QCHotelReservationControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkiye&apos;deki bir şehir.
        /// </summary>
        public static string QCHotelReservationInTurkey {
            get {
                return ResourceManager.GetString("QCHotelReservationInTurkey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gidiş-dönüş uçak bileti kapsayacak şekilde olmalı.
        /// </summary>
        public static string QCHotelValidityForRoundTripFlight {
            get {
                return ResourceManager.GetString("QCHotelValidityForRoundTripFlight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kimlik kontrolü.
        /// </summary>
        public static string QCIdCheck {
            get {
                return ResourceManager.GetString("QCIdCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hastalığına dair tüm raporlar.
        /// </summary>
        public static string QCIllnessReports {
            get {
                return ResourceManager.GetString("QCIllnessReports", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hitap edilen kurum.
        /// </summary>
        public static string QCInstitutionAddress {
            get {
                return ResourceManager.GetString("QCInstitutionAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to En az 30.000 € teminatı olmalı.
        /// </summary>
        public static string QCInsuranceMinumGuarantee {
            get {
                return ResourceManager.GetString("QCInsuranceMinumGuarantee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gidiş-dönüş uçak bileti kapsayacak şekilde olmalı.
        /// </summary>
        public static string QCInsuranceValidityForRoundTripFlight {
            get {
                return ResourceManager.GetString("QCInsuranceValidityForRoundTripFlight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Son 6 alık.
        /// </summary>
        public static string QCLastSixMonths {
            get {
                return ResourceManager.GetString("QCLastSixMonths", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Meslek ve maaş bilgisi.
        /// </summary>
        public static string QCOccupationAndSalaryInformation {
            get {
                return ResourceManager.GetString("QCOccupationAndSalaryInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasaport kontrol.
        /// </summary>
        public static string QCPassportControl {
            get {
                return ResourceManager.GetString("QCPassportControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to En az 2 adet boş sayfasının olması.
        /// </summary>
        public static string QCPassportHasBlankPages {
            get {
                return ResourceManager.GetString("QCPassportHasBlankPages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yıpranmamış, zarar görmemiş olması.
        /// </summary>
        public static string QCPassportNotDamaged {
            get {
                return ResourceManager.GetString("QCPassportNotDamaged", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasaporttaki kaşelerin kontrolü.
        /// </summary>
        public static string QCPassportStampValidity {
            get {
                return ResourceManager.GetString("QCPassportStampValidity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkiye&apos;ye gidiş tarihinden itibaren en az 6 ay geçerli.
        /// </summary>
        public static string QCPasswordExpireDateValidadtion {
            get {
                return ResourceManager.GetString("QCPasswordExpireDateValidadtion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İzin veren ebeveyn imzası.
        /// </summary>
        public static string QCPermissiveParentSigniture {
            get {
                return ResourceManager.GetString("QCPermissiveParentSigniture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fotoğraf kontrol.
        /// </summary>
        public static string QCPhotoCheck {
            get {
                return ResourceManager.GetString("QCPhotoCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Güncel tarihli.
        /// </summary>
        public static string QCPhotoCurrentDated {
            get {
                return ResourceManager.GetString("QCPhotoCurrentDated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İkamet kontrolü.
        /// </summary>
        public static string QCResidenceCheck {
            get {
                return ResourceManager.GetString("QCResidenceCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkiye&apos;ye gidiş tarihinden itibaren en az 6 ay geçerli.
        /// </summary>
        public static string QCResidenceDurationValidity {
            get {
                return ResourceManager.GetString("QCResidenceDurationValidity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru sahibinin adı-sayadı tam ve net yazıyor.
        /// </summary>
        public static string QCSchoolAcceptanceApplicantNameSurname {
            get {
                return ResourceManager.GetString("QCSchoolAcceptanceApplicantNameSurname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İletişim bilgileri var ve davet mektubu okunaklı.
        /// </summary>
        public static string QCSchoolAcceptanceContactInformation {
            get {
                return ResourceManager.GetString("QCSchoolAcceptanceContactInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Okul yetkilisi tarafından imzalı-kaşeli.
        /// </summary>
        public static string QCSchoolAcceptanceDocumentValidity {
            get {
                return ResourceManager.GetString("QCSchoolAcceptanceDocumentValidity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Okul kabul mektubu.
        /// </summary>
        public static string QCSchoolAcceptanceLetterControl {
            get {
                return ResourceManager.GetString("QCSchoolAcceptanceLetterControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kabul mektubu güncel tarihli.
        /// </summary>
        public static string QCSchoolAcceptanceLetterForCurrentDate {
            get {
                return ResourceManager.GetString("QCSchoolAcceptanceLetterForCurrentDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ödeme yapıldığına dair dekont ya da bursluluk belgesi.
        /// </summary>
        public static string QCSchoolAcceptancepaymentOrScolarship {
            get {
                return ResourceManager.GetString("QCSchoolAcceptancepaymentOrScolarship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bir önceki mezuniyet belgesi ve transcript.
        /// </summary>
        public static string QCSchoolAcceptanceTranscript {
            get {
                return ResourceManager.GetString("QCSchoolAcceptanceTranscript", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standartlara uygun.
        /// </summary>
        public static string QCStandardValidity {
            get {
                return ResourceManager.GetString("QCStandardValidity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fotoğraf ölçüsüne uygun.
        /// </summary>
        public static string QCSuitableForPhotoSize {
            get {
                return ResourceManager.GetString("QCSuitableForPhotoSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seyahat tarihi.
        /// </summary>
        public static string QCTravelDate {
            get {
                return ResourceManager.GetString("QCTravelDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seyahat sağlık sigortası (dışarıdan kabul edilme durumunda).
        /// </summary>
        public static string QCTravelHealthInsuranceControl {
            get {
                return ResourceManager.GetString("QCTravelHealthInsuranceControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birlikte seyahat; ebeveyn geçerli vize ve uçak bileti var.
        /// </summary>
        public static string QCTravelWithParent {
            get {
                return ResourceManager.GetString("QCTravelWithParent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vize türü.
        /// </summary>
        public static string QCVisaCategory {
            get {
                return ResourceManager.GetString("QCVisaCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vize tipi.
        /// </summary>
        public static string QCVisaType {
            get {
                return ResourceManager.GetString("QCVisaType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qr Alternatif Ödeme Yöntemi.
        /// </summary>
        public static string QrAlternativePaymentMethod {
            get {
                return ResourceManager.GetString("QrAlternativePaymentMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sıra yönetimi.
        /// </summary>
        public static string QueueMatic {
            get {
                return ResourceManager.GetString("QueueMatic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kota.
        /// </summary>
        public static string Quota {
            get {
                return ResourceManager.GetString("Quota", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Radyoloji.
        /// </summary>
        public static string Radiology {
            get {
                return ResourceManager.GetString("Radiology", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rebiyometri.
        /// </summary>
        public static string ReBiometry {
            get {
                return ResourceManager.GetString("ReBiometry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Elçilikte teslim alındı.
        /// </summary>
        public static string ReceivedAtEmbassy {
            get {
                return ResourceManager.GetString("ReceivedAtEmbassy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ana Vize Başvuru Merkezinde Teslim Alındı.
        /// </summary>
        public static string ReceivedAtMainVisaApplicationCenter {
            get {
                return ResourceManager.GetString("ReceivedAtMainVisaApplicationCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vac&apos;ta teslim alındı.
        /// </summary>
        public static string ReceivedAtVAC {
            get {
                return ResourceManager.GetString("ReceivedAtVAC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vize Merkezinde Teslim Alındı.
        /// </summary>
        public static string ReceivedAtVisaCenter {
            get {
                return ResourceManager.GetString("ReceivedAtVisaCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vac&apos;ta Teslim Alındı..
        /// </summary>
        public static string RecievedAtVacForKuwait {
            get {
                return ResourceManager.GetString("RecievedAtVacForKuwait", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vize merkezinde teslim alındı.
        /// </summary>
        public static string RecievedAtVisaCenter {
            get {
                return ResourceManager.GetString("RecievedAtVisaCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt bulunamadı.
        /// </summary>
        public static string RecordNotFound {
            get {
                return ResourceManager.GetString("RecordNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kırmızı.
        /// </summary>
        public static string Red {
            get {
                return ResourceManager.GetString("Red", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mülteci pasaport.
        /// </summary>
        public static string RefugeePassport {
            get {
                return ResourceManager.GetString("RefugeePassport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İade edildi.
        /// </summary>
        public static string Refund {
            get {
                return ResourceManager.GetString("Refund", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kısmi iade edilen.
        /// </summary>
        public static string Refunded {
            get {
                return ResourceManager.GetString("Refunded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RegistrationPlaceCity {
            get {
                return ResourceManager.GetString("RegistrationPlaceCity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string RegistrationPlaceCountry {
            get {
                return ResourceManager.GetString("RegistrationPlaceCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kendisi.
        /// </summary>
        public static string ReimbursementTypeHimselfHerself {
            get {
                return ResourceManager.GetString("ReimbursementTypeHimselfHerself", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mali evrak yok.
        /// </summary>
        public static string ReimbursementTypeNoFinancialDocument {
            get {
                return ResourceManager.GetString("ReimbursementTypeNoFinancialDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sponsor.
        /// </summary>
        public static string ReimbursementTypeSponsor {
            get {
                return ResourceManager.GetString("ReimbursementTypeSponsor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reddedildi.
        /// </summary>
        public static string Rejected {
            get {
                return ResourceManager.GetString("Rejected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Redli pasaport korgoya teslim edildi.
        /// </summary>
        public static string RejectedPassportDeliveredToCourier {
            get {
                return ResourceManager.GetString("RejectedPassportDeliveredToCourier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vize reddedildi.
        /// </summary>
        public static string Rejection {
            get {
                return ResourceManager.GetString("Rejection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vize ret iadesi yapıldı.
        /// </summary>
        public static string RejectionRefundDone {
            get {
                return ResourceManager.GetString("RejectionRefundDone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yurda giriş yasağı ile ret.
        /// </summary>
        public static string RejectionWithCountryEntryBanned {
            get {
                return ResourceManager.GetString("RejectionWithCountryEntryBanned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Din görevlisi.
        /// </summary>
        public static string ReligiousFunctionary {
            get {
                return ResourceManager.GetString("ReligiousFunctionary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bakımda.
        /// </summary>
        public static string Repairing {
            get {
                return ResourceManager.GetString("Repairing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Şube bazlı sigorta kasa raporu.
        /// </summary>
        public static string ReportAllBranchesDailyInsurance {
            get {
                return ResourceManager.GetString("ReportAllBranchesDailyInsurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tüm şubeler aylık sigorta raporu.
        /// </summary>
        public static string ReportAllBranchesInsurance {
            get {
                return ResourceManager.GetString("ReportAllBranchesInsurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tamamlanmış başvuruların iptal raporu.
        /// </summary>
        public static string ReportCancelCompletedApplications {
            get {
                return ResourceManager.GetString("ReportCancelCompletedApplications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kargo raporu.
        /// </summary>
        public static string ReportCargo {
            get {
                return ResourceManager.GetString("ReportCargo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kargo firma ödeme raporu.
        /// </summary>
        public static string ReportCargoCompanyPayment {
            get {
                return ResourceManager.GetString("ReportCargoCompanyPayment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kasa raporu 1.
        /// </summary>
        public static string ReportCashReport_1 {
            get {
                return ResourceManager.GetString("ReportCashReport_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kasa raporu 2.
        /// </summary>
        public static string ReportCashReport_2 {
            get {
                return ResourceManager.GetString("ReportCashReport_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Konsolosluk raporu.
        /// </summary>
        public static string ReportConsular {
            get {
                return ResourceManager.GetString("ReportConsular", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kargoya teslim edildi raporu.
        /// </summary>
        public static string ReportDeliveredToCargo {
            get {
                return ResourceManager.GetString("ReportDeliveredToCargo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ekstra ücretler raporu.
        /// </summary>
        public static string ReportExtraFees {
            get {
                return ResourceManager.GetString("ReportExtraFees", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sigorta iptal raporu.
        /// </summary>
        public static string ReportInsuranceCancellation {
            get {
                return ResourceManager.GetString("ReportInsuranceCancellation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sigorta ile ücret iade raporu.
        /// </summary>
        public static string ReportInsuranceCancellationForRefund {
            get {
                return ResourceManager.GetString("ReportInsuranceCancellationForRefund", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sigorta poliçe detay raporu.
        /// </summary>
        public static string ReportInsuranceDetail {
            get {
                return ResourceManager.GetString("ReportInsuranceDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru dışı sigorta raporu.
        /// </summary>
        public static string ReportNonApplicationInsurance {
            get {
                return ResourceManager.GetString("ReportNonApplicationInsurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eski kasa raporu-1(13.08.2022 öncesi).
        /// </summary>
        public static string ReportOldCashReport_1 {
            get {
                return ResourceManager.GetString("ReportOldCashReport_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eski kasa raporu-2(13.08.2022 öncesi).
        /// </summary>
        public static string ReportOldCashReport_2 {
            get {
                return ResourceManager.GetString("ReportOldCashReport_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PCR iptal raporu.
        /// </summary>
        public static string ReportPCRCancellation {
            get {
                return ResourceManager.GetString("ReportPCRCancellation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PCR firma ödeme raporu.
        /// </summary>
        public static string ReportPCRCompanyPayment {
            get {
                return ResourceManager.GetString("ReportPCRCompanyPayment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PCR genel raporu.
        /// </summary>
        public static string ReportPCRGeneral {
            get {
                return ResourceManager.GetString("ReportPCRGeneral", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PCR ödeme raporu.
        /// </summary>
        public static string ReportPCRPayment {
            get {
                return ResourceManager.GetString("ReportPCRPayment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QMS raporu.
        /// </summary>
        public static string ReportQMS {
            get {
                return ResourceManager.GetString("ReportQMS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QMS personel raporu.
        /// </summary>
        public static string ReportQMSPersonal {
            get {
                return ResourceManager.GetString("ReportQMSPersonal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QMS zaman raporu.
        /// </summary>
        public static string ReportQMSTimeline {
            get {
                return ResourceManager.GetString("ReportQMSTimeline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ret iade raporu.
        /// </summary>
        public static string ReportRejectionStatus {
            get {
                return ResourceManager.GetString("ReportRejectionStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru raporu.
        /// </summary>
        public static string ReportTypeAllApplications {
            get {
                return ResourceManager.GetString("ReportTypeAllApplications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bütün personel başvuru raporu.
        /// </summary>
        public static string ReportTypeAllStaffApplicationsByBranch {
            get {
                return ResourceManager.GetString("ReportTypeAllStaffApplicationsByBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sigorta iptal raporu.
        /// </summary>
        public static string ReportTypeCancelledInsurance {
            get {
                return ResourceManager.GetString("ReportTypeCancelledInsurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Günlük pasaport raporu.
        /// </summary>
        public static string ReportTypeDailyBalance {
            get {
                return ResourceManager.GetString("ReportTypeDailyBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Silinen başvuru raporu.
        /// </summary>
        public static string ReportTypeDeletedApplications {
            get {
                return ResourceManager.GetString("ReportTypeDeletedApplications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Detay raporu.
        /// </summary>
        public static string ReportTypeDetail {
            get {
                return ResourceManager.GetString("ReportTypeDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ücretsiz başvuru raporu.
        /// </summary>
        public static string ReportTypeFreeApplications {
            get {
                return ResourceManager.GetString("ReportTypeFreeApplications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sistemde geçerli sigorta raporu.
        /// </summary>
        public static string ReportTypeInsurance {
            get {
                return ResourceManager.GetString("ReportTypeInsurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sigorta yapılan başvuru raporu.
        /// </summary>
        public static string ReportTypeInsuranceApplications {
            get {
                return ResourceManager.GetString("ReportTypeInsuranceApplications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kısmi iade raporu.
        /// </summary>
        public static string ReportTypePartiallyRefundedApplications {
            get {
                return ResourceManager.GetString("ReportTypePartiallyRefundedApplications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Günlük PCR test raporu.
        /// </summary>
        public static string ReportTypePcrDaily {
            get {
                return ResourceManager.GetString("ReportTypePcrDaily", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PCR durum raporu.
        /// </summary>
        public static string ReportTypePcrStatus {
            get {
                return ResourceManager.GetString("ReportTypePcrStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Photobooth raporu.
        /// </summary>
        public static string ReportTypePhotobooth {
            get {
                return ResourceManager.GetString("ReportTypePhotobooth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kasa raporu.
        /// </summary>
        public static string ReportTypeSafe {
            get {
                return ResourceManager.GetString("ReportTypeSafe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scan cycle raporu.
        /// </summary>
        public static string ReportTypeScanCycle {
            get {
                return ResourceManager.GetString("ReportTypeScanCycle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Personel bazlı başvuru raporu + VAS.
        /// </summary>
        public static string ReportTypeStaffExtraFeeSales {
            get {
                return ResourceManager.GetString("ReportTypeStaffExtraFeeSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vize ret raporu.
        /// </summary>
        public static string ReportVisaRejection {
            get {
                return ResourceManager.GetString("ReportVisaRejection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkiye cumhuriyeti göç idaresi genel müdürlüğü.
        /// </summary>
        public static string RepublicOfTurkeyDirectorateGeneralOfMigrationManagement {
            get {
                return ResourceManager.GetString("RepublicOfTurkeyDirectorateGeneralOfMigrationManagement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkiye cumhuriyeti göç idaresi genel müdürlüğü.
        /// </summary>
        public static string RepublicTurkeyDirectorateGeneralMigrationManagement {
            get {
                return ResourceManager.GetString("RepublicTurkeyDirectorateGeneralMigrationManagement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkiye Cumhuriyeti Gümrük ve Ticaret Bakanlığı.
        /// </summary>
        public static string RepublicTurkeyMinistryCustomsTrade {
            get {
                return ResourceManager.GetString("RepublicTurkeyMinistryCustomsTrade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkiye cumhuriyeti çalışma ve sosyal güvenlik bakanlığı.
        /// </summary>
        public static string RepublicTurkeyMinistryLaborSocialSecurity {
            get {
                return ResourceManager.GetString("RepublicTurkeyMinistryLaborSocialSecurity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkiye cumhuriyeti dışişleri bakanlığı.
        /// </summary>
        public static string RepublicTurkeyMinistryMinistryForeignAfffairs {
            get {
                return ResourceManager.GetString("RepublicTurkeyMinistryMinistryForeignAfffairs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Araştırmacı Bilim İnsanı.
        /// </summary>
        public static string ResearcherScientist {
            get {
                return ResourceManager.GetString("ResearcherScientist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oturma izni.
        /// </summary>
        public static string ResidencePermit {
            get {
                return ResourceManager.GetString("ResidencePermit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Emekli.
        /// </summary>
        public static string Retired {
            get {
                return ResourceManager.GetString("Retired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geçmişe dönük iptal.
        /// </summary>
        public static string RetrospectiveCancellation {
            get {
                return ResourceManager.GetString("RetrospectiveCancellation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revizyon.
        /// </summary>
        public static string Revision {
            get {
                return ResourceManager.GetString("Revision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Satış.
        /// </summary>
        public static string Sale {
            get {
                return ResourceManager.GetString("Sale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aynı gün iptal.
        /// </summary>
        public static string SameDayCancellation {
            get {
                return ResourceManager.GetString("SameDayCancellation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SAR.
        /// </summary>
        public static string SAR {
            get {
                return ResourceManager.GetString("SAR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cumartesi.
        /// </summary>
        public static string Saturday {
            get {
                return ResourceManager.GetString("Saturday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tarayıcı.
        /// </summary>
        public static string Scanner {
            get {
                return ResourceManager.GetString("Scanner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Planlanmış.
        /// </summary>
        public static string Scheduled {
            get {
                return ResourceManager.GetString("Scheduled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deniz.
        /// </summary>
        public static string Sea {
            get {
                return ResourceManager.GetString("Sea", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Denizci.
        /// </summary>
        public static string Seafarer {
            get {
                return ResourceManager.GetString("Seafarer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Denizci vizesi.
        /// </summary>
        public static string SeafarerVisa {
            get {
                return ResourceManager.GetString("SeafarerVisa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Güvenlik ihlali.
        /// </summary>
        public static string SecurityBreach {
            get {
                return ResourceManager.GetString("SecurityBreach", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Güvenlik sebebi.
        /// </summary>
        public static string SecurityReason {
            get {
                return ResourceManager.GetString("SecurityReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serbest meslek.
        /// </summary>
        public static string SelfEmployed {
            get {
                return ResourceManager.GetString("SelfEmployed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Son karar için elçiliğe gönderildi.
        /// </summary>
        public static string SendToEmbassyForFinalApproval {
            get {
                return ResourceManager.GetString("SendToEmbassyForFinalApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gönderildi.
        /// </summary>
        public static string Sent {
            get {
                return ResourceManager.GetString("Sent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eksik evrak nedeniyle başvuru merkezine gönderildi.
        /// </summary>
        public static string SentCenterDueToMissingDocuments {
            get {
                return ResourceManager.GetString("SentCenterDueToMissingDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Elçilikten Ana Vize Başvuru Merkezine Gönderildi.
        /// </summary>
        public static string SentFromEmbassyToMainVisaApplicationCenter {
            get {
                return ResourceManager.GetString("SentFromEmbassyToMainVisaApplicationCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ana Vize Başvuru Merkezinden Vize Başvuru Merkezine Gönderildi.
        /// </summary>
        public static string SentFromMainVisaApplicationCenterToVisaApplicationCenter {
            get {
                return ResourceManager.GetString("SentFromMainVisaApplicationCenterToVisaApplicationCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ana Vize Başvuru Merkezine Gönderildi.
        /// </summary>
        public static string SentToMainVisaApplicationCenter {
            get {
                return ResourceManager.GetString("SentToMainVisaApplicationCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eylül.
        /// </summary>
        public static string September {
            get {
                return ResourceManager.GetString("September", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hizmetçi.
        /// </summary>
        public static string Servant {
            get {
                return ResourceManager.GetString("Servant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Servis hatası.
        /// </summary>
        public static string ServerError {
            get {
                return ResourceManager.GetString("ServerError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Servis ücreti.
        /// </summary>
        public static string ServiceFee {
            get {
                return ResourceManager.GetString("ServiceFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hizmet pasaport.
        /// </summary>
        public static string ServicePassport {
            get {
                return ResourceManager.GetString("ServicePassport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hizmet sektörü.
        /// </summary>
        public static string ServiceSector {
            get {
                return ResourceManager.GetString("ServiceSector", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 71-75 yaş.
        /// </summary>
        public static string Seventy_SeventyFive {
            get {
                return ResourceManager.GetString("Seventy_SeventyFive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 76-80 yaş.
        /// </summary>
        public static string SeventyFive_Eighty {
            get {
                return ResourceManager.GetString("SeventyFive_Eighty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İmza pedi.
        /// </summary>
        public static string SignaturePad {
            get {
                return ResourceManager.GetString("SignaturePad", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tek.
        /// </summary>
        public static string Single {
            get {
                return ResourceManager.GetString("Single", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tek ziyaret.
        /// </summary>
        public static string SingleVisit {
            get {
                return ResourceManager.GetString("SingleVisit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 66-70 yaş.
        /// </summary>
        public static string SixtySix_Seventy {
            get {
                return ResourceManager.GetString("SixtySix_Seventy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kota.
        /// </summary>
        public static string Slot {
            get {
                return ResourceManager.GetString("Slot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slot Zamanı.
        /// </summary>
        public static string SlotTime {
            get {
                return ResourceManager.GetString("SlotTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Özel pasaport.
        /// </summary>
        public static string SpecialPassport {
            get {
                return ResourceManager.GetString("SpecialPassport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sportif.
        /// </summary>
        public static string Sportive {
            get {
                return ResourceManager.GetString("Sportive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sportif aktivite.
        /// </summary>
        public static string SportiveActivity {
            get {
                return ResourceManager.GetString("SportiveActivity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Damga vizesi.
        /// </summary>
        public static string StampVisa {
            get {
                return ResourceManager.GetString("StampVisa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Statü.
        /// </summary>
        public static string Status {
            get {
                return ResourceManager.GetString("Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Etiket vizesi.
        /// </summary>
        public static string StickerVisa {
            get {
                return ResourceManager.GetString("StickerVisa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Öğrenci.
        /// </summary>
        public static string Student {
            get {
                return ResourceManager.GetString("Student", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Öğrenci refakat.
        /// </summary>
        public static string StudentCompanion {
            get {
                return ResourceManager.GetString("StudentCompanion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Öğrenci / eğitim vize.
        /// </summary>
        public static string StudentEducationVisa {
            get {
                return ResourceManager.GetString("StudentEducationVisa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stajyer öğrenci.
        /// </summary>
        public static string StudentTrainee {
            get {
                return ResourceManager.GetString("StudentTrainee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pazar.
        /// </summary>
        public static string Sunday {
            get {
                return ResourceManager.GetString("Sunday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Muayenehane.
        /// </summary>
        public static string Surgery {
            get {
                return ResourceManager.GetString("Surgery", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Soyad.
        /// </summary>
        public static string Surname {
            get {
                return ResourceManager.GetString("Surname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sistem hatası.
        /// </summary>
        public static string SystemError {
            get {
                return ResourceManager.GetString("SystemError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Perşembe.
        /// </summary>
        public static string Thursday {
            get {
                return ResourceManager.GetString("Thursday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TMT.
        /// </summary>
        public static string TMT {
            get {
                return ResourceManager.GetString("TMT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Token oluşturulmadı.
        /// </summary>
        public static string TokenNotCreated {
            get {
                return ResourceManager.GetString("TokenNotCreated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Turizm.
        /// </summary>
        public static string Tourism {
            get {
                return ResourceManager.GetString("Tourism", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Turistik / İş.
        /// </summary>
        public static string TouristBusinessperson {
            get {
                return ResourceManager.GetString("TouristBusinessperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Turistik.
        /// </summary>
        public static string Touristic {
            get {
                return ResourceManager.GetString("Touristic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Turistik ziyaret.
        /// </summary>
        public static string TouristicVisit {
            get {
                return ResourceManager.GetString("TouristicVisit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tur operatör temsilcisi.
        /// </summary>
        public static string TourOperatorRepresentative {
            get {
                return ResourceManager.GetString("TourOperatorRepresentative", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tren.
        /// </summary>
        public static string Train {
            get {
                return ResourceManager.GetString("Train", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transit.
        /// </summary>
        public static string Transit {
            get {
                return ResourceManager.GetString("Transit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TRY.
        /// </summary>
        public static string TRY {
            get {
                return ResourceManager.GetString("TRY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salı.
        /// </summary>
        public static string Tuesday {
            get {
                return ResourceManager.GetString("Tuesday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkçe.
        /// </summary>
        public static string Turkish {
            get {
                return ResourceManager.GetString("Turkish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkiye sınır kapıları.
        /// </summary>
        public static string TurkishBorderGates {
            get {
                return ResourceManager.GetString("TurkishBorderGates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türkçe dil kurs amaçlı.
        /// </summary>
        public static string TurkishLanguageCoursePurpose {
            get {
                return ResourceManager.GetString("TurkishLanguageCoursePurpose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türk görevleri.
        /// </summary>
        public static string TurkishMissions {
            get {
                return ResourceManager.GetString("TurkishMissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Türk ulusal polisi.
        /// </summary>
        public static string TurkishNationalPolice {
            get {
                return ResourceManager.GetString("TurkishNationalPolice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 25-50 yaş arası.
        /// </summary>
        public static string TwentyFive_Fifty {
            get {
                return ResourceManager.GetString("TwentyFive_Fifty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru tipi değişen.
        /// </summary>
        public static string TypeChanged {
            get {
                return ResourceManager.GetString("TypeChanged", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşsiz.
        /// </summary>
        public static string Unemployed {
            get {
                return ResourceManager.GetString("Unemployed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unico.
        /// </summary>
        public static string Unico {
            get {
                return ResourceManager.GetString("Unico", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sigortasız başvuru raporu.
        /// </summary>
        public static string UnInsuredApplications {
            get {
                return ResourceManager.GetString("UnInsuredApplications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bilinmeyen.
        /// </summary>
        public static string Unknown {
            get {
                return ResourceManager.GetString("Unknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Belirtilmemiş.
        /// </summary>
        public static string Unspecified {
            get {
                return ResourceManager.GetString("Unspecified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yüklendi.
        /// </summary>
        public static string Uploaded {
            get {
                return ResourceManager.GetString("Uploaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to USD.
        /// </summary>
        public static string USD {
            get {
                return ResourceManager.GetString("USD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıldı.
        /// </summary>
        public static string Used {
            get {
                return ResourceManager.GetString("Used", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı.
        /// </summary>
        public static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 6458 sayılı yabancılar ve uluslararası koruma kanununun ihlali.
        /// </summary>
        public static string ViolationInternationalProtection {
            get {
                return ResourceManager.GetString("ViolationInternationalProtection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VIP.
        /// </summary>
        public static string VIP {
            get {
                return ResourceManager.GetString("VIP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vize.
        /// </summary>
        public static string Visa {
            get {
                return ResourceManager.GetString("Visa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vize Karar Raporu.
        /// </summary>
        public static string VisaDecisionReport {
            get {
                return ResourceManager.GetString("VisaDecisionReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Çoklu.
        /// </summary>
        public static string VisaEntryTypeMultiple {
            get {
                return ResourceManager.GetString("VisaEntryTypeMultiple", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tek.
        /// </summary>
        public static string VisaEntryTypeSingle {
            get {
                return ResourceManager.GetString("VisaEntryTypeSingle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vize transfer.
        /// </summary>
        public static string VisaTransfer {
            get {
                return ResourceManager.GetString("VisaTransfer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kuzey Kıbrıs Türk Cumhuriyeti ziyareti.
        /// </summary>
        public static string VisitToTurkishRepublicOfNorthernCyprus {
            get {
                return ResourceManager.GetString("VisitToTurkishRepublicOfNorthernCyprus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teyit Bekleniyor.
        /// </summary>
        public static string WaitingApproval {
            get {
                return ResourceManager.GetString("WaitingApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eksik evraklar bekleniyor.
        /// </summary>
        public static string WaitingForAdditionalDocuments {
            get {
                return ResourceManager.GetString("WaitingForAdditionalDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teslim edilmeyi bekleyen.
        /// </summary>
        public static string WaitingForDelivery {
            get {
                return ResourceManager.GetString("WaitingForDelivery", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kalite kontrolü bekleniyor.
        /// </summary>
        public static string WaittingForQualityCheck {
            get {
                return ResourceManager.GetString("WaittingForQualityCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Verilerin tamamlanması bekleniyor.
        /// </summary>
        public static string WaittingToBeCompletedData {
            get {
                return ResourceManager.GetString("WaittingToBeCompletedData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kalite kontrole gönderilmeyi bekliyor.
        /// </summary>
        public static string WaittingToBeSentToQualityCheck {
            get {
                return ResourceManager.GetString("WaittingToBeSentToQualityCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Randevusuz.
        /// </summary>
        public static string WalkIn {
            get {
                return ResourceManager.GetString("WalkIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Çarşamba.
        /// </summary>
        public static string Wednesday {
            get {
                return ResourceManager.GetString("Wednesday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beyaz.
        /// </summary>
        public static string White {
            get {
                return ResourceManager.GetString("White", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eş.
        /// </summary>
        public static string Wife {
            get {
                return ResourceManager.GetString("Wife", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru sahibi tarafından geri çekme.
        /// </summary>
        public static string WithdrawalByApplicant {
            get {
                return ResourceManager.GetString("WithdrawalByApplicant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvurusuz.
        /// </summary>
        public static string WithoutReference {
            get {
                return ResourceManager.GetString("WithoutReference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvurulu.
        /// </summary>
        public static string WithReference {
            get {
                return ResourceManager.GetString("WithReference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Çalışma izni.
        /// </summary>
        public static string WorkPermit {
            get {
                return ResourceManager.GetString("WorkPermit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Çalışma izni refakat.
        /// </summary>
        public static string WorkPermitCompanion {
            get {
                return ResourceManager.GetString("WorkPermitCompanion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Çalışma izni referans numarası size Mail olarak gönderildi.
        /// </summary>
        public static string WorkPermitReferanceNumberSendMail {
            get {
                return ResourceManager.GetString("WorkPermitReferanceNumberSendMail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Çalışma izni referans numarası size SMS olarak gönderildi.
        /// </summary>
        public static string WorkPermitReferanceNumberSendSms {
            get {
                return ResourceManager.GetString("WorkPermitReferanceNumberSendSms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Çalışma vize.
        /// </summary>
        public static string WorkVisa {
            get {
                return ResourceManager.GetString("WorkVisa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru numarası bu şubeye ait değildir.
        /// </summary>
        public static string WrongBranchNotification {
            get {
                return ResourceManager.GetString("WrongBranchNotification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sarı.
        /// </summary>
        public static string Yellow {
            get {
                return ResourceManager.GetString("Yellow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Evet.
        /// </summary>
        public static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 0-15 yaş arası.
        /// </summary>
        public static string Zero_Fifteen {
            get {
                return ResourceManager.GetString("Zero_Fifteen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 65 yaş ve altı.
        /// </summary>
        public static string Zero_SixtyFive {
            get {
                return ResourceManager.GetString("Zero_SixtyFive", resourceCulture);
            }
        }
    }
}
