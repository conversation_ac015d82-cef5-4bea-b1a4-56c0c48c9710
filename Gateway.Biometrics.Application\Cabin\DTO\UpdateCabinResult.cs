﻿using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.Cabin.DTO
{
    public class UpdateCabinResult : BaseServiceResult<UpdateCabinStatus>
    {
        public int Id { get; set; }
    }

    public enum UpdateCabinStatus
    {
        Successful,
        ResourceExists,
        InvalidInput,
        CabinNotFound,
        OfficeNotFound,
    }
}
