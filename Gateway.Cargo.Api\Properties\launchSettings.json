{"profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Gateway.Cargo.Api-Local": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "https://localhost:6001;http://localhost:6000", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Local"}}, "Gateway.Cargo.Api-Development": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "https://localhost:6001;http://localhost:6000"}, "WSL": {"commandName": "WSL2", "launchBrowser": true, "launchUrl": "https://localhost:6001/swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:6001;http://localhost:6000"}, "distributionName": ""}, "Gateway.Cargo.Api-Staging": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Staging"}, "applicationUrl": "https://localhost:6001;http://localhost:6000"}, "Gateway.Cargo.Api-Production": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production"}, "applicationUrl": "https://localhost:6001;http://localhost:6000"}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:27987", "sslPort": 44390}}}