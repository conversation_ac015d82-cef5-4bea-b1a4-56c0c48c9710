﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Gateway.Biometrics.Application.InventoryAttribute.DTO
{
    public class UpdateInventoryAttributeResult : BaseServiceResult<UpdateInventoryAttributeStatus>
    {
        public int Id { get; set; }
    }

    public enum UpdateInventoryAttributeStatus
    {
        Successful,
        ResourceExists,
        InvalidInput,
        InventoryAttributeNotFound,
    }
}
