﻿using System.Collections.Generic;

namespace Gateway.Biometrics.Api.Models
{
    public class BaseDataApiResponse<T>
    {
        public string Status { get; set; }
        public string Code { get; set; }
        public string Message { get; set; }
        public List<string> ValidationMessages { get; set; }
        public List<string> ErrorMessages { get; set; }
        public T Data { get; set; }
    }
}