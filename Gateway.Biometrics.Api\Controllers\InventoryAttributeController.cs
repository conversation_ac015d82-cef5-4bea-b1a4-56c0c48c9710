﻿using AutoMapper;
using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;
using Gateway.Biometrics.Api.Models.InventoryAttribute;
using Gateway.Biometrics.Application.InventoryAttribute;
using Gateway.Biometrics.Application.InventoryAttribute.DTO;
using Gateway.Extensions;

namespace Gateway.Biometrics.Api.Controllers
{
    [Authorize]
    [Route("api")]
    [ApiController]
    public class InventoryAttributeController : Controller
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private readonly IInventoryAttributeService _inventoryService;

        #region ctor

        public InventoryAttributeController(IContext context, IMapper mapper, IInventoryAttributeService inventoryService)
        {
            _context = context;
            _mapper = mapper;
            _inventoryService = inventoryService;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Creates a new inventory attribute
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Creates a new inventory attribute", 
            Description = "Creates a new inventory attribute")]
        [HttpPost]
        [Route("InventoryAttributes/create")]
        public async Task<IActionResult> CreateInventoryAttribute(CreateInventoryAttributeRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<CreateInventoryAttributeRequest>(request);
            serviceRequest.Context = _context;

            var result = await _inventoryService.CreateInventoryAttribute(serviceRequest);

            return InventoryAttributeResponseFactory.CreateInventoryAttributeResponse(result);
        }

        /// <summary>
        /// Delete an existing inventory attribute
        /// </summary>
        /// <param name="resourceId"></param>  
        [SwaggerOperation(Summary = "Delete an existing inventory attribute", 
            Description = "Delete an existing inventory attribute")]
        [HttpDelete]
        [Route("InventoryAttributes/delete/{resourceId?}")]
        public async Task<IActionResult> DeleteInventoryAttribute(int resourceId)
        {
            if (!resourceId.IsNumericAndGreaterThenZero())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = new DeleteInventoryAttributeRequest
            {
                InventoryAttributeId = resourceId,
                Context = _context
            };

            var result = await _inventoryService.DeleteInventoryAttribute(serviceRequest);

            return InventoryAttributeResponseFactory.DeleteInventoryAttributeResponse(result);
        }



        /// <summary>
        /// Get paginated list of inventory attributes
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Get paginated list of InventoryAttributes", 
            Description = "Get paginated list of InventoryAttributes")]
        [HttpPost]
        [Route("InventoryAttributes/search")]
        public async Task<IActionResult> GetPaginatedInventoryAttributes(GetPaginatedInventoryAttributesRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetPaginatedInventoryAttributesRequest>(request);
            serviceRequest.Context = _context;

            var result = await _inventoryService.GetPaginatedInventoryAttributes(serviceRequest);

            return InventoryAttributeResponseFactory.GetPaginatedInventoryAttributesResponse(result);
        }

        /// <summary>
        /// Update selected inventory attribute
        /// </summary>
        /// <param name="resourceId"></param>  
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Update an existing inventory attribute",
            Description = "Update an existed inventory attribute")]
        [HttpPut]
        [Route("InventoryAttributes/update/{resourceId?}")]
        public async Task<IActionResult> UpdateInventoryAttribute(int resourceId, UpdateInventoryAttributeRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<UpdateInventoryAttributeRequest>(request);
            serviceRequest.InventoryAttributeId = resourceId;
            serviceRequest.Context = _context;

            var result = await _inventoryService.UpdateInventoryAttribute(serviceRequest);

            return InventoryAttributeResponseFactory.UpdateInventoryAttributeResponse(result);
        }

        #endregion
    }
}