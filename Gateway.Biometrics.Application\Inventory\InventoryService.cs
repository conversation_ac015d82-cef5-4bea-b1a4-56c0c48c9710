﻿using AutoMapper;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Application.Inventory.Validator;
using Gateway.Biometrics.Application.Lookup;
using Gateway.Biometrics.Entity.Entities.Inventory;
using Gateway.Biometrics.Persistence;
using Gateway.Biometrics.Resources;
using Gateway.Core.Pagination;
using Gateway.Extensions;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Inventory
{
    public class InventoryService : IInventoryService
    {
        private readonly BiometricsDbContext _dbContext;
        private readonly IValidationService _validationService;
        private IMapper _mapper;

        public InventoryService(IValidationService validationService, BiometricsDbContext dbContext, IMapper mapper)
        {
            _validationService = validationService;
            _dbContext = dbContext;
            _mapper = mapper;
        }

        public async Task<GetInventoryResult> GetInventory(GetInventoryRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetInventoryValidator), request);

            if (!validationResult.IsValid)
                return new GetInventoryResult
                {
                    Status = GetInventoryStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var existingInventory = await _dbContext.Inventory.Where(p => p.Id == request.ResourceId && !p.IsDeleted)
                .FirstOrDefaultAsync();

            if (existingInventory == null)
                return new GetInventoryResult
                {
                    Status = GetInventoryStatus.NotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };


            var inventoryDefinition = await _dbContext.InventoryDefinition
                .Where(n =>
                    n.Id == existingInventory.InventoryDefinitionId && !n.IsDeleted)
                .FirstOrDefaultAsync();


            if (inventoryDefinition == null)
                return new GetInventoryResult
                {
                    Status = GetInventoryStatus.InventoryDefinitionNotFound,
                    Message = ServiceResources.INVENTORY_DEFINITION_NOT_FOUND
                };

            existingInventory.IpCameras = existingInventory.IpCameras.Where(x => !x.IsDeleted).ToList();
            var inventoryGet = _mapper.Map<InventoryDto>(existingInventory);

            return new GetInventoryResult
            {
                Status = GetInventoryStatus.Successful,
                Message = ServiceResources.RESOURCE_FOUND,
                Inventory = inventoryGet
            };
        }


        public async Task<CreateInventoryResult> CreateInventory(CreateInventoryRequest request)
        {
            var validationResult = _validationService.Validate(typeof(CreateInventoryValidator), request);

            if (!validationResult.IsValid)
                return new CreateInventoryResult
                {
                    Status = CreateInventoryStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };



            var inventoryDefinitionCount = _dbContext.InventoryDefinition.Count(t => t.Id == request.InventoryDefinitionId);

            if (inventoryDefinitionCount > 1)
            {
                return new CreateInventoryResult
                {
                    Status = CreateInventoryStatus.InventoryDefinitionNotFound,
                    Message = ServiceResources.INVENTORY_DEFINITION_NOT_FOUND
                };
            }

            var newInventory = new Entity.Entities.Inventory.Inventory
            {
                InventoryDefinitionId = request.InventoryDefinitionId,
                SerialNumber = request.SerialNumber,
                Description = request.Description,
                Status = request.Status,
            };

            await _dbContext.Inventory.AddAsync(newInventory);

            await _dbContext.SaveChangesAsync();

            #region Ip cameras
            foreach (var ipCamera in request.IpCameras)
            {
                var newIpCamera = new InventoryIpCamera()
                {
                    InventoryId = newInventory.Id,
                    Name = ipCamera.Name,
                    CameraId = ipCamera.CameraId,
                    Description = ipCamera.Description,
                };

                await _dbContext.InventoryIpCamera.AddAsync(newIpCamera);
            }
            #endregion

            await _dbContext.SaveChangesAsync();

            return new CreateInventoryResult
            {
                Status = CreateInventoryStatus.Successful,
                Message = ServiceResources.RESOURCE_CREATED,
                Id = newInventory.Id
            };
        }

        public async Task<DeleteInventoryResult> DeleteInventory(DeleteInventoryRequest request)
        {
            var validationResult = _validationService.Validate(typeof(DeleteInventoryValidator), request);

            if (!validationResult.IsValid)
                return new DeleteInventoryResult
                {
                    Status = DeleteInventoryStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var inventoryExisting = await _dbContext.Inventory
                .Where(p => !p.IsDeleted && p.Id == request.InventoryId).FirstOrDefaultAsync();

            if (inventoryExisting == null)
                return new DeleteInventoryResult
                {
                    Status = DeleteInventoryStatus.ResourceNotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };

            inventoryExisting.DeletedAt = DateTime.Now;
            inventoryExisting.IsDeleted = true;

            _dbContext.Inventory.Update(inventoryExisting);


            #region Ip cameras
            foreach (var ipCamera in inventoryExisting.IpCameras)
            {
                ipCamera.IsDeleted = true;
                ipCamera.DeletedAt = DateTime.Now;
            }
            #endregion

            await _dbContext.SaveChangesAsync();

            return new DeleteInventoryResult
            {
                Status = DeleteInventoryStatus.Successful,
                Message = ServiceResources.RESOURCE_DELETED
            };
        }


        public async Task<GetPaginatedInventoriesResult> GetPaginatedInventories(GetPaginatedInventoriesRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetPaginatedInventoriesValidator), request);

            if (!validationResult.IsValid)
                return new GetPaginatedInventoriesResult
                {
                    Status = GetPaginatedInventoriesStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var queryInventories = _dbContext.Inventory
                .Where(p => !p.IsDeleted);

            if (request.FilterInventoryTypeId.HasValue)
            {
                var inventoryDefinitionIdsByInventoryType = _dbContext.InventoryDefinition
                    .Where(n => !n.IsDeleted && n.InventoryTypeId == request.FilterInventoryTypeId.Value).Select(n => n.Id)
                    .ToList();
                queryInventories = queryInventories.Where(n => inventoryDefinitionIdsByInventoryType.Contains(n.InventoryDefinitionId));
            }

            var listInventories = await queryInventories.ToListAsync();

            if (listInventories == null || listInventories.Count == 0)
                return new GetPaginatedInventoriesResult
                {
                    Status = GetPaginatedInventoriesStatus.ResourceNotFound,
                    Message = ServiceResources.INVENTORY_NOT_FOUND
                };

            var inventories = new GetPaginatedInventoriesResult
            {
                Inventories = listInventories.Select(n => _mapper.Map<InventoryDto>(n)).ToList()
            };

            var paginationResult = PagedResultsFactory.CreatePagedResult(
                inventories.Inventories.AsQueryable(), request.Pagination.PageNumber, request.Pagination.PageSize,
                request.Pagination.OrderBy, request.Pagination.Ascending);

            return paginationResult == null
                ? new GetPaginatedInventoriesResult
                {
                    Inventories = null,
                    Status = GetPaginatedInventoriesStatus.ResourceNotFound,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                }
                : new GetPaginatedInventoriesResult
                {
                    Inventories = paginationResult.Results,
                    Status = GetPaginatedInventoriesStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    TotalNumberOfPages = paginationResult.TotalNumberOfPages,
                    TotalNumberOfRecords = paginationResult.TotalNumberOfRecords,
                };
        }

        public async Task<UpdateInventoryResult> UpdateInventory(UpdateInventoryRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateInventoryValidator), request);

            if (!validationResult.IsValid)
                return new UpdateInventoryResult
                {
                    Status = UpdateInventoryStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var inventoryExisting = await _dbContext.Inventory
                .Where(q => q.Id == request.InventoryId).FirstOrDefaultAsync();

            if (inventoryExisting == null)
                return new UpdateInventoryResult
                {
                    Status = UpdateInventoryStatus.InventoryNotFound,
                    Message = ServiceResources.INVENTORY_NOT_FOUND,
                };


            var inventoryDefinitionCount = _dbContext.InventoryDefinition.Count(t => t.Id == request.InventoryDefinitionId);

            if (inventoryDefinitionCount > 1)
            {
                return new UpdateInventoryResult
                {
                    Status = UpdateInventoryStatus.InventoryDefinitionNotFound,
                    Message = ServiceResources.INVENTORY_DEFINITION_NOT_FOUND
                };
            }

            inventoryExisting.InventoryDefinitionId = request.InventoryDefinitionId;
            inventoryExisting.SerialNumber = request.SerialNumber;
            inventoryExisting.Description = request.Description;
            inventoryExisting.Status = request.Status;
            inventoryExisting.IsDeleted = false;
            inventoryExisting.UpdatedAt = DateTime.Now;

            _dbContext.Inventory.Update(inventoryExisting);

            #region Ip cameras
            foreach (var ipCamera in inventoryExisting.IpCameras)
            {
                ipCamera.IsDeleted = true;
                ipCamera.DeletedAt = DateTime.Now;
            }

            foreach (var inventoryIpCamera in request.IpCameras)
            {
                var newInventoryIpCamera = new InventoryIpCamera()
                {
                    InventoryId = inventoryExisting.Id,
                    Name = inventoryIpCamera.Name,
                    CameraId = inventoryIpCamera.CameraId,
                    Description = inventoryIpCamera.Description,
                };
                await _dbContext.InventoryIpCamera.AddAsync(newInventoryIpCamera);
            }
            #endregion

            await _dbContext.SaveChangesAsync();

            return new UpdateInventoryResult
            {
                Status = UpdateInventoryStatus.Successful,
                Message = ServiceResources.RESOURCE_UPDATED,
                Id = inventoryExisting.Id
            };
        }

        private static LookupValue GetLookupValue(int enumType, int? value)
        {
            return EnumExtensions.GetEnumAsDictionary(LookupTypeFactory.GetInstance(enumType))
                .Select(x => new LookupValue { Id = x.Key.ToString(), DisplayValue = x.Value })
                .FirstOrDefault(p => p.Id == value.ToString());
        }
    }
}