﻿using FluentValidation;
using Gateway.Cargo.Application.Status.Dto.Request;
using Gateway.Cargo.Resources;
using Gateway.Extensions;

namespace Gateway.Cargo.Application.Status.Validator
{
    internal class UpdateStatusRequestValidator : AbstractValidator<UpdateStatusRequest>
    {

        public UpdateStatusRequestValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.ApplicationId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INPUT_ERROR, nameof(item.ApplicationId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.StatusId.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.INPUT_ERROR, nameof(item.StatusId)));
            });
        }
    }
}
