﻿using System;
using System.Collections;
using System.Linq;
using System.Resources;
using System.Threading;

namespace Gateway.Biometrics.Resources
{
    public static class ResourceManagerExtensions
    {
        public static string GetResourceName(this ResourceManager resourceManager, string value, bool ignoreCase = false)
        {
            var comparisonType = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;

            var entry = resourceManager.GetResourceSet(Thread.CurrentThread.CurrentUICulture, true, true)
                .OfType<DictionaryEntry>()
                .FirstOrDefault(dictionaryEntry => dictionaryEntry.Value.ToString()
                    .Equals(value, comparisonType));

            return entry.Key == null ? string.Empty : entry.Key.ToString();
        }
    }

    public static class Resource
    {
        public static string GetKey(string value)
        {
            return ServiceResources.ResourceManager.GetResourceName(value);
        }
    }
}
