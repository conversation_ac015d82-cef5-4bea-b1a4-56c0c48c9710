﻿using System;
using System.Linq;
using FluentValidation;
using Gateway.Extensions;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Entity.Entities.Inventory;
using Gateway.Biometrics.Resources;
using Gateway.Validation;
using Gateway.Biometrics.Application.InventoryDefinition.DTO;
using Gateway.Biometrics.Application.Cabin.DTO;

namespace Gateway.Biometrics.Application.InventoryDefinition.Validator
{
    internal class GetInventoryDefinitionValidator : AbstractValidator<GetInventoryDefinitionRequest>
    {
        public GetInventoryDefinitionValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.ResourceId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.ResourceId)));
            });
        }
    }


    internal class CreateInventoryDefinitionValidator : AbstractValidator<CreateInventoryDefinitionRequest>
    {
        public CreateInventoryDefinitionValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.InventoryTypeId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.InventoryTypeId)));
            });
            
        }
    }

    internal class UpdateInventoryDefinitionValidator : AbstractValidator<UpdateInventoryDefinitionRequest>
    {
        public UpdateInventoryDefinitionValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.InventoryTypeId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.InventoryTypeId)));
            });
            
        }
    }
    internal class DeleteInventoryDefinitionValidator : AbstractValidator<DeleteInventoryDefinitionRequest>
    {
        public DeleteInventoryDefinitionValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.InventoryDefinitionId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.InventoryDefinitionId)));
            });
        }
    }

    internal class GetPaginatedInventoryDefinitionsValidator : AbstractValidator<GetPaginatedInventoryDefinitionsRequest>
    {
        public GetPaginatedInventoryDefinitionsValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (string.IsNullOrWhiteSpace(item.Pagination.OrderBy))
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Pagination.OrderBy)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageSize.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageSize)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageNumber.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageNumber)));
            });
        }
    }
}