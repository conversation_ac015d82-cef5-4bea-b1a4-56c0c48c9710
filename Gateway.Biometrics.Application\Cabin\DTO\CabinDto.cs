﻿using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Application.Office.DTO;
using System.Collections.Generic;

namespace Gateway.Biometrics.Application.Cabin.DTO
{
    public class CabinDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public int OfficeId { get; set; }
        public int Status { get; set; }
        public bool IsBiometricCabin { get; set; }
        public virtual OfficeDto Office { get; set; }
        public virtual List<InventoryDto> Inventories { get; set; } = new List<InventoryDto>();

        public virtual List<InventoryDto> UnAssignedInventories { get; set; } = new List<InventoryDto>();
    


    }
}
