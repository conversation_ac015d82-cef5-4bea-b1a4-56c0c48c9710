﻿using Gateway.Biometrics.Application.Innovatrics.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Innovatrics.Dis
{
    public interface IInnovatricsDisServiceProvider
    {
        void Initialize<TConfig>(TConfig config);
        Task<CreateFaceResult> Create(string base64Image);
        Task<FaceGlassesResult> Glasses(string faceId);
        Task<FaceQualityResult> Quality(string faceId);
        Task<DeleteFaceResult> Delete(string faceId);
    }
}
