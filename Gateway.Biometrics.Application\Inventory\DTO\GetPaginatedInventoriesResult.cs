﻿using System;
using System.Collections.Generic;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.Inventory.DTO
{
    public class GetPaginatedInventoriesResult : BasePaginationServiceListResult<GetPaginatedInventoriesStatus>
    {
        public IEnumerable<InventoryDto> Inventories { get; set; }
    }
    public enum GetPaginatedInventoriesStatus
    {
        Successful,
        InvalidInput,
        ResourceNotFound
    }
}
