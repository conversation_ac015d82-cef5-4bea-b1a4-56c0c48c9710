﻿using System.Collections.Generic;
using System;
using Gateway.Core.Pagination;
using System.ComponentModel.DataAnnotations;
using Gateway.Biometrics.Application;
using Gateway.Biometrics.Application.Inventory.DTO;

namespace Gateway.Biometrics.Api.Models.Cabin
{
    public class BaseCabinRequestModel
    {
        public string Name { get; set; }
        public int OfficeId { get; set; }
        public int Status { get; set; }
        public bool IsBiometricCabin { get; set; }
        public List<InventoryDto> Inventories { get; set; }

        public BaseCabinRequestModel()
        {
        }
    }
    
    public class GetCabinRequestModel : BaseServiceRequest
    {
        [Required]
        public int ResourceId { get; set; }
    }

    public class CreateCabinRequestModel : BaseCabinRequestModel
    {
    }

    public class UpdateCabinRequestModel : BaseCabinRequestModel
    {
    }
    
    public class DeleteCabinRequestModel : BaseServiceRequest
    {
        [Required]
        public int ResourceId { get; set; }
    }

    public class GetPaginatedCabinsRequestModel
    {
        public int? FilterCountryId { get; set; }
        public PaginationRequest Pagination { get; set; }
    }
}