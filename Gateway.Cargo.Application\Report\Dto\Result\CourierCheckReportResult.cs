﻿using Gateway.Core.Responses;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Gateway.Core.Responses;

namespace Gateway.Cargo.Application.Report.Dto.Result
{
    public sealed class CourierCheckReportResult : BaseServiceResult<GetCargoTrackReportStatus>
    {
        public List<BranchResult> Branches { get; set; }

        public sealed class BranchResult
        {
            public int TotalApplicationCount { get; set; }
            public int TotalCourierSales { get; set; }
            public int NonTotalCourierSales { get; set; }

            public int BranchId { get; set; }

            public IEnumerable<ApplicationResult> Applications { get; set; }

            public sealed class ApplicationResult
            {
                public int Id { get; set; }
                public string ApplicationId { get; set; }
                public int ApplicantTypeId { get; set; }
                public string PassportNumber { get; set; }
                public string ApplicantName { get; set; }
                public int? RelationalApplicationId { get; set; }
                public int BranchId { get; set; }
                public string ApplicantNationalityCode { get; set; }
                public DateTime ApplicantBirthDay { get; set; }
                public string ApplicantAddress { get; set; }
                public string ApplicantPhone1 { get; set; }
                public string ApplicantPhone2 { get; set; }
                public List<string> VisaNo { get; set; }
                public DateTimeOffset ApplicationDate { get; set; }
                public string DeliveredDate { get; set; }
                public string CargoType { get; set; }
                public decimal Price { get; set; }
                public int Quantity { get; set; }
                public int CurrencyId { get; set; }
                public string ProcessedBy { get; set; }
                public string AgencyName { get; set; }

                public CargoInfo Cargo { get; set; }
            }
        }
    }

    public sealed class CargoInfo
    {
        public string ExtraFeeFlag { get; set; }
        public int CurrencyId { get; set; }
        public decimal Price { get; set; }
    }
}
