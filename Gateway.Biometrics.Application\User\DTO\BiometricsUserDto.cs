﻿using Gateway.Biometrics.Application.ClientConfiguration.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.User.DTO
{
    public class BiometricsUserDto
    {
        public ClientConfigurationForHostDto ClientConfiguration { get; set; }
        public PortalUserDto PortalUser { get; set; }
        public string Token { get; set; }
    }
}
