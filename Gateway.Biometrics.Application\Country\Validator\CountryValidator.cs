﻿using FluentValidation;
using Gateway.Biometrics.Application.Country.DTO;
using Gateway.Biometrics.Resources;

namespace Gateway.Biometrics.Application.Country.Validator
{
    public static class CountryValidator
    {
        internal class GetCountriesValidator : AbstractValidator<GetCountriesRequest>
        {
            public GetCountriesValidator()
            {
                RuleFor(p => p).Custom((item, context) =>
                {
                    if (item.To && item.From)
                        context.AddFailure(ServiceResources.METHOD_REQUIREMENT_ERROR + $" ({nameof(item.To)}: true, {nameof(item.From)} : true)");
                });
            }
        }
    }
}
