﻿using AutoMapper;
using Gateway.Biometrics.Application.ClientConfiguration.DTO;
using Gateway.Biometrics.Application.Helpers;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Application.InventoryDefinition.DTO;
using Gateway.Biometrics.Application.Jwt;
using Gateway.Biometrics.Application.Plugin.DTO;
using Gateway.Biometrics.Application.User.DTO;
using Gateway.Biometrics.Application.User.Validator;
using Gateway.Biometrics.Persistence;
using Gateway.Biometrics.Resources;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Web;
using Microsoft.IdentityModel.Tokens;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Settings.AppSettings;
using Portal.Gateway.ExternalServices.Contracts;
using Portal.Gateway.ExternalServices.Models.Ldap;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.User
{
    public class UserService : IUserService
    {
        private readonly JwtOption _jwtSettings;
        private const string UserNameEncrypt = "userNameEncode";
        private const string PasswordEncrypt = "passwordEncode";

        private readonly LdapSettings _ldapSettings;
        private readonly ILdapService _ldapService;
        private readonly ExternalDbContext _externalDbContext;
        private readonly BiometricsDbContext _dbContext;
        private readonly IValidationService _validationService;
        private readonly IMapper _mapper;

        public UserService(
            IOptions<JwtOption> jwtSettings,
            IOptions<LdapSettings> ldapSettings,
            ILdapService ldapService,
            IValidationService validationService, 
            IMapper mapper, 
            ExternalDbContext externalDbContext, 
            BiometricsDbContext dbContext)
        {
            _jwtSettings = jwtSettings.Value;
            _ldapSettings = ldapSettings.Value;
            _ldapService = ldapService;
            _validationService = validationService;
            _externalDbContext = externalDbContext;
            _dbContext = dbContext;
            _mapper = mapper;
        }

        public async Task<PortalUserLoginResult> PortalUserLogin(PortalUserLoginRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UserValidator.PortalUserLoginValidator), request);
            if (!validationResult.IsValid)
                return new PortalUserLoginResult
                {
                    Status = PortalUserLoginStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            Entity.ExternalDbEntities.User user = null;
            try
            {
                user = await _externalDbContext.User.FirstOrDefaultAsync(n => n.Email == request.UserName && n.IsActive);                
            }
            catch (Exception ex)
            {
                return new PortalUserLoginResult
                {
                    Status = PortalUserLoginStatus.ResourceNotFound,
                    Message = ex.Message
                };
            }
            
            
            if (user == null || !CryptoHelper.VerifyPasswordHash(request.Password, user.Password))
            {
                return new PortalUserLoginResult
                {
                    Status = PortalUserLoginStatus.UserNotFound,
                    Message = ServiceResources.USER_NOT_FOUND
                };
            }

            var userBranchIds = _externalDbContext.UserBranch.Where(n => n.UserId == user.Id && n.IsActive && !n.IsDeleted).Select(n => n.BranchId).ToList();

            if (!userBranchIds.Any())
            {
                return new PortalUserLoginResult
                {
                    Status = PortalUserLoginStatus.UserHaveNotBranch,
                    Message = ServiceResources.USER_HAVE_NOT_BRANCH
                };
            }

            
            #region Client Configuration
            var clientConfiguration = await _dbContext.ClientConfiguration.FirstOrDefaultAsync(x =>
                x.HostName == request.HostName && x.IsDeleted == false);

            if (clientConfiguration == null)
            {
                return new PortalUserLoginResult
                {
                    Status = PortalUserLoginStatus.HostNameNotFound,
                    Message = ServiceResources.HOST_NAME_NOT_FOUND
                };
            }

            var clientConfigurationDto = _mapper.Map<ClientConfigurationForHostDto>(clientConfiguration);
            clientConfigurationDto.ConfigurationDate = clientConfiguration.Cabin.UpdatedAt != null ? clientConfiguration.Cabin.UpdatedAt.Value : clientConfiguration.Cabin.CreatedAt.Value;

            //WebApi tipinde inventory ekle
            clientConfigurationDto.Cabin.Inventories.Insert(0, new InventoryDto() { InventoryDefinition = new InventoryDefinitionDto() { InventoryTypeId = 0, InventoryType = new InventoryTypeDto() { Name = "WebApi" } } });

            var inventoryTypeIds = clientConfigurationDto.Cabin.Inventories.Select(n => n.InventoryDefinition.InventoryTypeId).ToList();

            clientConfigurationDto.Plugins = _mapper.Map<List<PluginDto>>(_dbContext.Plugin.Where(x => inventoryTypeIds.Contains(x.InventoryTypeId) && !x.IsDeleted).ToList());
            clientConfigurationDto.CountryId = clientConfigurationDto.Cabin.Office.CountryId;
            #endregion

            if (!userBranchIds.Contains(clientConfigurationDto.Cabin.Office.BranchId))
            {
                return new PortalUserLoginResult
                {
                    Status = PortalUserLoginStatus.BranchesDoNotMatch,
                    Message = ServiceResources.BRANCHES_DO_NOT_MATCH
                };
            }



            BiometricsUserDto biometricsUser = new BiometricsUserDto() {
                ClientConfiguration = clientConfigurationDto,
                PortalUser = _mapper.Map<PortalUserDto>(user),
                //Token = ""
                Token = GenerateJSONWebToken(user, request)
            };


            return new PortalUserLoginResult
            {
                Status = PortalUserLoginStatus.Successful,
                Message = ServiceResources.RESOURCE_RETRIEVED,
                User = biometricsUser
            };
        }


        public async Task<PortalUserLoginResult> LdapUserLogin(PortalUserLoginRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UserValidator.PortalUserLoginValidator), request);
            if (!validationResult.IsValid)
                return new PortalUserLoginResult
                {
                    Status = PortalUserLoginStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            Entity.ExternalDbEntities.User user = null;

            var ldapResponse = await _ldapService.AuthenticateLdapUser(request.UserName, request.Password);

            if (!ldapResponse.Success)
            {
                if (ldapResponse.ErrorCode == AuthenticateLdapUserResponse.ErrAccountLocked)
                {
                    return new PortalUserLoginResult
                    {
                        Status = PortalUserLoginStatus.UserNotFound,
                        Message = ServiceResources.LDAP_USER_ACCOUNT_LOCKED
                    };
                }
                else if (ldapResponse.ErrorCode == AuthenticateLdapUserResponse.ErrInvalidCredentials)
                {
                    return new PortalUserLoginResult
                    {
                        Status = PortalUserLoginStatus.UserNotFound,
                        Message = ServiceResources.LDAP_USER_INVALID_CREDENTIALS
                    };
                }
                else if (ldapResponse.ErrorCode == AuthenticateLdapUserResponse.ErrAccountExpired)
                {
                    return new PortalUserLoginResult
                    {
                        Status = PortalUserLoginStatus.UserNotFound,
                        Message = ServiceResources.LDAP_USER_ACCOUNT_EXPIRED
                    };
                }

                return new PortalUserLoginResult
                {
                    Status = PortalUserLoginStatus.UserNotFound,
                    Message = ServiceResources.LDAP_USER_ACCOUNT_NOT_FOUND
                };
            }

            try
            {
                user = await _externalDbContext.User.FirstOrDefaultAsync(n => n.Email == ldapResponse.Email && n.IsBiometricsDesktopUser && n.IsActive && !n.IsDeleted);                
            }
            catch (Exception ex)
            {
                return new PortalUserLoginResult
                {
                    Status = PortalUserLoginStatus.ResourceNotFound,
                    Message = ex.Message
                };
            }
            
            
            if (user == null)
            {
                return new PortalUserLoginResult
                {
                    Status = PortalUserLoginStatus.UserNotFound,
                    Message = ServiceResources.USER_NOT_FOUND
                };
            }

            var userBranchIds = _externalDbContext.UserBranch.Where(n => n.UserId == user.Id && n.IsActive && !n.IsDeleted).Select(n => n.BranchId).ToList();

            if (!userBranchIds.Any())
            {
                return new PortalUserLoginResult
                {
                    Status = PortalUserLoginStatus.UserHaveNotBranch,
                    Message = ServiceResources.USER_HAVE_NOT_BRANCH
                };
            }

            Entity.Entities.ClientConfiguration.ClientConfiguration clientConfiguration = null;
            #region Client Configuration
            try
            {
                clientConfiguration = await _dbContext.ClientConfiguration.FirstOrDefaultAsync(x =>
                x.HostName == request.HostName && x.IsDeleted == false);
            }
            catch (Exception ex)
            {
                return new PortalUserLoginResult
                {
                    Status = PortalUserLoginStatus.HostNameNotFound,
                    Message = ex.Message
                };
            }
            

            if (clientConfiguration == null)
            {
                return new PortalUserLoginResult
                {
                    Status = PortalUserLoginStatus.HostNameNotFound,
                    Message = ServiceResources.HOST_NAME_NOT_FOUND
                };
            }

            var clientConfigurationDto = _mapper.Map<ClientConfigurationForHostDto>(clientConfiguration);
            clientConfigurationDto.ConfigurationDate = clientConfiguration.Cabin.UpdatedAt != null ? clientConfiguration.Cabin.UpdatedAt.Value : clientConfiguration.Cabin.CreatedAt.Value;

            //WebApi tipinde inventory ekle
            clientConfigurationDto.Cabin.Inventories.Insert(0, new InventoryDto() { InventoryDefinition = new InventoryDefinitionDto() { InventoryTypeId = 0, InventoryType = new InventoryTypeDto() { Name = "WebApi" } } });

            var inventoryTypeIds = clientConfigurationDto.Cabin.Inventories.Select(n => n.InventoryDefinition.InventoryTypeId).ToList();

            clientConfigurationDto.Plugins = _mapper.Map<List<PluginDto>>(_dbContext.Plugin.Where(x => inventoryTypeIds.Contains(x.InventoryTypeId) && !x.IsDeleted).ToList());
            clientConfigurationDto.CountryId = clientConfigurationDto.Cabin.Office.CountryId;
            #endregion

            if (!userBranchIds.Contains(clientConfigurationDto.Cabin.Office.BranchId))
            {
                return new PortalUserLoginResult
                {
                    Status = PortalUserLoginStatus.BranchesDoNotMatch,
                    Message = ServiceResources.BRANCHES_DO_NOT_MATCH
                };
            }



            BiometricsUserDto biometricsUser = new BiometricsUserDto() {
                ClientConfiguration = clientConfigurationDto,
                PortalUser = _mapper.Map<PortalUserDto>(user),                
                Token = GenerateJSONWebToken(user, request)
            };


            return new PortalUserLoginResult
            {
                Status = PortalUserLoginStatus.Successful,
                Message = ServiceResources.RESOURCE_RETRIEVED,
                User = biometricsUser
            };
        }

        private string GenerateJSONWebToken(Entity.ExternalDbEntities.User user, PortalUserLoginRequest userInfo)
        {
            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Key));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);
      
            string userNameEncrypt = InetCriptoHelper.EncryptMessage(user.Email);
            string passwordEncrypt = InetCriptoHelper.EncryptMessage(userInfo.Password);

            var claims = new List<Claim>()
            {
                new Claim(ClaimConstants.Sub, userInfo.UserName),
                new Claim(ClaimConstants.Name, user.Name + " " + user.Surname),           
                new Claim(UserNameEncrypt , userNameEncrypt),
                new Claim(PasswordEncrypt,passwordEncrypt),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
            };

            var token = new JwtSecurityToken(
                _jwtSettings.Issuer,
                _jwtSettings.Issuer,
                claims,
                expires: DateTime.Now.AddMinutes(_jwtSettings.ExpiresIn),
                signingCredentials: credentials);

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
    }
}
