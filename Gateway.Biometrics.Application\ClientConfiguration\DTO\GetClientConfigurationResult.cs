﻿using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.ClientConfiguration.DTO
{

    public class GetClientConfigurationResult : BaseServiceResult<GetClientConfigurationStatus>
    {
        public ClientConfigurationDto ClientConfiguration { get; set; }
    }

  
    public enum GetClientConfigurationStatus
    {
        Successful,
        InvalidInput,
        NotFound,
        CabinNotFound,
        OfficeNotFound,

    }
}
