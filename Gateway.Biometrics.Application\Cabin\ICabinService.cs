﻿using System.Threading.Tasks;
using Gateway.Biometrics.Application.Cabin.DTO;

namespace Gateway.Biometrics.Application.Cabin
{
    public interface ICabinService
    {
        Task<GetCabinResult> GetCabin(GetCabinRequest request);
        Task<CreateCabinResult> CreateCabin(CreateCabinRequest request);

        Task<UpdateCabinResult> UpdateCabin(UpdateCabinRequest request);
        
        Task<DeleteCabinResult> DeleteCabin(DeleteCabinRequest request);

        Task<GetPaginatedCabinsResult> GetPaginatedCabins(GetPaginatedCabinsRequest request);
    }
}