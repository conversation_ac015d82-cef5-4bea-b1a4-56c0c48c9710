﻿using Gateway.Biometrics.Application.Category.DTO;
using Gateway.Core.Pagination;
using Gateway.Validation;
using System.Collections.Generic;
using System;
using System.Linq;
using System.Threading.Tasks;
using Gateway.Biometrics.Persistence;
using Gateway.Biometrics.Application.Category.Validator;
using Gateway.Biometrics.Resources;
using Microsoft.EntityFrameworkCore;

namespace Gateway.Biometrics.Application.Category
{
    public class CategoryService : ICategoryService
    {
        private readonly BiometricsDbContext _dbContext;
        private readonly IValidationService _validationService;


        #region ctor

        public CategoryService(IValidationService validationService, BiometricsDbContext dbContext)
        {
            _validationService = validationService;
            _dbContext = dbContext;
        }
        #endregion

        #region Public Methods

        public async Task<CreateCategoryResult> CreateCategory(CreateCategoryRequest request)
        {
            var validationResult = _validationService.Validate(typeof(CreateCategoryValidator), request);

            if (!validationResult.IsValid)
                return new CreateCategoryResult
                {
                    Status = CreateCategoryStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var newCategory = new Entity.Entities.Category.GenericAttribute
            {
                Name = request.Name,
                ParentId = request.ParentId,
                Description = request.Description,
            };

            await _dbContext.GenericAttribute.AddAsync(newCategory);

            await _dbContext.SaveChangesAsync();

            return new CreateCategoryResult
            {
                Status = CreateCategoryStatus.Successful,
                Message = ServiceResources.RESOURCE_CREATED,
                Id = newCategory.Id
            };
        }

        public async Task<DeleteCategoryResult> DeleteCategory(DeleteCategoryRequest request)
        {
            var validationResult = _validationService.Validate(typeof(DeleteCategoryValidator), request);

            if (!validationResult.IsValid)
                return new DeleteCategoryResult
                {
                    Status = DeleteCategoryStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var category = await _dbContext.GenericAttribute
                .Where(p => !p.IsDeleted && p.Id == request.CategoryId).FirstOrDefaultAsync();

            if (category == null)
                return new DeleteCategoryResult
                {
                    Status = DeleteCategoryStatus.ResourceNotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };

            category.DeletedAt = DateTime.Now;
            category.IsDeleted = true;

            _dbContext.GenericAttribute.Update(category);

            await _dbContext.SaveChangesAsync();

            return new DeleteCategoryResult
            {
                Status = DeleteCategoryStatus.Successful,
                Message = ServiceResources.RESOURCE_DELETED
            };
        }

        public async Task<GetPaginatedCategoriesResult> GetPaginatedCategories(GetPaginatedCategoriesRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetPaginatedCategoriesValidator), request);

            if (!validationResult.IsValid)
                return new GetPaginatedCategoriesResult
                {
                    Status = GetPaginatedCategoriesStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var queryCategories = await _dbContext.GenericAttribute
                .Where(p => !p.IsDeleted && p.Id > 0).ToListAsync();

            if (queryCategories == null || queryCategories.Count == 0)
                return new GetPaginatedCategoriesResult
                {
                    Status = GetPaginatedCategoriesStatus.ResourceNotFound,
                    Message = ServiceResources.Category_NOT_FOUND
                };

            var categories = new GetPaginatedCategoriesResult
            {
                Categories = queryCategories.Select(c => new GenericAttributeDto
                {
                    Id = c.Id,
                    ParentId = c.ParentId,
                    Name = c.Name,
                    Description = c.Description,
                }).ToList()
            };

            var paginationResult = PagedResultsFactory.CreatePagedResult(
                categories.Categories.AsQueryable(), request.Pagination.PageNumber, request.Pagination.PageSize,
                request.Pagination.OrderBy, request.Pagination.Ascending);

            return paginationResult == null
                ? new GetPaginatedCategoriesResult
                {
                    Categories = null,
                    Status = GetPaginatedCategoriesStatus.ResourceNotFound,
                    Message = ServiceResources.INVALID_INPUT_ERROR
                }
                : new GetPaginatedCategoriesResult
                {
                    Categories = paginationResult.Results,
                    Status = GetPaginatedCategoriesStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED
                };
        }

        public async Task<UpdateCategoryResult> UpdateCategory(UpdateCategoryRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateCategoryValidator), request);

            if (!validationResult.IsValid)
                return new UpdateCategoryResult
                {
                    Status = UpdateCategoryStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var category = await _dbContext.GenericAttribute
                .Where(q => q.Id == request.CategoryId).FirstOrDefaultAsync();

            if (category == null)
                return new UpdateCategoryResult
                {
                    Status = UpdateCategoryStatus.CategoryNotFound,
                    Message = ServiceResources.Category_NOT_FOUND,
                };


            category.ParentId = request.ParentId;
            category.Name = request.Name;
            category.Description = request.Description;
            category.IsDeleted = false;
            category.UpdatedAt = DateTime.Now;

            _dbContext.GenericAttribute.Update(category);

            await _dbContext.SaveChangesAsync();

            return new UpdateCategoryResult
            {
                Status = UpdateCategoryStatus.Successful,
                Message = ServiceResources.RESOURCE_UPDATED,
                Id = category.Id
            };
        }
               

        public async Task<GetCategoriesByParentIdResult> GetCategoriesByParentId(GetCategoriesByParentIdRequest request)
        {
            if (request.ParentId < 1)
            {
                return new GetCategoriesByParentIdResult
                {
                    Status = GetCategoriesByParentIdStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = new List<string>() { string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(request.ParentId)) }
                };
            }

            var category = await _dbContext.GenericAttribute.Where(x => x.ParentId == request.ParentId && x.IsDeleted == false).ToListAsync();
            if (category == null || category.Count == 0)
            {
                return new GetCategoriesByParentIdResult
                {
                    Status = GetCategoriesByParentIdStatus.ResourceNotFound,
                    Message = ServiceResources.Category_NOT_FOUND
                };
            }

            var categories = category.Select(x => new GenericAttributeDto
            {
                Id = x.Id,
                ParentId = x.ParentId,
                Name = x.Name,
                Description = x.Description,
                Options = x.Options,
            }).ToList();

            return new GetCategoriesByParentIdResult
            {
                Categories = categories,
                Status = GetCategoriesByParentIdStatus.Successful,
                Message = ServiceResources.RESOURCE_RETRIEVED
            };
        }
        
        #endregion
    }
}
