﻿using AutoMapper;
using Gateway.External.Api.Factories.ResponseFactory;
using Gateway.External.Api.Models;
using Gateway.External.Api.Models.PhotoBooth;
using Gateway.External.Application.PhotoBooth;
using Gateway.External.Application.PhotoBooth.Dto;
using Gateway.External.Core.Context;
using Gateway.External.Resources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;

namespace Gateway.External.Api.Controllers
{
	[Authorize]
	[Route("api")]
	[ApiController]
	public class PhotoBoothController : Controller
	{
		private readonly IContext _context;
		private readonly IPhotoBoothService _photoBoothService;
		private readonly IMapper _mapper;

		public PhotoBoothController(IContext context, IPhotoBoothService photoBoothService, IMapper mapper)
		{
			_context = context;
			_photoBoothService = photoBoothService;
			_mapper = mapper;
		}

		[SwaggerOperation(Summary = "Update an existing application photobooth status",
			Description = "Update an existed application photobooth status")]
		[HttpPut]
		[Route("photobooths/UpdateApplicationPhotoBoothStatus")]
		public async Task<IActionResult> UpdateApplicationPhotoBoothStatus(PhotoBoothModel photoBoothModel)
		{
			if (photoBoothModel == null)
				return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
					Resource.GetKey(ServiceResources.INVALID_REQUEST),
					ServiceResources.INVALID_REQUEST));

			var serviceRequest = _mapper.Map<UpdateApplicationPhotoBoothStatusRequest>(photoBoothModel);
			serviceRequest.Context = _context;
			var result = await _photoBoothService.UpdateApplicationPhotoBoothStatus(serviceRequest);
			return BaseResponseFactory.CreateResponse(result, result?.Id);
		}
	}
}
