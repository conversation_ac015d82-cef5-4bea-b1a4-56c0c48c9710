﻿using System;
using Microsoft.AspNetCore.Http;

namespace Gateway.Cargo.Core.Context
{
    internal class IdentityContext : IIdentityContext
    {
        public int UserId { get; set; }
        public int BranchId { get; set; }

        public IdentityContext(HttpContext context)
        {
            UserId = Convert.ToInt32(context.Request.Headers["UserId"]);
            BranchId = int.TryParse(context.Request.Headers["BranchId"], out var branchId) ? branchId : 0;
        }
    }
}
