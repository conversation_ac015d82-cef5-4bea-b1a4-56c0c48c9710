﻿using FluentValidation;
using Gateway.Biometrics.Application.Appeal.DTO;
using Gateway.Biometrics.Resources;
using Gateway.Extensions;

namespace Gateway.Biometrics.Application.Appeal.Validator
{
    public class CreateAppealDetailValidator : AbstractValidator<BaseAppealDetailRequest>
    {
        public CreateAppealDetailValidator()
        {
            //RuleFor(p => p).Custom((item, context) =>
            //{
            //    if (item.Definition.IsNullOrWhitespace())
            //        context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Definition)));
            //});

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.InventoryId.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.InventoryId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Description.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Description)));
            }); 
        } 
    }
}
