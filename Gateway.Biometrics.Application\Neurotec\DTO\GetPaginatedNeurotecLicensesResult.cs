﻿using System;
using System.Collections.Generic;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.Neurotec.DTO
{
    public class GetPaginatedNeurotecLicensesResult : BasePaginationServiceListResult<GetPaginatedNeurotecLicensesStatus>
    {
        public IEnumerable<NeurotecLicenseDto> NeurotecLicenses { get; set; }
    }
}
public enum GetPaginatedNeurotecLicensesStatus
{
    Successful,
    InvalidInput,
    ResourceNotFound
}
