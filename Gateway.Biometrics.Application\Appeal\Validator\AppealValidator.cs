﻿using FluentValidation;
using Gateway.Biometrics.Application.Appeal.DTO;
using Gateway.Biometrics.Resources;
using Gateway.Extensions;

namespace Gateway.Biometrics.Application.Appeal.Validator
{
    public class CreateAppealValidator : AbstractValidator<CreateAppealRequest>
    {
        public CreateAppealValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.PassportNumber.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.PassportNumber)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.ReferenceNumber.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.ReferenceNumber)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Name.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Name)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Surname.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Surname)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Gender.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Gender)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.MotherName.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.MotherName)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.FatherName.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.FatherName)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.BirthDate.IsDateTime())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.BirthDate)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.AppealCountryId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.AppealCountryId)));
            });

            //RuleFor(p => p).Custom((item, context) =>
            //{
            //    if (!item.AppealCityId.IsNumericAndGreaterThenZero())
            //        context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.AppealCityId)));
            //});

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.AppealOfficeId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.AppealOfficeId)));
            });

            //RuleFor(p => p).Custom((item, context) =>
            //{
            //    if (!item.Status.IsNumericAndGreaterThenZero())
            //        context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Status)));
            //});

            RuleForEach(x => x.AppealDetails).SetValidator(new CreateAppealDetailValidator()); // test için
        }
    }

    public class InsertAppealWithMetaDataValidator : AbstractValidator<InsertAppealWithMetaDataRequest>
    {
        public InsertAppealWithMetaDataValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.PassportNumber.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.PassportNumber)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.ReferenceNumber.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.ReferenceNumber)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Name.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Name)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Surname.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Surname)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Gender.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Gender)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.MotherName.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.MotherName)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.FatherName.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.FatherName)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.BirthDate.IsDateTime())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.BirthDate)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.AppealCountryId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.AppealCountryId)));
            });

            //RuleFor(p => p).Custom((item, context) =>
            //{
            //    if (!item.AppealCityId.IsNumericAndGreaterThenZero())
            //        context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.AppealCityId)));
            //});

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.AppealOfficeId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.AppealOfficeId)));
            });

            //RuleFor(p => p).Custom((item, context) =>
            //{
            //    if (!item.Status.IsNumericAndGreaterThenZero())
            //        context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Status)));
            //});

            RuleForEach(x => x.AppealDetails).SetValidator(new CreateAppealDetailValidator()); // test için
        }
    }

    public class InsertAppealWithMetaDataFromOfflineValidator : AbstractValidator<InsertAppealWithMetaDataFromOfflineRequest>
    {
        public InsertAppealWithMetaDataFromOfflineValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.OfflineId.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.OfflineId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.PassportNumber.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.PassportNumber)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.ReferenceNumber.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.ReferenceNumber)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Name.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Name)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Surname.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Surname)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Gender.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Gender)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.MotherName.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.MotherName)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.FatherName.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.FatherName)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.BirthDate.IsDateTime())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.BirthDate)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.AppealCountryId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.AppealCountryId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.AppealOfficeId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.AppealOfficeId)));
            });


            RuleForEach(x => x.AppealDetails).SetValidator(new CreateAppealDetailValidator()); // test için
        }
    }


    public class InsertAppealWithMetaDataFastValidator : AbstractValidator<InsertAppealWithMetaDataFastRequest>
    {
        public InsertAppealWithMetaDataFastValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.PassportNumber.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.PassportNumber)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.ReferenceNumber.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.ReferenceNumber)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Name.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Name)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Surname.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Surname)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Gender.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Gender)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.MotherName.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.MotherName)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.FatherName.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.FatherName)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.BirthDate.IsDateTime())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.BirthDate)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.AppealCountryId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.AppealCountryId)));
            });

            //RuleFor(p => p).Custom((item, context) =>
            //{
            //    if (!item.AppealCityId.IsNumericAndGreaterThenZero())
            //        context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.AppealCityId)));
            //});

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.AppealOfficeId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.AppealOfficeId)));
            });

            //RuleFor(p => p).Custom((item, context) =>
            //{
            //    if (!item.Status.IsNumericAndGreaterThenZero())
            //        context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Status)));
            //});

            RuleForEach(x => x.AppealDetails).SetValidator(new CreateAppealDetailValidator()); // test için
        }
    }

 public class SaveAppealMetaDataFullValidator : AbstractValidator<SaveAppealMetaDataFullRequest>
    {
        public SaveAppealMetaDataFullValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Id <= 0)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Id)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.MetaDataSerialized.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.MetaDataSerialized)));
            });
          
        }
    }

    public class InsertAppealMetaDataValidator : AbstractValidator<InsertAppealMetaDataRequest>
    {
        public InsertAppealMetaDataValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.AppealId < 0)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.AppealId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            { 
                if (item.ParentId <= 0)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.ParentId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.MetaDataSerialized.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.MetaDataSerialized)));
            });

        }
    }


    public class GetAppealsByPassportAndCountryValidator : AbstractValidator<GetAppealsByPassportAndCountryRequest>
    {
        public GetAppealsByPassportAndCountryValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.PassportNumber.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.PassportNumber)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.CountryId <= 0)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.CountryId)));
            });

        }
    }

    public class GetAppealsByXmlValidator : AbstractValidator<GetAppealsByXmlRequest>
    {
        public GetAppealsByXmlValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.PassportNumber.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.PassportNumber)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Name.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Name)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Surname.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Surname)));
            }); 

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.PassportNumber.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.PassportNumber)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Gender <= 0)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Gender)));
            });

        }
    }

    public class GetFullAppealMetaDataValidator : AbstractValidator<GetFullAppealMetaDataRequest>
    {
        public GetFullAppealMetaDataValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.AppealMetaDataId.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.AppealMetaDataId)));
            });
           

        }
    }
}
