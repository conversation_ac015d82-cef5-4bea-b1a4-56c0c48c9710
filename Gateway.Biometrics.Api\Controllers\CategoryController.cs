﻿using System.Threading.Tasks;
using AutoMapper;
using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Resources;
using Gateway.Extensions;
using Gateway.Biometrics.Api.Models.Category;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using Gateway.Biometrics.Application.Category;
using Gateway.Biometrics.Application.Category.DTO;

namespace Gateway.Biometrics.Api.Controllers
{
    [Authorize]
    [Route("api")]
    [ApiController]
    public class CategoryController : Controller
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private readonly ICategoryService _categoryService;

        #region ctor

        public CategoryController(IContext context, IMapper mapper, ICategoryService categoryService)
        {
            _context = context;
            _mapper = mapper;
            _categoryService = categoryService;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Creates a new category
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Creates a new category",
            Description = "Creates a new category")]
        [HttpPost]
        [Route("categories")]
        public async Task<IActionResult> CreateCategory(CreateCategoryRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<CreateCategoryRequest>(request);
            serviceRequest.Context = _context;

            var result = await _categoryService.CreateCategory(serviceRequest);

            return CategoryResponseFactory.CreateCategoryResponse(result);
        }

        /// <summary>
        /// Delete an existing Category
        /// </summary>
        /// <param name="resourceId"></param>  
        [SwaggerOperation(Summary = "Delete an existing Category",
            Description = "Delete an existing Category")]
        [HttpDelete]
        [Route("categories/{resourceId?}")]
        public async Task<IActionResult> DeleteCategory(int resourceId)
        {
            if (!resourceId.IsNumericAndGreaterThenZero())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = new DeleteCategoryRequest
            {
                CategoryId = resourceId,
                Context = _context
            };

            var result = await _categoryService.DeleteCategory(serviceRequest);

            return CategoryResponseFactory.DeleteCategoryResponse(result);
        }

        /// <summary>
        /// Get paginated list of Categories
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Get paginated list of Categories",
            Description = "Get paginated list of Categories")]
        [HttpPost]
        [Route("categories/search")]
        public async Task<IActionResult> GetPaginatedCategories(GetPaginatedCategoriesRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetPaginatedCategoriesRequest>(request);
            serviceRequest.Context = _context;

            var result = await _categoryService.GetPaginatedCategories(serviceRequest);

            return CategoryResponseFactory.GetPaginatedCategoriesResponse(result);
        }

        /// <summary>
        /// Update selected Category
        /// </summary>
        /// <param name="resourceId"></param>  
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Update an existing Category",
            Description = "Update an existed Category")]
        [HttpPut]
        [Route("categories/{resourceId?}")]
        public async Task<IActionResult> UpdateCategory(int resourceId, UpdateCategoryRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<UpdateCategoryRequest>(request);
            serviceRequest.CategoryId = resourceId;
            serviceRequest.Context = _context;

            var result = await _categoryService.UpdateCategory(serviceRequest);

            return CategoryResponseFactory.UpdateCategoryResponse(result);
        }

        /// <summary>
        /// Gets up category information by parent id
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        [SwaggerOperation(Summary = "Gets up category information by parent id",
            Description = "Gets up category information by parent id")]
        [HttpGet]
        [Route("categories/{parentId}/getByParentId")]
        public async Task<IActionResult> GetWithParentId(int parentId)
        {
            if (parentId < 1)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = new GetCategoriesByParentIdRequest
            {
                ParentId = parentId,
                Context = _context
            };

            var result = await _categoryService.GetCategoriesByParentId(serviceRequest);

            return CategoryResponseFactory.GetCategoriesByParentId(result);

        }


        #endregion
    }
}