﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Gateway.Cargo.Entity.Entities.Application
{
    public class ApplicationCancellation
    {
        public ApplicationCancellation()
        {
            IsActive = true;
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public int ApplicationId { get; set; }

        [Required]
        public int CancellationTypeId { get; set; }

        [Required]
        public int CancellationReasonId { get; set; }

        [Required]
        public int CancellationStatusId { get; set; }

        [Required]
        public int CreatedBy { get; set; }

        [Required]
        public DateTimeOffset CreatedAt { get; set; }

        public int? UpdatedBy { get; set; }

        public DateTimeOffset? UpdatedAt { get; set; }

        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }

        public Application Application { get; set; }
    }
}
