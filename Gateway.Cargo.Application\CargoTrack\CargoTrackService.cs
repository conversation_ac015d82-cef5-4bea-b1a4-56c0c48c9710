﻿using Gateway.Cargo.Application.CargoTrack.Dto.Response;
using Gateway.Cargo.Dto.Request;
using Gateway.Cargo.Dto.Response;
using Gateway.Cargo.Persistence;
using Gateway.Cargo.Resources;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Gateway.Cargo.Application.CargoTrack.Dto.Request;

namespace Gateway.Cargo.Application.CargoTrack
{
    public class CargoTrackService : ICargoTrackService
    {
        private readonly CargoDbContext _dbContext;
        private static readonly ILogger Logger = Log.ForContext<CargoTrackService>();
        private IConfiguration Configuration { get; }

        public CargoTrackService(CargoDbContext dbContext, IConfiguration configuration)
        {
            _dbContext = dbContext;
            Configuration = configuration;
        }

        public async Task<CargoTrackingResultDto> Track(TrackingRequest request)
        {
            try
            {
                var cargoTrack = await _dbContext.CargoTrack.FirstOrDefaultAsync(p => p.ApplicationId == request.ApplicationId && p.IsActive && !p.IsDeleted);
                if (cargoTrack == null)
                    return new CargoTrackingResultDto
                    {
                        Status = GetTrackingStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    };

                var cargoBranch = await _dbContext.CargoBranch.FirstOrDefaultAsync(c => c.BranchId == cargoTrack.BranchId && c.IsActive && !c.IsDeleted);
                if (cargoBranch == null)
                    return new CargoTrackingResultDto
                    {
                        Status = GetTrackingStatus.InternalServerError,
                        Message = ServiceResources.BRANCH_NOT_FOUND
                    };

                var cargoService = CargoServiceProviderFactory.GetInstance(cargoBranch, Configuration);

                var result = await cargoService.Tracking(cargoService.GetType().Name.Equals("LastMileServiceProvider") ? 
                    cargoTrack.PackageTrackingNumber :
                    cargoTrack.CargoTransactionId, cargoTrack.PackageId);

                if (result is not { Status: GetTrackingStatus.Successful })
                    return new CargoTrackingResultDto
                    {
                        Status = result.Status,
                        Message = result.Message
                    };

                cargoTrack.CargoStatus = result.CargoTrackingResult?.LastStatus?.StatusCode;
                cargoTrack.Description = result.CargoTrackingResult?.LastStatus?.Description;
                cargoTrack.UpdatedBy = request.Context?.Identity?.UserId;
                cargoTrack.UserAgent = request.Context?.UserAgent;

                _dbContext.CargoTrack.Update(cargoTrack);
                await _dbContext.SaveChangesAsync();

                return new CargoTrackingResultDto
                {
                    Status = result.Status,
                    CargoTrackingResult = result.CargoTrackingResult
                };
            }
            catch (Exception ex)
            {
				Logger.Error(ex, ex.Message);

				return new CargoTrackingResultDto
				{
					Status = GetTrackingStatus.InternalServerError,
					Message = ServiceResources.INTERNAL_SERVER_ERROR
				};
			}
        }

        public async Task<GetListCargoTrackResult> ListCargoToCheckStatus(int? cargoProviderId)
        {
            var list = new List<CargoTrackDto>();

            if (cargoProviderId is (int)Enums.Enums.CargoProviderType.LastMile)
            {
                list = await _dbContext.CargoTrack
                    .Include(i => i.Application)
                    .ThenInclude(i => i.ApplicationStatusHistories)
                    .Where(c => c.IsActive && !c.IsDeleted && c.CargoProviderId == cargoProviderId &&
                                c.Description.Trim().ToLower().Equals("under processing") && c.ShipmentId != null &&
                                c.Application.ApplicationStatusHistories.Any(r => r.IsActive && !r.IsDeleted && r.ApplicationStatusId == 18))
                    .Select(s => new CargoTrackDto
                    {
                        ApplicationId = s.ApplicationId,
                        CargoStatus = s.CargoStatus,
                        CargoTransactionId = s.CargoTransactionId,
                        CreatedBy = s.CreatedBy,
                        CreatedDate = s.CreatedDate,
                        Id = s.Id,
                        UpdatedAt = s.UpdatedAt,
                        UpdatedBy = s.UpdatedBy
                    }).OrderByDescending(r => r.Id).ToListAsync();
            }
            else
            {
                list = await _dbContext.CargoTrack
                    .Where(c => c.CargoStatus != (byte)Enums.Enums.CargoTrackingStatus.Delivered && c.IsActive && !c.IsDeleted)
                    .Select(s => new CargoTrackDto
                    {
                        ApplicationId = s.ApplicationId,
                        CargoStatus = s.CargoStatus,
                        CargoTransactionId = s.CargoTransactionId,
                        CreatedBy = s.CreatedBy,
                        CreatedDate = s.CreatedDate,
                        Id = s.Id,
                        UpdatedAt = s.UpdatedAt,
                        UpdatedBy = s.UpdatedBy
                    }).OrderByDescending(r => r.Id).ToListAsync();
            }

            if (list is { Count: > 0 })
                return new GetListCargoTrackResult
                {
                    Status = GetTrackingStatus.Successful,
                    CargoTrackList = list
                };

            return new GetListCargoTrackResult
            {
                Status = GetTrackingStatus.NotFound
            };
        }

        public async Task<GetCargoAreaResult> GetArea(GetAreaRequest request)
        {
            var cargoBranch = await _dbContext.CargoBranch.FirstOrDefaultAsync(c => c.BranchId == request.Context.Identity.BranchId && c.IsActive && !c.IsDeleted);
            if (cargoBranch == null)
                return new GetCargoAreaResult
                {
                    Status = GetCargoAreaStatus.InternalServerError,
                    Message = ServiceResources.BRANCH_NOT_FOUND
                };

            var cargoService = CargoServiceProviderFactory.GetInstance(cargoBranch, Configuration);

            var result = await cargoService.GetArea(request.GovernorateId);

            if(result == null || !result.AreaList.Any())
                return new GetCargoAreaResult
                {
                    Status = GetCargoAreaStatus.NotFound
                };

            return new GetCargoAreaResult
            {
                Status = GetCargoAreaStatus.Successful,
                AreaList = result.AreaList.Select(r => new GetCargoAreaResultDto
                {
                    Id = r.Id,
                    Title = r.Title
                }).ToList()
            };
        }

        public async Task<GetCargoGovernorateResult> GetGovernorate(GetGovernorateRequest request)
        {
            var cargoBranch = await _dbContext.CargoBranch.FirstOrDefaultAsync(c => c.BranchId == request.Context.Identity.BranchId && c.IsActive && !c.IsDeleted);
            if (cargoBranch == null)
                return new GetCargoGovernorateResult
                {
                    Status = GetCargoGovernorateStatus.InternalServerError,
                    Message = ServiceResources.BRANCH_NOT_FOUND
                };

            var cargoService = CargoServiceProviderFactory.GetInstance(cargoBranch, Configuration);

            var result = await cargoService.GetGovernorate();

            if (result == null || !result.AreaList.Any())
                return new GetCargoGovernorateResult
                {
                    Status = GetCargoGovernorateStatus.NotFound
                };

            return new GetCargoGovernorateResult
            {
                Status = GetCargoGovernorateStatus.Successful,
                GovernorateList = result.AreaList.Select(r => new GetCargoGovernorateResultDto
                {
                    Id = r.Id,
                    Title = r.Title
                }).ToList()
            };
        }
    }
}
