trigger:
  branches:
    include:
      - development
      - staging
      - master

pool:
  name: LinuxPool

extends:
  template: /Infra/azure-pipelines-build-k8s-template-v2.yml
  ${{ if eq(variables['Build.SourceBranchName'], 'staging') }}:
    parameters:
      registry: harbor-sta
      repository: sta/gatewayportals-biometrics-api
      dockerfile: Gateway.Biometrics.Api/Dockerfile
      publishpath: Gateway.Biometrics.Api/Infra/kustomize
  ${{ elseif eq(variables['Build.SourceBranchName'], 'master') }}:
    parameters:
      registry: harbor-prod
      repository: prod/gatewayportals-biometrics-api
      dockerfile: Gateway.Biometrics.Api/Dockerfile
      publishpath: Gateway.Biometrics.Api/Infra/kustomize
  ${{ else }}:
    parameters:
      registry: harbor-dev
      repository: dev/gatewayportals-biometrics-api
      dockerfile: Gateway.Biometrics.Api/Dockerfile
      publishpath: Gateway.Biometrics.Api/Infra/kustomize
