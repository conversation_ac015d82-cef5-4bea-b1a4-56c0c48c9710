﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace Gateway.Biometrics.Entity.Entities.Inventory
{
    public class InventoryValueSet : BaseEntity
    {  
        public int InventoryDefinitionId { get; set; }
        public int InventoryAttributeId { get; set; }
        public string Value { get; set; }
        public virtual InventoryAttribute InventoryAttribute { get; set; }
    }
}
