﻿using Gateway.Cargo.Dto.Response;
using Gateway.Core.Responses;
using System;
using System.Collections.Generic;

namespace Gateway.Cargo.Application.CargoTrack.Dto.Response
{
    public class GetListCargoTrackResult : BaseServiceResult<GetTrackingStatus>
    {
        public List<CargoTrackDto> CargoTrackList { get; set; }
    }

    public class CargoTrackDto
    {
        public int Id { get; set; }
        public int ApplicationId { get; set; }
        public string CargoTransactionId { get; set; }
        public DateTimeOffset CreatedDate { get; set; }
        public DateTimeOffset? UpdatedAt { get; set; }
        public byte? CargoStatus { get; set; }
        public int CreatedBy { get; set; }
        public int? UpdatedBy { get; set; }
    }
}
