﻿using AutoMapper;
using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;
using Gateway.Biometrics.Api.Models.Inventory;
using Gateway.Biometrics.Application.Inventory;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Extensions;
using Gateway.Biometrics.Api.Models.Cabin;
using Gateway.Biometrics.Application.Cabin.DTO;

namespace Gateway.Biometrics.Api.Controllers
{
    //[Authorize]
    [Route("api")]
    [ApiController]
    public class InventoryController : Controller
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private readonly IInventoryService _inventoryService;

        #region ctor

        public InventoryController(IContext context, IMapper mapper, IInventoryService inventoryService)
        {
            _context = context;
            _mapper = mapper;
            _inventoryService = inventoryService;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Get Inventory by id
        /// </summary>
        /// <param name="resourceId"></param>
        [HttpGet]
        [Route("Inventories/get/{resourceId?}")]
        public async Task<IActionResult> GetInventory(int resourceId)
        {
            var serviceRequest = new GetInventoryRequest()
            {
                Context = _context,
                ResourceId = resourceId
            };

            var result = await _inventoryService.GetInventory(serviceRequest);

            return InventoryResponseFactory.GetInventoryResponse(result);
        }


        /// <summary>
        /// Creates a new inventory
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Creates a new inventory", 
            Description = "Creates a new inventory")]
        [HttpPost]
        [Route("Inventories/create")]
        public async Task<IActionResult> CreateInventory(CreateInventoryRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<CreateInventoryRequest>(request);
            serviceRequest.Context = _context;

            var result = await _inventoryService.CreateInventory(serviceRequest);

            return InventoryResponseFactory.CreateInventoryResponse(result);
        }

        /// <summary>
        /// Delete an existing inventory
        /// </summary>
        /// <param name="resourceId"></param>  
        [SwaggerOperation(Summary = "Delete an existing inventory", 
            Description = "Delete an existing inventory")]
        [HttpDelete]
        [Route("Inventories/delete/{resourceId?}")]
        public async Task<IActionResult> DeleteInventory(int resourceId)
        {
            if (!resourceId.IsNumericAndGreaterThenZero())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = new DeleteInventoryRequest
            {
                InventoryId = resourceId,
                Context = _context
            };

            var result = await _inventoryService.DeleteInventory(serviceRequest);

            return InventoryResponseFactory.DeleteInventoryResponse(result);
        }



        /// <summary>
        /// Get paginated list of inventories
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Get paginated list of Inventories", 
            Description = "Get paginated list of Inventories")]
        [HttpPost]
        [Route("Inventories/search")]
        public async Task<IActionResult> GetPaginatedInventories(GetPaginatedInventoriesRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetPaginatedInventoriesRequest>(request);
            serviceRequest.Context = _context;

            var result = await _inventoryService.GetPaginatedInventories(serviceRequest);

            return InventoryResponseFactory.GetPaginatedInventoriesResponse(result);
        }

        /// <summary>
        /// Update selected inventory
        /// </summary>
        /// <param name="resourceId"></param>  
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Update an existing inventory",
            Description = "Update an existed inventory")]
        [HttpPut]
        [Route("Inventories/update/{resourceId?}")]
        public async Task<IActionResult> UpdateInventory(int resourceId, UpdateInventoryRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<UpdateInventoryRequest>(request);
            serviceRequest.InventoryId = resourceId;
            serviceRequest.Context = _context;

            var result = await _inventoryService.UpdateInventory(serviceRequest);

            return InventoryResponseFactory.UpdateInventoryResponse(result);
        }

        #endregion
    }
}