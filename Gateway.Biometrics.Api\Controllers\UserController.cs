﻿using AutoMapper;
using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Api.Models.Supervisor;
using Gateway.Biometrics.Api.Models.User;
using Gateway.Biometrics.Application.Supervisor;
using Gateway.Biometrics.Application.Supervisor.DTO;
using Gateway.Biometrics.Application.User;
using Gateway.Biometrics.Application.User.DTO;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Api.Controllers
{
    [Authorize]
    [Route("api")]
    [ApiController]
    public class UserController : Controller
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private readonly IUserService _userService;

        #region ctor

        public UserController(IContext context, IMapper mapper, IUserService userService)
        {
            _context = context;
            _mapper = mapper;
            _userService = userService;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Perform portal user login
        /// </summary>
        /// <param name="passportNumber"></param>  
        [SwaggerOperation(Summary = "Perform portal user login",
            Description = "Perform portal user login")]
        [HttpPost]
        [AllowAnonymous]
        [Route("user/portallogin")]
        public async Task<IActionResult> PortalLogin(PortalUserLoginRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<PortalUserLoginRequest>(request);
            serviceRequest.Context = _context;

            var result = await _userService.PortalUserLogin(serviceRequest);

            return UserResponseFactory.PortalUserLoginResponse(result);
        }

        /// <summary>
        /// Perform portal user login
        /// </summary>
        /// <param name="passportNumber"></param>  
        [SwaggerOperation(Summary = "Perform Active directory user login",
            Description = "Perform Active directory user login")]
        [HttpPost]
        [AllowAnonymous]
        [Route("user/ldaplogin")]
        public async Task<IActionResult> LDapLogin(PortalUserLoginRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<PortalUserLoginRequest>(request);
            serviceRequest.Context = _context;

            var result = await _userService.LdapUserLogin(serviceRequest);

            return UserResponseFactory.PortalUserLoginResponse(result);
        }

        /// <summary>
        /// Check is server reachable
        /// </summary>        
        [SwaggerOperation(Summary = "Check is server reachable",
            Description = "Check is server reachable")]
        [HttpGet]
        [AllowAnonymous]
        [Route("user/isServerReachable")]
        public async Task<IActionResult> IsServerReachable()
        { 
            return UserResponseFactory.IsServerReachableResponce();
        }


        #endregion
    }
}
