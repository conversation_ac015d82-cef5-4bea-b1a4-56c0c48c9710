﻿using AutoMapper;
using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Api.Models.InventoryDefinition;
using Gateway.Biometrics.Application.InventoryDefinition;
using Gateway.Biometrics.Application.InventoryDefinition.DTO;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Resources;
using Gateway.Extensions;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Api.Controllers
{
    //[Authorize]
    [Route("api")]
    [ApiController]
    public class InventoryDefinitionController : Controller
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private readonly IInventoryDefinitionService _inventoryService;

        #region ctor

        public InventoryDefinitionController(IContext context, IMapper mapper, IInventoryDefinitionService inventoryService)
        {
            _context = context;
            _mapper = mapper;
            _inventoryService = inventoryService;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Get InventoryDefinition by id
        /// </summary>
        /// <param name="resourceId"></param>
        [HttpGet]
        [Route("InventoryDefinitions/get/{resourceId?}")]
        public async Task<IActionResult> GetInventoryDefinition(int resourceId)
        {
            var serviceRequest = new GetInventoryDefinitionRequest()
            {
                Context = _context,
                ResourceId = resourceId
            };

            var result = await _inventoryService.GetInventoryDefinition(serviceRequest);

            return InventoryDefinitionResponseFactory.GetInventoryDefinitionResponse(result);
        }

        /// <summary>
        /// Creates a new inventory Definition
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Creates a new inventory Definition", 
            Description = "Creates a new inventory")]
        [HttpPost]
        [Route("InventoryDefinitions/create")]
        public async Task<IActionResult> CreateInventoryDefinition(CreateInventoryDefinitionRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<CreateInventoryDefinitionRequest>(request);
            serviceRequest.Context = _context;

            var result = await _inventoryService.CreateInventoryDefinition(serviceRequest);

            return InventoryDefinitionResponseFactory.CreateInventoryDefinitionResponse(result);
        }

        /// <summary>
        /// Delete an existing inventory Definition
        /// </summary>
        /// <param name="resourceId"></param>  
        [SwaggerOperation(Summary = "Delete an existing inventory Definition", 
            Description = "Delete an existing inventory")]
        [HttpDelete]
        [Route("InventoryDefinitions/delete/{resourceId?}")]
        public async Task<IActionResult> DeleteInventoryDefinition(int resourceId)
        {
            if (!resourceId.IsNumericAndGreaterThenZero())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = new DeleteInventoryDefinitionRequest
            {
                InventoryDefinitionId = resourceId,
                Context = _context
            };

            var result = await _inventoryService.DeleteInventoryDefinition(serviceRequest);

            return InventoryDefinitionResponseFactory.DeleteInventoryDefinitionResponse(result);
        }



        /// <summary>
        /// Get paginated list of inventory Definitions
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Get paginated list of InventoryDefinitions", 
            Description = "Get paginated list of InventoryDefinitions")]
        [HttpPost]
        [Route("InventoryDefinitions/search")]
        public async Task<IActionResult> GetPaginatedInventoryDefinitions(GetPaginatedInventoryDefinitionsRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetPaginatedInventoryDefinitionsRequest>(request);
            serviceRequest.Context = _context;

            var result = await _inventoryService.GetPaginatedInventoryDefinitions(serviceRequest);

            return InventoryDefinitionResponseFactory.GetPaginatedInventoryDefinitionsResponse(result);
        }

        /// <summary>
        /// Update selected inventory Definition
        /// </summary>
        /// <param name="resourceId"></param>  
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Update an existing inventory definition",
            Description = "Update an existed inventory")]
        [HttpPut]
        [Route("InventoryDefinitions/update/{resourceId?}")]
        public async Task<IActionResult> UpdateInventoryDefinition(int resourceId, UpdateInventoryDefinitionRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<UpdateInventoryDefinitionRequest>(request);
            serviceRequest.InventoryDefinitionId = resourceId;
            serviceRequest.Context = _context;

            var result = await _inventoryService.UpdateInventoryDefinition(serviceRequest);

            return InventoryDefinitionResponseFactory.UpdateInventoryDefinitionResponse(result);
        }

        #endregion
    }
}