﻿using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.Neurotec.DTO
{
    public class UpdateNeurotecLicenseResult : BaseServiceResult<UpdateNeurotecLicenseStatus>
    {
        public int Id { get; set; }
    }

    public enum UpdateNeurotecLicenseStatus
    {
        Successful,
        ResourceExists,
        InvalidInput,
        NeurotecLicenseNotFound,        
        HostNameNotFound,        
    }
}
