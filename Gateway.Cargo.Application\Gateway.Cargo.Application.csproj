<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.4" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Gateway.Cargo.Core\Gateway.Cargo.Core.csproj" />
    <ProjectReference Include="..\Gateway.Cargo.FalconExpress\Gateway.Cargo.FalconExpress.csproj" />
    <ProjectReference Include="..\Gateway.Cargo.Persistence\Gateway.Cargo.Persistence.csproj" />
    <ProjectReference Include="..\Gateway.Cargo.Resources\Gateway.Cargo.Resources.csproj" />
    <ProjectReference Include="..\Gateway.Cargo\Gateway.Cargo.csproj" />
    <ProjectReference Include="..\Gateway.Core\Gateway.Core.csproj" />
    <ProjectReference Include="..\Gateway.Extensions\Gateway.Extensions.csproj" />
    <ProjectReference Include="..\Gateway.Logger.Core\Gateway.Logger.Core.csproj" />
    <ProjectReference Include="..\Gateway.Validation\Gateway.Validation.csproj" />
    <ProjectReference Include="..\Portal.Gateway.Common.Utility\Portal.Gateway.Common.Utility.csproj" />
    <ProjectReference Include="..\Portal.Gateway.Contracts\Portal.Gateway.Contracts.csproj" />
  </ItemGroup>
</Project>