﻿using System.ComponentModel.DataAnnotations;

namespace Gateway.Biometrics.Application.Category.Dto
{
    public class BaseCategoryRequest : BaseServiceRequest
    {
        [Required]
        public int? ParentId { get; set; }

        [StringLength(200)]
        [Required]
        public string Name { get; set; }

        [StringLength(200)]
        public string Description { get; set; }
    }
}

