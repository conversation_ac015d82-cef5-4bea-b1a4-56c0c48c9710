﻿using static Gateway.Cargo.LastMile.Dto.Result.LastMileTrackingResult.TrackingResult;

namespace Gateway.Cargo.LastMile.Dto.Result
{
	public class LastMileGetPackageResult : LastMileBaseServiceListResult<BaseLastMileStatus, LastMileGetPackageResult.GetPackageResult, LastMileBaseMetaResult>
	{
		public class GetPackageResult
		{
			public string Id { get; set; }
			public string TrackNumber { get; set; }
			public string StatusId { get; set; }
			public string AreaId { get; set; }
			public string TrackingNumberSegments { get; set; }
			public string ShipmentId { get; set; }
			public string RecipientPhoneNumber { get; set; }
			public string QrCodeImage { get; set; }
			public List<TrackingHistory> PackageTracking { get; set; }
			public string AreaName { get; set; }
			public string DockId { get; set; }
			public string Address { get; set; }
			public string Notes { get; set; }
			public string InternalStatus { get; set; }
			public string ExternalStatus { get; set; }
			public string CustomerId { get; set; }
			public string CustomerSignature { get; set; }
			public string PackageType { get; set; }

			public class TrackingHistory
			{
				public string Date { get; set; }
				public bool IsLatest { get; set; }
				public TrackingHistoryStatus Status { get; set; }
				public class TrackingHistoryStatus
				{
					public string Internal { get; set; }
					public string External { get; set; }
					public string Description { get; set; }
				}
			}
		}
	}
}
