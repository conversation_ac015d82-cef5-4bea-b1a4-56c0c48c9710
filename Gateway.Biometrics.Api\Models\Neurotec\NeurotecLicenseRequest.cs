﻿using System.Collections.Generic;
using System;
using Gateway.Core.Pagination;
using System.ComponentModel.DataAnnotations;
using Gateway.Biometrics.Application;
using Gateway.Biometrics.Application.Inventory.DTO;

namespace Gateway.Biometrics.Api.Models.Neurotec
{
    public class BaseNeurotecLicenseRequestModel
    {
        public string HostName { get; set; }

        public BaseNeurotecLicenseRequestModel()
        {
        }
    }

    public class GetNeurotecLicenseRequestModel : BaseServiceRequest
    {
        [Required]
        public int ResourceId { get; set; }
    }

    public class UpdateNeurotecLicenseRequestModel : BaseNeurotecLicenseRequestModel
    {

    }

    public class GetPaginatedNeurotecLicensesRequestModel
    {
        public PaginationRequest Pagination { get; set; }
    }
}