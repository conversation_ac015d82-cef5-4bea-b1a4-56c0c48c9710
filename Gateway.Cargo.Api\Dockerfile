# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
RUN apt-get update && apt-get install -y libgdiplus
RUN ln -s /lib/x86_64-linux-gnu/libdl.so.2 /usr/lib/libdl.so

# USER $APP_UID
USER root
WORKDIR /app
EXPOSE 8080

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
RUN apt-get update && apt-get install -y bash
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

# Restore as distinct layers
COPY ["Gateway.Cargo.Api/Gateway.Cargo.Api.csproj", "Gateway.Cargo.Api/"]
RUN dotnet restore "Gateway.Cargo.Api" --disable-build-servers --no-cache
COPY . .

# Build and publish a release
WORKDIR /src/Gateway.Cargo.Api
RUN mkdir -p "/app/publish"
RUN dotnet publish "./Gateway.Cargo.Api.csproj" --disable-build-servers -c $BUILD_CONFIGURATION -o "/app/publish" /p:UseAppHost=false

# Build runtime image
FROM base AS final
ENV TZ=Turkey
WORKDIR /app
COPY --from=build "/app/publish" .
ENTRYPOINT ["dotnet", "Gateway.Cargo.Api.dll"]