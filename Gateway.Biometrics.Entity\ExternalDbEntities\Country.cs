﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gateway.Biometrics.Entity.ExternalDbEntities
{
    public class Country
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string Name { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(5)]
        public string ISO2 { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(5)]
        public string ISO3 { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(5)]
        public string CallingCode { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string NameTr { get; set; }

        [Required]
        [Column(TypeName = "citext"), <PERSON><PERSON>ength(100)]
        public string NameAr { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string NameTm { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string NameRu { get; set; }


    }
}
