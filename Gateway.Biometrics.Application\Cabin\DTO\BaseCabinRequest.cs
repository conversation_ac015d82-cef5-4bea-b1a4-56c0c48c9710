﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Entity.Entities.Inventory;

namespace Gateway.Biometrics.Application.Cabin.DTO
{
    public class BaseCabinRequest : BaseServiceRequest
    {
        public int Id { get; set; }
        [Required]
        public string Name { get; set; }
        public int OfficeId { get; set; }
        public int Status { get; set; }
        public bool IsBiometricCabin { get; set; }
        public List<InventoryDto> Inventories { get; set; }

        public BaseCabinRequest()
        {

        }
    }


}

