﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Appeal.DTO
{
    public class AppealMetaDataDto
    {
        public int Id { get; set; }
        public int AppealId { get; set; }
        public int? ParentId { get; set; }
        public List<AppealMetaDataDto> Children { get; set; } = new List<AppealMetaDataDto>();
        public string MetaDataSerialized { get; set; }
    }
}
