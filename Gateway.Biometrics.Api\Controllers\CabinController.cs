﻿using AutoMapper;
using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;
using Gateway.Biometrics.Api.Models.Cabin;
using Gateway.Biometrics.Application.Cabin;
using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Extensions;

namespace Gateway.Biometrics.Api.Controllers
{
    //[Authorize]
    [Route("api")]
    [ApiController]
    public class CabinController : Controller
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private readonly ICabinService _cabinService;

        #region ctor

        public CabinController(IContext context, IMapper mapper, ICabinService cabinService)
        {
            _context = context;
            _mapper = mapper;
            _cabinService = cabinService;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Get cabin by id
        /// </summary>
        /// <param name="resourceId"></param>
        [HttpGet]
        [Route("Cabins/get/{resourceId?}")]
        public async Task<IActionResult> GetCabin(int resourceId)
        {
            var serviceRequest = new GetCabinRequest()
            {
                Context = _context,
                ResourceId = resourceId
            };

            var result = await _cabinService.GetCabin(serviceRequest);

            return CabinResponseFactory.GetCabinResponse(result);
        }


        /// <summary>
        /// Creates a new cabin
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Creates a new inventory Definition", 
            Description = "Creates a new inventory")]
        [HttpPost]
        [Route("Cabins/create")]
        public async Task<IActionResult> CreateCabin(CreateCabinRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<CreateCabinRequest>(request);
            serviceRequest.Context = _context;

            var result = await _cabinService.CreateCabin(serviceRequest);

            return CabinResponseFactory.CreateCabinResponse(result);
        }

        /// <summary>
        /// Delete an existing cabin
        /// </summary>
        /// <param name="resourceId"></param>  
        [SwaggerOperation(Summary = "Delete an existing inventory Definition", 
            Description = "Delete an existing inventory")]
        [HttpDelete]
        [Route("Cabins/delete/{resourceId?}")]
        public async Task<IActionResult> DeleteCabin(int resourceId)
        {
            if (!resourceId.IsNumericAndGreaterThenZero())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = new DeleteCabinRequest
            {
                CabinId = resourceId,
                Context = _context
            };

            var result = await _cabinService.DeleteCabin(serviceRequest);

            return CabinResponseFactory.DeleteCabinResponse(result);
        }



        /// <summary>
        /// Get paginated list of cabins
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Get paginated list of Cabins", 
            Description = "Get paginated list of Cabins")]
        [HttpPost]
        [Route("cabins/search")]
        public async Task<IActionResult> GetPaginatedCabins(GetPaginatedCabinsRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetPaginatedCabinsRequest>(request);
            serviceRequest.Context = _context;

            var result = await _cabinService.GetPaginatedCabins(serviceRequest);

            return CabinResponseFactory.GetPaginatedCabinsResponse(result);
        }

        /// <summary>
        /// Update selected cabin
        /// </summary>
        /// <param name="resourceId"></param>  
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Update an existing cabin",
            Description = "Update an existed cabin")]
        [HttpPut]
        [Route("Cabins/update/{resourceId?}")]
        public async Task<IActionResult> UpdateCabin(int resourceId, UpdateCabinRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<UpdateCabinRequest>(request);
            serviceRequest.CabinId = resourceId;
            serviceRequest.Id = resourceId;
            serviceRequest.Context = _context;

            var result = await _cabinService.UpdateCabin(serviceRequest);

            return CabinResponseFactory.UpdateCabinResponse(result);
        }

        #endregion
    }
}