﻿using FluentValidation;
using Gateway.Biometrics.Application.DemographicInformation.DTO;
using Gateway.Biometrics.Application.Supervisor.DTO;
using Gateway.Biometrics.Resources;
using Gateway.Extensions;

namespace Gateway.Biometrics.Application.Supervisor.Validator
{
    public static class SupervisorValidator
    {
        internal class SupervisorApprovalValidator : AbstractValidator<SupervisorApprovalRequest>
        {
            public SupervisorApprovalValidator()
            {
                RuleFor(p => p).Custom((item, context) =>
                {
                    if (item.UserName.IsNullOrWhitespace())
                        context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.UserName)));
                });

                RuleFor(p => p).Custom((item, context) =>
                {
                    if (item.Password.IsNullOrWhitespace())
                        context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Password)));
                });

                RuleFor(p => p).Custom((item, context) =>
                {
                    if (!item.BranchId.IsNumericAndGreaterThenZero())
                        context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.BranchId)));
                });
            }
        }


    }
}
