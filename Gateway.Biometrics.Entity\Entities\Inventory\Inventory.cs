﻿using System.Collections.Generic;

namespace Gateway.Biometrics.Entity.Entities.Inventory
{
    public class Inventory : BaseEntity
    {  
        public int InventoryDefinitionId { get; set; }
        public int CabinId { get; set; }
          
        public string SerialNumber { get; set; }

        public string Description { get; set; }

        public int Status { get; set; }

        public virtual InventoryDefinition InventoryDefinition { get; set; }
        //public virtual Cabin.Cabin Cabin { get; set; }

        public virtual List<InventoryIpCamera> IpCameras { get; set; }
    }
}
