﻿using Gateway.Biometrics.Core;
using System.Collections.Generic;

namespace Gateway.Biometrics.Application
{
    public class BaseServiceResult<TStatus> : IBaseServiceResult<TStatus>
    {
        public string Message { get; set; }
        public List<string> ValidationMessages { get; set; }
        public TStatus Status { get; set; }

        protected BaseServiceResult()
        {
            ValidationMessages = new List<string>();
        }
    }

    public class BasePaginationServiceListResult<TStatus>
    {
        public string Message { get; set; }

        public TStatus Status { get; set; }

        public List<string> ValidationMessages { get; set; }

        public int TotalNumberOfPages { get; set; }

        public int TotalNumberOfRecords { get; set; }
    }

    public class BaseServiceDataResult<T> : IBaseServiceDataResult<T>
    {
        public string Message { get; set; }
        public List<string> ValidationMessages { get; set; }
        public T Data { get; set; }

        protected BaseServiceDataResult()
        {
            ValidationMessages = new List<string>();
        }
    }
}
