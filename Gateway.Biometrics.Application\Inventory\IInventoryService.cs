﻿using System.Threading.Tasks;
using Gateway.Biometrics.Application.Inventory.DTO;

namespace Gateway.Biometrics.Application.Inventory
{
    public interface IInventoryService
    {
        Task<GetInventoryResult> GetInventory(GetInventoryRequest request);
        Task<CreateInventoryResult> CreateInventory(CreateInventoryRequest request);

        Task<UpdateInventoryResult> UpdateInventory(UpdateInventoryRequest request);
        
        Task<DeleteInventoryResult> DeleteInventory(DeleteInventoryRequest request);

        Task<GetPaginatedInventoriesResult> GetPaginatedInventories(GetPaginatedInventoriesRequest request);
    }
}