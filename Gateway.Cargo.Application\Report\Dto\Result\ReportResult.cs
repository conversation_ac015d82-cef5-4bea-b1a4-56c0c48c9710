﻿using System;
using System.Collections.Generic;
using Gateway.Cargo.Resources;
using Gateway.Core.CustomAttributes;
using Gateway.Core.Responses;
using Gateway.Core.Responses.Models;

namespace Gateway.Cargo.Application.Report.Dto.Result
{
    public class GetCargoTrackReportByStatusResponse : BaseServiceResult<GetCargoTrackReportStatus>
    {
        public List<ResultDto> ResultList { get; set; }

        public class ResultDto
        {
            public string BranchName { get; set; }
            public List<DataDto> Data { get; set; }

            public class DataDto
            {
                public string ApplicationNumber { get; set; }
                public string Nationality { get; set; }
                public string PassportNumber { get; set; }
                public DateTime? ApplicationDate { get; set; }
                public string Name { get; set; }
                public string Surname { get; set; }
                public DateTime? BirthDate { get; set; }
                public string ApplicationCreatedBy { get; set; }
                public DateTime? BarcodeDate { get; set; }
                public string BarcodeCreatedBy { get; set; }
                public string Status { get; set; }
                public string CargoProvider { get; set; }
                public string TrackingNumber { get; set; }
            }
        }
    }

    public enum GetCargoTrackReportStatus
    {
        [CustomHttpStatus(Code = "SUCCESS", Resources = typeof(ServiceResources), Status = "SUCCESS", StatusCode = HttpStatusCodes.Ok)]
        Successful,

        [CustomHttpStatus(Code = "INVALID_INPUT_ERROR", Resources = typeof(ServiceResources), Status = "INVALID_INPUT_ERROR", StatusCode = HttpStatusCodes.InvalidInput)]
        InvalidInput,

        [CustomHttpStatus(Code = "RESOURCE_NOT_FOUND", Resources = typeof(ServiceResources), Status = "RESOURCE_NOT_FOUND", StatusCode = HttpStatusCodes.ResourceNotFound)]
        NotFound,

        [CustomHttpStatus(Code = "INTERNAL_SERVER_ERROR", Resources = typeof(ServiceResources), Status = "INTERNAL_SERVER_ERROR", StatusCode = HttpStatusCodes.InternalServerError)]
        InternalServerError
    }
}
