﻿using Gateway.Biometrics.Application.Cabin.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.InventoryStatusLog.DTO
{
    public class LastInventoryStatusLogsOfCabin
    {
        public CabinDto Cabin { get; set; }
        public List<InventoryStatusLogDto> LastLogsOfEachInventories { get; set; } = new List<InventoryStatusLogDto>();
    }
}