﻿using System;
using AutoMapper;
using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Api.Models.DemographicInformation;
using Gateway.Biometrics.Application.DemographicInformation;
using Gateway.Biometrics.Application.DemographicInformation.DTO;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Resources;
using Gateway.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;
using Gateway.Biometrics.Application.Supervisor;
using Gateway.Biometrics.Api.Models.Supervisor;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Application.Supervisor.DTO;

namespace Gateway.Biometrics.Api.Controllers
{
    [Authorize]
    [Route("api")]
    [ApiController]
    public class SupervisorController : Controller
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private readonly ISupervisorService _supervisorService;

        #region ctor

        public SupervisorController(IContext context, IMapper mapper, ISupervisorService supervisorService)
        {
            _context = context;
            _mapper = mapper;
            _supervisorService = supervisorService;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Perform supervisor approval
        /// </summary>
        /// <param name="passportNumber"></param>  
        [SwaggerOperation(Summary = "Perform supervisor approval",
            Description = "Perform supervisor approval")]
        [HttpPost]
        [Route("supervisor/approval")]
        public async Task<IActionResult> SupervisorApproval(SupervisorApprovalRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<SupervisorApprovalRequest>(request);
            serviceRequest.Context = _context;

            var result = await _supervisorService.SupervisorApproval(serviceRequest);

            return SupervisorResponseFactory.SupervisorApprovalResponse(result);
        }
       

        #endregion
    }
}
