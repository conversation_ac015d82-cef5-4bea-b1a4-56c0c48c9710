﻿{
  "GlobalConfiguration": {
    "BaseUrl": "http://localhost:8001"
  },
  "Routes": [
    //development
    {
      "UpstreamPathTemplate": "/dev_chathub",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "DownstreamPathTemplate": "/chathub",
      "ReRouteIsCaseSensitive": false,
      "DownstreamScheme": "wss",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 4001
        }
      ],
      "HttpHandlerOptions": {
        "AllowAutoRedirect": true
      }
    },
    {
      "DownstreamPathTemplate": "/chathub/negotiate",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 4001
        }
      ],
      "ReRouteIsCaseSensitive": false,
      "UpstreamHttpMethod": [ "POST" ],
      "UpstreamPathTemplate": "/{everything}/negotiate"
    },
    
    //production
    {
      "UpstreamPathTemplate": "/prod_chathub",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "DownstreamPathTemplate": "/chathub",
      "ReRouteIsCaseSensitive": false,
      "DownstreamScheme": "wss",
      "DownstreamHostAndPorts": [
        {
          "Host": "gatewayqms.gateway.com.tr"
        }
      ],
      "HttpHandlerOptions": {
        "AllowAutoRedirect": true
      }
    }/*,
    {
      "DownstreamPathTemplate": "/chathub/negotiate",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "gatewayqms.gateway.com.tr"
        }
      ],
      "ReRouteIsCaseSensitive": false,
      "UpstreamHttpMethod": [ "POST" ],
      "UpstreamPathTemplate": "/{everything}/negotiate"
    },*/
  ]
}