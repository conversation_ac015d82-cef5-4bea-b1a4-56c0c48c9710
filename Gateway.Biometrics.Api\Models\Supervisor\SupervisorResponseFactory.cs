﻿using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Application.Supervisor.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.Biometrics.Api.Models.Supervisor
{
    public static class SupervisorResponseFactory
    {

        public static ObjectResult SupervisorApprovalResponse(SupervisorApprovalResult result)
        {
            switch (result.Status)
            {
                case SupervisorApprovalStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.SuperVisorInfo
                    })
                    { StatusCode = HttpStatusCodes.Created };
                case SupervisorApprovalStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case SupervisorApprovalStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case SupervisorApprovalStatus.UserNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.USER_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.USER_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case SupervisorApprovalStatus.UserNotAuthorized:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.USER_NOT_AUTHORIZED,
                        Code = Resource.GetKey(ServiceResources.USER_NOT_AUTHORIZED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.Unauthorized };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

    }
}