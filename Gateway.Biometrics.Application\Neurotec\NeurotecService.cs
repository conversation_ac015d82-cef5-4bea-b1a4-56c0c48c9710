﻿using AutoMapper;
using Gateway.Biometrics.Application.Neurotec.DTO;
using Gateway.Biometrics.Application.Neurotec.Validator;
using Gateway.Biometrics.Entity.Entities.NeuroTec;
using Gateway.Biometrics.Persistence;
using Gateway.Biometrics.Resources;
using Gateway.Core.Pagination;
using Gateway.Extensions;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Neurotec
{
    public class NeurotecService : INeurotecService
    {
        private readonly BiometricsDbContext _dbContext;
        private readonly IValidationService _validationService;
        private IMapper _mapper;

        public NeurotecService(IValidationService validationService, BiometricsDbContext dbContext, IMapper mapper)
        {
            _validationService = validationService;
            _dbContext = dbContext;
            _mapper = mapper;
        }

        public async Task<GetNeurotecLicenseResult> GetNeurotecLicense(GetNeurotecLicenseRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetNeurotecLicenseValidator), request);

            if (!validationResult.IsValid)
                return new GetNeurotecLicenseResult
                {
                    Status = GetNeurotecLicenseStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var neurotecLicenseExisting = await _dbContext.NeurotecLicense.Where(p => p.Id == request.ResourceId && !p.IsDeleted)
                .FirstOrDefaultAsync();

            if (neurotecLicenseExisting == null)
                return new GetNeurotecLicenseResult
                {
                    Status = GetNeurotecLicenseStatus.NotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };
                       

            var neurotecLicenseGet = _mapper.Map<NeurotecLicenseDto>(neurotecLicenseExisting);

            return new GetNeurotecLicenseResult
            {
                Status = GetNeurotecLicenseStatus.Successful,
                Message = ServiceResources.RESOURCE_FOUND,
                NeurotecLicense = neurotecLicenseGet
            };
        }

        public async Task<UpdateNeurotecLicenseResult> UpdateNeurotecLicense(UpdateNeurotecLicenseRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateNeurotecLicenseValidator), request);

            if (!validationResult.IsValid)
                return new UpdateNeurotecLicenseResult
                {
                    Status = UpdateNeurotecLicenseStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            request.HostName = request.HostName?.Trim();

            if (!request.HostName.IsNullOrWhitespace() && !_dbContext.ClientConfiguration.Where(n => n.HostName == request.HostName && !n.IsDeleted).Any())
            {
                return new UpdateNeurotecLicenseResult
                {
                    Status = UpdateNeurotecLicenseStatus.HostNameNotFound,
                    Message = ServiceResources.HOST_NAME_NOT_FOUND,                   
                };
            }

            var neurotecLicenseExisting = await _dbContext.NeurotecLicense
                .Where(q => q.Id == request.Id).FirstOrDefaultAsync();

            if (neurotecLicenseExisting == null)
                return new UpdateNeurotecLicenseResult
                {
                    Status = UpdateNeurotecLicenseStatus.NeurotecLicenseNotFound,
                    Message = ServiceResources.NEUROTEC_LICENSE_NOT_FOUND,
                };

            neurotecLicenseExisting.Id = request.Id;
            neurotecLicenseExisting.HostName = request.HostName;

            neurotecLicenseExisting.IsDeleted = false;
            neurotecLicenseExisting.UpdatedAt = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
            neurotecLicenseExisting.CreatedAt = DateTime.SpecifyKind(neurotecLicenseExisting.CreatedAt.Value, DateTimeKind.Utc);

            _dbContext.NeurotecLicense.Update(neurotecLicenseExisting);

            #region License usage log

            var neurotecLicenseUsageLog = new NeurotecLicenseUsageLog()
            {
                LicenseNumber = neurotecLicenseExisting.LicenseNumber,
                HostName = neurotecLicenseExisting.HostName,
                CreatedAt = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc)
            };
            await _dbContext.NeurotecLicenseUsageLog.AddAsync(neurotecLicenseUsageLog);

            #endregion

            await _dbContext.SaveChangesAsync();

            return new UpdateNeurotecLicenseResult
            {
                Status = UpdateNeurotecLicenseStatus.Successful,
                Message = ServiceResources.RESOURCE_UPDATED,
                Id = neurotecLicenseExisting.Id
            };
        }

        public async Task<GetPaginatedNeurotecLicensesResult> GetPaginatedNeurotecLicenses(GetPaginatedNeurotecLicensesRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetPaginatedNeurotecLicensesValidator), request);

            if (!validationResult.IsValid)
                return new GetPaginatedNeurotecLicensesResult
                {
                    Status = GetPaginatedNeurotecLicensesStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var queryNeurotecLicenses = _dbContext.NeurotecLicense
                .Where(p => !p.IsDeleted);

            var listNeurotecLicenses = await queryNeurotecLicenses.ToListAsync();
            if (listNeurotecLicenses == null || listNeurotecLicenses.Count == 0)
                return new GetPaginatedNeurotecLicensesResult
                {
                    Status = GetPaginatedNeurotecLicensesStatus.ResourceNotFound,
                    Message = ServiceResources.NEUROTEC_LICENSE_NOT_FOUND
                };

            var result = new GetPaginatedNeurotecLicensesResult
            {
                NeurotecLicenses = listNeurotecLicenses.Select(p => _mapper.Map<NeurotecLicenseDto>(p)).ToList()
            };

            var paginationResult = PagedResultsFactory.CreatePagedResult(
                result.NeurotecLicenses.AsQueryable(), request.Pagination.PageNumber, request.Pagination.PageSize,
                request.Pagination.OrderBy, request.Pagination.Ascending);

            return paginationResult == null
                ? new GetPaginatedNeurotecLicensesResult
                {
                    NeurotecLicenses = null,
                    Status = GetPaginatedNeurotecLicensesStatus.ResourceNotFound,
                    Message = ServiceResources.INVALID_INPUT_ERROR
                }
                : new GetPaginatedNeurotecLicensesResult
                {
                    NeurotecLicenses = paginationResult.Results,
                    TotalNumberOfPages = paginationResult.TotalNumberOfPages,
                    TotalNumberOfRecords = paginationResult.TotalNumberOfRecords,
                    Status = GetPaginatedNeurotecLicensesStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED
                };
        }


    }
}
