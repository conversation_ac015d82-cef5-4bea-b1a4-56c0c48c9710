﻿using System;
using System.Collections.Generic;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.Cabin.DTO
{
    public class GetPaginatedCabinsResult : BasePaginationServiceListResult<GetPaginatedCabinsStatus>
    {
        public IEnumerable<CabinDto> Cabins { get; set; }
    }
}
public enum GetPaginatedCabinsStatus
{
    Successful,
    InvalidInput,
    ResourceNotFound
}
