﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gateway.Biometrics.Entity.ExternalDbEntities
{
    public class UserBranch
    {
        public UserBranch()
        {
            IsActive = true; 
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public int BranchId { get; set; }        
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
    }
}

