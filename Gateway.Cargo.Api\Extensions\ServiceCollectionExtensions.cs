﻿using AutoMapper;
using Gateway.Cargo.Api.Mapping;
using Gateway.Cargo.Application.CargoTrack;
using Gateway.Cargo.Application.Report;
using Gateway.Cargo.Application.Shipment;
using Gateway.Cargo.Application.Status;
using Gateway.Cargo.Core.Context;
using Gateway.Cargo.FalconExpress;
using Gateway.Cargo.Persistence;
using Gateway.Db.Repository;
using Gateway.Validation;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Gateway.Cargo.Api.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection RegisterMappers(this IServiceCollection services)
        {
            var mapperConfiguration = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(new ApiMappingProfile());
            });

            var mapper = mapperConfiguration.CreateMapper();

            services.AddSingleton(typeof(IMapper), _ => mapper);

            return services;
        }

        public static IServiceCollection RegisterServices(this IServiceCollection services)
        {
            services.AddScoped<IValidationService, ValidationService>();
            services.AddScoped<IFalconExpressProvider, FalconExpressProvider>();
            services.AddScoped<IShipmentService, ShipmentService>();
            services.AddScoped<ICargoTrackService, CargoTrackService>();
            services.AddScoped<IReportService, ReportService>();
            services.AddScoped<ICargoStatusService, CargoStatusService>();

            return services;
        }

        public static IServiceCollection RegisterDbContext(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddDbContext<CargoDbContext>(options => options.UseNpgsql(configuration["ConnectionStrings:GatewayCargoDbConnection"]));
            services.AddScoped<IDbRepository>(options => new DbRepository(configuration["ConnectionStrings:GatewayCargoDbConnection"]));

            return services;
        }

        public static IServiceCollection RegisterHttpContext(this IServiceCollection services)
        {
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddScoped<IContextFactory, ContextFactory>();
            services.AddTransient(p => p.GetRequiredService<IContextFactory>().Create());

            return services;
        }
    }
}
