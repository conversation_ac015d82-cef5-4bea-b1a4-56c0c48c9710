﻿using System.ComponentModel.DataAnnotations;

namespace Gateway.External.Entity.Entities.Branch
{
    public class BranchDataTranslation
    {
        public int Id { get; set; }

        [Required]
        public int BranchId { get; set; }


        public string Address { get; set; }


        public string Mission { get; set; }


        public string CorporateName { get; set; }


        public string InvoiceNumber { get; set; }


        public string CityName { get; set; }

        [Required]
        public int LanguageId { get; set; }

        public Branch Branch { get; set; }
    }
}
