﻿namespace Gateway.Cargo.Dto.Request
{
    public class ShipmentRequest : BaseServiceRequest
    {
        public int ApplicationId { get; set; }
        public byte CargoProviderId { get; set; }
        public string EnvironmentType { get; set; }
        public string OfficeId { get; set; }
        public string AreaId { get; set; }

        public Application Application { get; set; }
        public Branch Branch { get; set; }
    }

	public class Branch
    {
		public string Phone { get; set; }
		public string BranchAddress { get; set; }
		public string BranchCityName { get; set; }
		public string BranchCountryCode { get; set; }
    }

    public class Application
    {
        public string Name { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string PostalCode { get; set; }
        public string Email { get; set; }
        public string PassportNumber { get; set; }
        public string Surname { get; set; }
		public string Phone1 { get; set; }
		public string Phone2 { get; set; }
        public string NameOfSecondContactPerson { get; set; }
        public string Phone3 { get; set; }
    }
}
