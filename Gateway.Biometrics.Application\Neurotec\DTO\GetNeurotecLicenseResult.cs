﻿using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.Neurotec.DTO
{

    public class GetNeurotecLicenseResult : BaseServiceResult<GetNeurotecLicenseStatus>
    {
        public NeurotecLicenseDto NeurotecLicense { get; set; }
    }

  
    public enum GetNeurotecLicenseStatus
    {
        Successful,
        InvalidInput,
        NotFound,        
    }
}
