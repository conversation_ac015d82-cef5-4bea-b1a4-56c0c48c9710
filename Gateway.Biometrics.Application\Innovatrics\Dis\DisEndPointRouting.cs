﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Innovatrics.Dis
{
    public class DisEndPointRouting
    {
        private string _baseAddress;
        private string _apiVersion;
        public string FaceId { get; set; }
        public DisEndPointRouting(string baseAddress, string apiVersion)
        {
            _baseAddress = baseAddress;
            _apiVersion = apiVersion;
        }

        private string rootUrl => $"{_baseAddress}/api/{_apiVersion}";

        public string Create => $"{rootUrl}/faces";
        public string Delete => $"{rootUrl}/faces/{FaceId}";
        public string Glasses => $"{rootUrl}/faces/{FaceId}/glasses";
        public string Quality => $"{rootUrl}/faces/{FaceId}/quality";
    }
}
