﻿using System.Collections.Generic;
using Gateway.Biometrics.Application.Office.DTO;

namespace Gateway.Biometrics.Application.Parameter.DTO
{
    public class NeurotecLicensesDto
    {
        public IList<NeurotecLicenseSelectDto> Licenses { get; set; }
    }
    public class NeurotecLicenseSelectDto
    {
        public int Id { get; set; }
        public string LicenseNumber { get; set; }


    }

    public class NeurotecLicensesRequestDto
    {
        public int? ClientConfigurationId { get; set; }
    }

    public class NeurotecLicensesResult : BaseServiceDataResult<NeurotecLicensesDto>
    {
        public int Id { get; set; }
    }
}
