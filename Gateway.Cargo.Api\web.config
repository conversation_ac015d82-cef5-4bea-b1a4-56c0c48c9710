﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<location path="." inheritInChildApplications="false">
		<system.webServer>
			<modules>
				<remove name="WebDAVModule" />
			</modules>
			<handlers>
				<remove name="WebDAV" />
				<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
			</handlers>
			<aspNetCore processPath="dotnet" arguments=".\Gateway.Cargo.Api.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" />
		</system.webServer>
	</location>
</configuration>
<!--ProjectGuid: A0FBDEE6-190A-46F3-9037-F9D29848E7CC-->