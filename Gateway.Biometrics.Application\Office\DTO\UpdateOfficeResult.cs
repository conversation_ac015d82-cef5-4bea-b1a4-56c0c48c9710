﻿using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.Office.DTO
{
    public class UpdateOfficeResult : BaseServiceResult<UpdateOfficeStatus>
    {
        public int Id { get; set; }
    }

    public enum UpdateOfficeStatus
    {
        Successful,
        ResourceExists,
        InvalidInput,
        OfficeNotFound,
        CountryNotfound,
        BrachNotFound,
    }
}
