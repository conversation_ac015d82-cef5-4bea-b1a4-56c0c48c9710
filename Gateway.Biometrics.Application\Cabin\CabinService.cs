﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Gateway.Biometrics.Persistence;
using Gateway.Biometrics.Resources;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Gateway.Core.Pagination;
using Gateway.Extensions;
using Gateway.Biometrics.Application.Lookup;
using Gateway.Biometrics.Application.Inventory.Validator;
using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.Cabin.Validator;
using Gateway.Biometrics.Application.Office.DTO;
using Gateway.Biometrics.Application.Inventory.DTO;
using System.Collections.Generic;

namespace Gateway.Biometrics.Application.Cabin
{
    public class CabinService : ICabinService
    {
        private readonly BiometricsDbContext _dbContext;
        private readonly IValidationService _validationService;
        private IMapper _mapper;

        public CabinService(IValidationService validationService, BiometricsDbContext dbContext, IMapper mapper)
        {
            _validationService = validationService;
            _dbContext = dbContext;
            _mapper = mapper;
        }

        public async Task<GetCabinResult> GetCabin(GetCabinRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetCabinValidator), request);

            if (!validationResult.IsValid)
                return new GetCabinResult
                {
                    Status = GetCabinStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var cabinExisting = await _dbContext.Cabin.Where(p => p.Id == request.ResourceId && !p.IsDeleted)
                .FirstOrDefaultAsync();

            if (cabinExisting == null)
                return new GetCabinResult
                {
                    Status = GetCabinStatus.NotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };
            

            var office = await _dbContext.Office
                .Where(n =>
                    n.Id == cabinExisting.OfficeId && !n.IsDeleted)
                .FirstOrDefaultAsync();


            if (office == null)
                return new GetCabinResult
                {
                    Status = GetCabinStatus.OfficeNotFound,
                    Message = ServiceResources.OFFICE_NOT_FOUND
                };

            //var cabinGet = new CabinDto
            //{
            //    Id = cabinExisting.Id,
            //    Name = cabinExisting.Name,
            //    OfficeId = cabinExisting.OfficeId,
            //    IsBiometricCabin = cabinExisting.IsBiometricCabin,
            //    Status = cabinExisting.Status,
            //    Office = new OfficeDto()
            //    {
            //        Id = office.Id,
            //        Name = office.Name,
            //        CountryId = office.CountryId,
            //        Address = office.Address,
            //        BranchId = office.BranchId,
            //        Status = office.Status,
            //        OfficeCode = office.OfficeCode,
            //        Phone = office.Phone,
            //        Email = office.Email
            //    },                
            //};

            var cabinGet = _mapper.Map<CabinDto>(cabinExisting);

            var unAssignedInventories = _dbContext.Inventory.Where(n=> !n.IsDeleted && n.CabinId == 0).ToList();
            cabinGet.UnAssignedInventories = _mapper.Map<List<InventoryDto>>(unAssignedInventories);

            return new GetCabinResult
            {
                Status = GetCabinStatus.Successful,
                Message = ServiceResources.RESOURCE_FOUND,
                Cabin = cabinGet
            };
        }
        

        public async Task<CreateCabinResult> CreateCabin(CreateCabinRequest request)
        {
            var validationResult = _validationService.Validate(typeof(CreateCabinValidator), request);

            if (!validationResult.IsValid)
                return new CreateCabinResult
                {
                    Status = CreateCabinStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };


            var officeCount = _dbContext.Office.Count(t => t.Id == request.OfficeId);

            if (officeCount > 1)
            {
                return new CreateCabinResult
                {
                    Status = CreateCabinStatus.OfficeNotFound,
                    Message = ServiceResources.OFFICE_NOT_FOUND
                };
            }

            var newCabin = new Entity.Entities.Cabin.Cabin()
            {
                Name = request.Name,
                OfficeId = request.OfficeId,
                Status = request.Status,
                IsBiometricCabin = request.IsBiometricCabin,

            };



            await _dbContext.Cabin.AddAsync(newCabin);

            #region Inventories
            var inventoryIds = request.Inventories.Select(x => x.Id).ToList();
            var inventories = _dbContext.Inventory.Where(n => inventoryIds.Contains(n.Id)).ToList();
            foreach (var inventory in inventories)
            {
                inventory.CabinId = newCabin.Id;
                _dbContext.Inventory.Update(inventory);
            }
            #endregion


            await _dbContext.SaveChangesAsync();

            return new CreateCabinResult
            {
                Status = CreateCabinStatus.Successful,
                Message = ServiceResources.RESOURCE_CREATED,
                Id = newCabin.Id
            };
        }

        public async Task<UpdateCabinResult> UpdateCabin(UpdateCabinRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateCabinValidator), request);

            if (!validationResult.IsValid)
                return new UpdateCabinResult
                {
                    Status = UpdateCabinStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var cabinExisting = await _dbContext.Cabin
                .Where(q => q.Id == request.CabinId).FirstOrDefaultAsync();

            if (cabinExisting == null)
                return new UpdateCabinResult
                {
                    Status = UpdateCabinStatus.CabinNotFound,
                    Message = ServiceResources.INVENTORY_DEFINITION_NOT_FOUND,
                };


            var officeCount = _dbContext.Office.Count(t => t.Id == request.OfficeId);

            if (officeCount > 1)
            {
                return new UpdateCabinResult
                {
                    Status = UpdateCabinStatus.OfficeNotFound,
                    Message = ServiceResources.OFFICE_NOT_FOUND
                };
            }

            cabinExisting.Id = request.Id;
            cabinExisting.Name = request.Name;
            cabinExisting.OfficeId = request.OfficeId;
            cabinExisting.Status = request.Status;
            cabinExisting.IsBiometricCabin = request.IsBiometricCabin;

            cabinExisting.IsDeleted = false;
            cabinExisting.UpdatedAt = DateTime.Now;

            _dbContext.Cabin.Update(cabinExisting);

            #region Inventories
            var inventoryIdsExisting = cabinExisting.Inventories.Select(x => x.Id).ToList();
            var inventoriesExisting = _dbContext.Inventory.Where(n => inventoryIdsExisting.Contains(n.Id)).ToList();
            foreach (var inventory in inventoriesExisting)
            {
                inventory.CabinId = 0;
                _dbContext.Inventory.Update(inventory);
            }

            var inventoryIdsNew = request.Inventories.Select(x => x.Id).ToList();
            var inventoriesNew = _dbContext.Inventory.Where(n => inventoryIdsNew.Contains(n.Id)).ToList();
            foreach (var inventory in inventoriesNew)
            {
                inventory.CabinId = cabinExisting.Id;
                _dbContext.Inventory.Update(inventory);
            }
            #endregion


            await _dbContext.SaveChangesAsync();

            return new UpdateCabinResult
            {
                Status = UpdateCabinStatus.Successful,
                Message = ServiceResources.RESOURCE_UPDATED,
                Id = cabinExisting.Id
            };
        }


        public async Task<DeleteCabinResult> DeleteCabin(DeleteCabinRequest request)
        {
            var validationResult = _validationService.Validate(typeof(DeleteCabinValidator), request);

            if (!validationResult.IsValid)
                return new DeleteCabinResult
                {
                    Status = DeleteCabinStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var cabinExisting = await _dbContext.Cabin
                .Where(p => !p.IsDeleted && p.Id == request.CabinId).FirstOrDefaultAsync();

            if (cabinExisting == null)
                return new DeleteCabinResult
                {
                    Status = DeleteCabinStatus.ResourceNotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };

            cabinExisting.DeletedAt = DateTime.Now;
            cabinExisting.IsDeleted = true;

            #region Inventories
            var inventoryIdsExisting = cabinExisting.Inventories.Select(x => x.Id).ToList();
            var inventoriesExisting = _dbContext.Inventory.Where(n => inventoryIdsExisting.Contains(n.Id)).ToList();
            foreach (var inventory in inventoriesExisting)
            {
                inventory.CabinId = 0;
                _dbContext.Inventory.Update(inventory);
            }            
            #endregion


            _dbContext.Cabin.Update(cabinExisting);
            await _dbContext.SaveChangesAsync();

            return new DeleteCabinResult
            {
                Status = DeleteCabinStatus.Successful,
                Message = ServiceResources.RESOURCE_DELETED,
            };
        }

    
        public async Task<GetPaginatedCabinsResult> GetPaginatedCabins(GetPaginatedCabinsRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetPaginatedCabinsValidator), request);

            if (!validationResult.IsValid)
                return new GetPaginatedCabinsResult
                {
                    Status = GetPaginatedCabinsStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var queryCabins = _dbContext.Cabin
                .Where(p => !p.IsDeleted);

            if (request.FilterCountryId.HasValue)
            {
                var officeIdsByCountry = _dbContext.Office
                    .Where(n => !n.IsDeleted && n.CountryId == request.FilterCountryId.Value).Select(n => n.Id);

                queryCabins = queryCabins.Where(n => officeIdsByCountry.Contains(n.OfficeId));
            }

            var listCabins = await queryCabins.ToListAsync();
            if (listCabins == null || listCabins.Count == 0)
                return new GetPaginatedCabinsResult
                {
                    Status = GetPaginatedCabinsStatus.ResourceNotFound,
                    Message = ServiceResources.CABIN_NOT_FOUND
                };

            var result = new GetPaginatedCabinsResult
            {
                Cabins = listCabins.Select(p => _mapper.Map<CabinDto>(p) ).ToList()
            };

            var paginationResult = PagedResultsFactory.CreatePagedResult(
                result.Cabins.AsQueryable(), request.Pagination.PageNumber, request.Pagination.PageSize,
                request.Pagination.OrderBy, request.Pagination.Ascending);

            return paginationResult == null
                ? new GetPaginatedCabinsResult
                {
                    Cabins = null, Status = GetPaginatedCabinsStatus.ResourceNotFound,
                    Message = ServiceResources.INVALID_INPUT_ERROR
                }
                : new GetPaginatedCabinsResult
                {
                    Cabins = paginationResult.Results, 
                    TotalNumberOfPages = paginationResult.TotalNumberOfPages,
                    TotalNumberOfRecords = paginationResult.TotalNumberOfRecords,
                    Status = GetPaginatedCabinsStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED
                };
        }


    }
}