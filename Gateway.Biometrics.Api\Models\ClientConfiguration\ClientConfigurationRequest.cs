﻿using System.Collections.Generic;
using System;
using Gateway.Biometrics.Application.ClientConfiguration.DTO;
using Gateway.Biometrics.Entity.Entities.ClientConfiguration;
using Gateway.Core.Pagination;

namespace Gateway.Biometrics.Api.Models.ClientConfiguration
{
    public class BaseClientConfigurationRequestModel
    {
        public string HostName { get; set; }

        public string Description { get; set; }

        public int Status { get; set; }

        public int CountryId { get; set; }

        public int ProvinceId { get; set; }

        public int OfficeId { get; set; }

        public int CabinId { get; set; }

        public int LicenseId { get; set; }

        public string NeurotecLicenseNumber { get; set; }

        public List<ClientConfigurationInventoryDto> ClientConfigurationInventories { get; set; }

        public BaseClientConfigurationRequestModel()
        {
        }
    }
    
    public class CreateClientConfigurationRequestModel : BaseClientConfigurationRequestModel
    {
    }

    public class UpdateClientConfigurationRequestModel : BaseClientConfigurationRequestModel
    {
    }

    public class DeleteClientConfigurationRequestModel
    {
        public int ClientConfigurationId { get; set; }
    }
    
    public class GetPaginatedClientConfigurationsRequestModel
    {
        public int? FilterCountryId { get; set; }
        public PaginationRequest Pagination { get; set; }
    }
}