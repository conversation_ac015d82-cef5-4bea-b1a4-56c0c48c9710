﻿using System;
using System.Linq;
using FluentValidation;
using Gateway.Extensions;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Entity.Entities.Inventory;
using Gateway.Biometrics.Resources;
using Gateway.Validation;
using Gateway.Biometrics.Application.Cabin.DTO;

namespace Gateway.Biometrics.Application.Inventory.Validator
{

    internal class GetInventoryValidator : AbstractValidator<GetInventoryRequest>
    {
        public GetInventoryValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.ResourceId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.ResourceId)));
            });
        }
    }


    internal class CreateInventoryValidator : AbstractValidator<CreateInventoryRequest>
    {
        public CreateInventoryValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.InventoryDefinitionId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.InventoryDefinitionId)));
            });
            
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Status.IsNumeric())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Status)));
            });
            

            RuleFor(p => p).Custom((item, context) =>
            {
                var maxLength = item.GetMaxLength(nameof(item.SerialNumber));
                if (item.SerialNumber?.Length > maxLength)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_MAX_LENGTH_ERROR, nameof(item.SerialNumber), maxLength));
            });

            RuleFor(p => p).Custom((item, context) => {
                
                if (item.SerialNumber.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.SerialNumber)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                var maxLength = item.GetMaxLength(nameof(item.Description));
                if (item.Description?.Length > maxLength)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_MAX_LENGTH_ERROR, nameof(item.SerialNumber), maxLength));
            });


        }
    }

    internal class UpdateInventoryValidator : AbstractValidator<UpdateInventoryRequest>
    {
        public UpdateInventoryValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.InventoryDefinitionId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.InventoryDefinitionId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Status.IsNumeric())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Status)));
            });


            RuleFor(p => p).Custom((item, context) =>
            {
                var maxLength = item.GetMaxLength(nameof(item.SerialNumber));
                if (item.SerialNumber?.Length > maxLength)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_MAX_LENGTH_ERROR, nameof(item.SerialNumber), maxLength));
            });

            RuleFor(p => p).Custom((item, context) => {

                if (item.SerialNumber.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.SerialNumber)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                var maxLength = item.GetMaxLength(nameof(item.Description));
                if (item.Description?.Length > maxLength)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_MAX_LENGTH_ERROR, nameof(item.SerialNumber), maxLength));
            });
        }
    }
    internal class DeleteInventoryValidator : AbstractValidator<DeleteInventoryRequest>
    {
        public DeleteInventoryValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.InventoryId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.InventoryId)));
            });
        }
    }

    internal class GetPaginatedInventoriesValidator : AbstractValidator<GetPaginatedInventoriesRequest>
    {
        public GetPaginatedInventoriesValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (string.IsNullOrWhiteSpace(item.Pagination.OrderBy))
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Pagination.OrderBy)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageSize.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageSize)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageNumber.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageNumber)));
            });
        }
    }
}