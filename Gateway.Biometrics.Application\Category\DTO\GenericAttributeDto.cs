﻿using Gateway.Biometrics.Entity.Enum;
using Newtonsoft.Json;

namespace Gateway.Biometrics.Application.Category.DTO
{
    public class GenericAttributeDto
    {
        public int Id { get; set; }

        public int? ParentId { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string Options { get; set; }

        [JsonIgnore]
        public GenericAttributeTypeEnum GenericAttributeTypeEnumId { get; set; }
    }
}
