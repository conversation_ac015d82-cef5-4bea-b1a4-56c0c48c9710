﻿using Gateway.Biometrics.Application.InventoryStatusLog.DTO;
using Microsoft.AspNetCore.SignalR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Gateway.Biometrics.SignalR.Hubs
{
    public class InventoryStatusHub : Hub
    {
        public async Task SendMessageAsync(string message)
        {
            var request = JsonConvert.DeserializeObject<CreateInventoryStatusLogRequest>(message);

            await Clients.All.SendAsync("receiveMessage", message);
        }
    }
}
