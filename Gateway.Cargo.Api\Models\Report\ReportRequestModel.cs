﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Gateway.Cargo.Api.Models.Report
{
    public class BaseReportRequestModel<T> where T : class
    {

        public int UserId { get; set; }

        public int ReportTypeId { get; set; }

        public T Request { get; set; }

        public byte? StatusId { get; set; }

    }

    public class ReportRequestModelByBranch
    {
        [Required]
        public int BranchId { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }
    }

    public class ReportRequestModelByBranches
    {
        [Required]
        public List<int> BranchIds { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }
    }
}

