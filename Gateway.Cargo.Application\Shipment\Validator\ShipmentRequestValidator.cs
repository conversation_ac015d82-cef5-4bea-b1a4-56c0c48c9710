﻿using FluentValidation;
using Gateway.Cargo.Dto.Request;
using Gateway.Cargo.Resources;
using Gateway.Extensions;

namespace Gateway.Cargo.Application.Shipment.Validator
{
    internal class ShipmentRequestValidator : AbstractValidator<ShipmentRequest>
    {
        public ShipmentRequestValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.ApplicationId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INPUT_ERROR, nameof(item.ApplicationId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.CargoProviderId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INPUT_ERROR, nameof(item.CargoProviderId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (item?.Application == null)
                    context.AddFailure(string.Format(ServiceResources.INPUT_ERROR, nameof(item.Application)));
            });
        }
    }
}
