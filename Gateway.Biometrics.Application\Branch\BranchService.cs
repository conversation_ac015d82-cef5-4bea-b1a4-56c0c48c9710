﻿using Gateway.Biometrics.Application.Branch.DTO;
using Gateway.Biometrics.Application.Branch.Validator;
using Gateway.Biometrics.Persistence;
using Gateway.Biometrics.Resources;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Branch
{
    public class BranchService : IBranchService
    {
        private readonly ExternalDbContext _dbContext;
        private readonly IValidationService _validationService;

        public BranchService(IValidationService validationService, ExternalDbContext dbContext)
        {
            _validationService = validationService;
            _dbContext = dbContext;
        }

        public async Task<BranchesResult> GetBranches(GetBranchesRequest request)
        {
            var validationResult = _validationService.Validate(typeof(BranchValidator.GetBranchesValidator), request);

            if (!validationResult.IsValid)
                return new BranchesResult
                {
                    Status = GetBranchesStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var queryBranches = await _dbContext.Branch.Where(n => !n.IsDeleted).ToListAsync();
                        

            if (!queryBranches.Any())
                return new BranchesResult
                {
                    Status = GetBranchesStatus.ResourceNotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND,
                };

            var Branches = queryBranches.Select(c => new BranchDto
            {
                Id = c.Id,
                CountryId = c.CountryId,
                Name = c.CityName,               
            }).OrderBy(c => c.Name).ToList();

            return new BranchesResult
            {
                Branches = Branches,
                Status = Branches.Any() ? GetBranchesStatus.Successful : GetBranchesStatus.ResourceNotFound,
                Message = Branches.Any() ? ServiceResources.RESOURCE_RETRIEVED : ServiceResources.RESOURCE_NOT_FOUND
            };
        }

       
    }
}
