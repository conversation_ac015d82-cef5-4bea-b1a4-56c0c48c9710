﻿using System;

namespace Gateway.Biometrics.Entity.Entities.Appeal
{
    public class Appeal : BaseEntity
    {
        public string PassportNumber { get; set; }

        public string ReferenceNumber { get; set; }

        public string Name { get; set; }

        public string Surname { get; set; }

        public int Gender { get; set; }

        public string MotherName { get; set; }

        public string FatherName { get; set; }

        public DateTime BirthDate { get; set; }

        public string MaidenName { get; set; }

        public int AppealCountryId { get; set; }

        public int AppealCityId { get; set; }

        public int AppealOfficeId { get; set; }

        public int AppealCabinId { get; set; }

        public int Status { get; set; }

        public DateTime TakenDate { get; set; }

        public string HostName { get; set; }
        public bool FromOffline { get; set; }
    } 
}
