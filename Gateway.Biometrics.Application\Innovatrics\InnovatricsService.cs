﻿using System.Drawing;
using System;
using System.Text;
using Gateway.Biometrics.Application.Innovatrics.DTO;
using Gateway.Biometrics.Application.Innovatrics.Validator;
using Gateway.Biometrics.Resources;
using Gateway.Validation;
using Innovatrics.IFace;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace Gateway.Biometrics.Application.Innovatrics
{
    public class InnovatricsService : IInnovatricsService
    {
        private readonly IValidationService _validationService;
        private IConfiguration _configuration;


        #region ctor

        public InnovatricsService(IValidationService validationService, IConfiguration configuration)
        {
            _validationService = validationService;
            _configuration = configuration;
        }
        #endregion


        byte[] imageData;
        StringBuilder analyseImageDetails;
        Face face;

        static bool isInitialized; // TODO
        object lockObject = new object();
        FaceHandler faceHandler = null;
        int i = 0;

        #region Public Methods

        public AnalyseFaceResult AnalyseFace(AnalyseFaceRequest request)
        {

            var validationResult = _validationService.Validate(typeof(AnalyseFaceValidator), request);


            if (!validationResult.IsValid)
                return new AnalyseFaceResult
                {
                    Status = AnalyseFaceStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                //var image = ImageHelper.Base64ToImage(request.Base64ByteArray);
                byte[] fullImageData = Convert.FromBase64String(request.Base64Image);
                analyseImageDetails = new StringBuilder();

                if (!(DetectFace(fullImageData) is null))
                {

                    var imageDetails = ICAOVerify(face);
                    //var croppedImage = ImageHelper.ResizeImage(ImageHelper.ToImage(fullImageData), 780, 1040);

                    //using (var ms = new MemoryStream())
                    //{
                    //    croppedImage.SaveJpeg(ms);
                    //    imageData = ms.ToArray();
                    //}

                    //croppedImage.Dispose();

                    if (imageDetails.VerificationResult[FaceAttributeId.Sharpness] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Sharpness Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.Brightness] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Brightness Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.Contrast] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Contrast Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.UniqueIntensityLevels] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* UniqueIntensity Levels Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.Shadow] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Shadow Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.NoseShadow] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Nose Shadow Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.Specularity] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Specularity Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.EyeGaze] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Eye Gaze Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.EyeStatusR] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Eye StatusR Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.EyeStatusL] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Eye StatusL Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.GlassStatus] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Glass Status Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.HeavyFrame] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Heavy Frame Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.MouthStatus] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Mouth Status Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.BackgroundUniformity] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Background Uniformity Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.RedEyeR] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Red EyeR Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.RedEyeL] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Red EyeL Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.Roll] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Roll Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.Yaw] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Yaw Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.Pitch] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Pitch Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.FaceConfidence] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Face Confidence Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.RollAngle] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Roll Angle Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.PitchAngle] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Pitch Angle Not Ok");
                    if (imageDetails.VerificationResult[FaceAttributeId.YawAngle] == ICAOResult.NotOk)
                        analyseImageDetails.AppendLine("* Yaw Angle Not Ok");

                    return new AnalyseFaceResult()
                    {
                        IFaceData = new InnovatricsIFaceData()
                        {
                            ImageData = imageData,
                            CropRect = new Rectangle(0, 0, 4, 4),
                            AnalyseImageDetails = analyseImageDetails,
                            VerificationResult = imageDetails.VerificationResult
                        }
                    };

                }
                else
                {
                    analyseImageDetails.AppendLine("There is no face on picture");
                    return new AnalyseFaceResult()
                    {
                        IFaceData = new InnovatricsIFaceData() { AnalyseImageDetails = analyseImageDetails },
                        Status = AnalyseFaceStatus.ThereIsNoFace
                    };
                }
            }
            catch (Exception ex)
            {
                analyseImageDetails.AppendLine(ex.Message);
                return new AnalyseFaceResult()
                {
                    IFaceData = new InnovatricsIFaceData() { AnalyseImageDetails = analyseImageDetails },
                    Status = AnalyseFaceStatus.ServerError
                };
            }
        }

        public AnalyseFaceResult AnalyseFace2(AnalyseFaceRequest request)
        {

            var validationResult = _validationService.Validate(typeof(AnalyseFaceValidator), request);


            if (!validationResult.IsValid)
                return new AnalyseFaceResult
                {
                    Status = AnalyseFaceStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                byte[] fullImageData = Convert.FromBase64String(request.Base64Image);
                analyseImageDetails = new StringBuilder();

                if (!(DetectFace(fullImageData) is null))
                {

                    var imageDetails = ICAOVerify(face);

                    //if (imageDetails.VerificationResult[FaceAttributeId.Sharpness] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Sharpness Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.Brightness] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Brightness Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.Contrast] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Contrast Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.UniqueIntensityLevels] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* UniqueIntensity Levels Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.Shadow] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Shadow Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.NoseShadow] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Nose Shadow Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.Specularity] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Specularity Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.EyeGaze] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Eye Gaze Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.EyeStatusR] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Eye StatusR Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.EyeStatusL] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Eye StatusL Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.GlassStatus] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Glass Status Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.HeavyFrame] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Heavy Frame Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.MouthStatus] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Mouth Status Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.BackgroundUniformity] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Background Uniformity Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.RedEyeR] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Red EyeR Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.RedEyeL] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Red EyeL Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.Roll] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Roll Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.Yaw] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Yaw Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.Pitch] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Pitch Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.FaceConfidence] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Face Confidence Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.RollAngle] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Roll Angle Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.PitchAngle] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Pitch Angle Not Ok");
                    //if (imageDetails.VerificationResult[FaceAttributeId.YawAngle] == ICAOResult.NotOk)
                    //    analyseImageDetails.AppendLine("* Yaw Angle Not Ok");

                    return new AnalyseFaceResult()
                    {
                        IFaceData = new InnovatricsIFaceData()
                        {
                            ImageData = imageData,
                            CropRect = new Rectangle(0, 0, 4, 4),
                            AnalyseImageDetails = analyseImageDetails,
                            VerificationResult = imageDetails.VerificationResult
                        }
                    };

                }
                else
                {
                    //analyseImageDetails.AppendLine("There is no face on picture");
                    return new AnalyseFaceResult()
                    {
                        IFaceData = new InnovatricsIFaceData() { AnalyseImageDetails = analyseImageDetails },
                        Status = AnalyseFaceStatus.ThereIsNoFace
                    };
                }
            }
            catch (Exception ex)
            {
                analyseImageDetails.AppendLine(ex.Message);
                return new AnalyseFaceResult()
                {
                    IFaceData = new InnovatricsIFaceData() { AnalyseImageDetails = analyseImageDetails },
                    Status = AnalyseFaceStatus.ServerError
                };
            }
        }

        public async Task<AnalyseFaceDisResult> AnalyseFaceDis(AnalyseFaceRequest request)
        {
            var disService = InnovatricsDisServiceProviderFactory.GetInstance(_configuration);

            #region create face

            var createFaceResult = await disService.Create(request.Base64Image);
            if (createFaceResult == null)
            {
                return new AnalyseFaceDisResult()
                {
                    Status = AnalyseFaceDisResultStatus.CreateFaceError,
                };
            }

            if (createFaceResult.Status != CreateFaceStatus.Successful)
            {
                return new AnalyseFaceDisResult()
                {
                    Status = AnalyseFaceDisResultStatus.CreateFaceError,
                    Message = createFaceResult.Message ?? createFaceResult.ErrorCode
                };
            }

            if (createFaceResult.Detection == null)
            {
                return new AnalyseFaceDisResult()
                {
                    Status = AnalyseFaceDisResultStatus.ThereIsNoFace,
                    Message = createFaceResult.Message ?? createFaceResult.ErrorCode
                };
            }


            var faceId = createFaceResult.Id;
            var confidence = createFaceResult.Detection.Confidence;

            #endregion

            #region face glasses

            var faceGlassesResult = await disService.Glasses(faceId);

            if (faceGlassesResult == null)
            {
                return new AnalyseFaceDisResult()
                {
                    Status = AnalyseFaceDisResultStatus.FaceGlassesError,
                };
            }

            if (faceGlassesResult.Status != FaceGlassesStatus.Successful)
            {
                return new AnalyseFaceDisResult()
                {
                    Status = AnalyseFaceDisResultStatus.FaceGlassesError,
                    Message = faceGlassesResult.Message ?? faceGlassesResult.ErrorCode
                };
            }

            #endregion

            #region face quality
            var faceQualityResult = await disService.Quality(faceId);

            if (faceQualityResult == null)
            {
                return new AnalyseFaceDisResult()
                {
                    Status = AnalyseFaceDisResultStatus.FaceQualityError,
                };
            }

            if (faceQualityResult.Status != FaceQualityStatus.Successful)
            {
                return new AnalyseFaceDisResult()
                {
                    Status = AnalyseFaceDisResultStatus.FaceQualityError,
                    Message = faceQualityResult.Message ?? faceQualityResult.ErrorCode
                };
            }
            #endregion

            #region delete face
            var deleteFaceResult = disService.Delete(faceId);
            #endregion

            faceQualityResult.Confidence = new FaceAttributeDto() { Score = confidence, PreconditionsMet = true };
            faceQualityResult.GlassStatus = new FaceAttributeDto() { Score = faceGlassesResult.Score, PreconditionsMet = true };
            faceQualityResult.HeavyFrame = new FaceAttributeDto() { Score = faceGlassesResult.HeavyFrame, PreconditionsMet = true };


            return new AnalyseFaceDisResult()
            {
                Status = AnalyseFaceDisResultStatus.Successful,
                IFaceData = faceQualityResult
            };
        }


        #endregion

        #region Private Methods

        private IFace faceInstance;
        private PointF[] DetectFace(byte[] faceImageData)
        {

            lock (lockObject)
            {
                if (!isInitialized)
                {
                    faceInstance = IFace.Instance;

                    faceInstance.Init();
                    isInitialized = true;

                }

                if (faceHandler == null)
                {
                    faceHandler = new FaceHandler();
                    faceHandler.SetParam(global::Innovatrics.IFace.Parameter.SegmentationMattingType, SegmentationMatting.Global);
                    faceHandler.SetParam(global::Innovatrics.IFace.Parameter.FaceCropFullFrontalExtendedScale, "50");

                }
            }

            // face = faceHandler.DetectFaces(ConvertByteArrayToImage(faceImageData), 40, 400, 10)?.FirstOrDefault();
            // return face is null ? null : face.GetCropRectangle();

            return null;
        }

        private ICAOVerification ICAOVerify(Face face)
        {
            FaceHandler faceHandler = new FaceHandler();
            faceHandler.SetParam(global::Innovatrics.IFace.Parameter.FaceDetSpeedAccuracyMode, "accurate");
            faceHandler.SetParam(global::Innovatrics.IFace.Parameter.SegmentationMattingType, SegmentationMatting.Global);
            faceHandler.SetParam(global::Innovatrics.IFace.Parameter.FaceCropFullFrontalExtendedScale, "50");

            ICAOVerification result = new ICAOVerification();

            foreach (var item in ICAOVerification.FaceAttributeIds.Value)
            {
                try
                {
                    if (!result.VerificationResult.ContainsKey(item))
                    {
                        var value = face.GetAttributeRangeStatus(item, faceHandler);
                        result.VerificationResult.Add(item, value == FaceAttributeRangeStatus.InRange ? ICAOResult.Ok : ICAOResult.NotOk);
                    }
                }
                catch (Exception ex)
                {
                    if (!result.VerificationResult.ContainsKey(item))
                    {
                        if (item == FaceAttributeId.GlassStatus && ex.Message.Contains("Can't get score"))
                            result.VerificationResult.Add(item, ICAOResult.Ok);
                        else if (item == FaceAttributeId.HeavyFrame && ex.Message.Contains("Can't get score"))
                            result.VerificationResult.Add(item, ICAOResult.Ok);
                        else if (item == FaceAttributeId.BackgroundUniformity)
                            result.VerificationResult.Add(item, ICAOResult.NotOk);
                    }
                }
            }

            return result;
        }

        #endregion

    }
}
