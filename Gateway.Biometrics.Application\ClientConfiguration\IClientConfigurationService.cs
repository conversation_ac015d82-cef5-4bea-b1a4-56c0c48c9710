﻿using Gateway.Biometrics.Application.ClientConfiguration.DTO;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.ClientConfiguration
{
    public interface IClientConfigurationService
    {
        public Task<GetClientConfigurationByHostNameResult> GetClientConfigurationByHostName(GetClientConfigurationByHostNameRequest request);
        public Task<GetClientConfigurationByHostNameResult> GetClientConfigurationByHostName2(GetClientConfigurationByHostNameRequest request);

        Task<GetClientConfigurationResult> GetClientConfiguration(GetClientConfigurationRequest request);
        Task<CreateClientConfigurationResult> CreateClientConfiguration(CreateClientConfigurationRequest request);
        Task<UpdateClientConfigurationResult> UpdateClientConfiguration(UpdateClientConfigurationRequest request);
        Task<DeleteClientConfigurationResult> DeleteClientConfiguration(DeleteClientConfigurationRequest request);
        Task<GetPaginatedClientConfigurationsResult> GetPaginatedClientConfigurations(GetPaginatedClientConfigurationsRequest request);
    }
}
