﻿namespace Gateway.Cargo.LastMile.Dto.Result
{
    public class LastMileAddShipmentResult : LastMileBaseServiceResult<BaseLastMileStatus, LastMileAddShipmentResult.ShipmentResult>
    {
        public class ShipmentResult
        {
            public string Id { get; set; }
            public string OfficeId { get; set; }
            public string ShipmentType { get; set; }
            public string DriverId { get; set; }
            public string HubId { get; set; }
            public string Status { get; set; }
            public string MobileStatus { get; set; }
            public string UpdatedAt { get; set; }
            public string CreatedAt { get; set; }
            public string TrackNumber { get; set; }
            public string DropOffLocation { get; set; }
            public string SizeId { get; set; }
            public string UserId { get; set; }
            public string PickUpLocation { get; set; }
            public string ArrivalDateTime { get; set; }
            public string Distance { get; set; }
            public string RecipientPhoneNumber { get; set; }
            public string Source { get; set; }
            public string Destination { get; set; }
            public string Fare { get; set; }
            public string Items { get; set; }
            public string TrackingNumberSegments { get; set; }
            public string AreaId { get; set; }
            public string City { get; set; }
            public string DeletedAt { get; set; }
            public bool IsActive { get; set; }
            public int NumberOfPackages { get; set; }
        }
    }
}
