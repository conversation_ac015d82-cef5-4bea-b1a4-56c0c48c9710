﻿using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.Appeal.DTO
{
    public class InsertAppealWithMetaDataFastResult : BaseServiceResult<InsertAppealWithMetaDataFastStatus>
    {
        public int Id { get; set; }
        public int AppealId { get; set; }
    }

    public enum InsertAppealWithMetaDataFastStatus
    {
        Successful,
        ResourceExists,
        InvalidInput,
        ResourceNotFound
    }
}
