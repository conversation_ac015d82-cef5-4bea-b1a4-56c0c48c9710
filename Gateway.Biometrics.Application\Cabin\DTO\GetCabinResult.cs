﻿using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.Cabin.DTO
{

    public class GetCabinResult : BaseServiceResult<GetCabinStatus>
    {
        public CabinDto Cabin { get; set; }
    }

  
    public enum GetCabinStatus
    {
        Successful,
        InvalidInput,
        NotFound,
        OfficeNotFound
    }
}
