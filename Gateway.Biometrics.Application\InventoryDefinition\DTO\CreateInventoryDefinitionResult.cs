﻿using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.InventoryDefinition.DTO
{
    public class CreateInventoryDefinitionResult : BaseServiceResult<CreateInventoryDefinitionStatus>
    {
        public int Id { get; set; }
    }
    
    public enum CreateInventoryDefinitionStatus
    {
        Successful,
        ResourceExists,
        InvalidInput,
        InventoryTypeNotFound
    }
}