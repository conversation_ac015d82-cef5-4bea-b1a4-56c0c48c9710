﻿using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Api.Models.Country;
using Gateway.Biometrics.Application.Country;
using Gateway.Biometrics.Application.Country.DTO;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Resources;
using Gateway.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Api.Controllers
{
    [Authorize]
    [Route("api")]
    [ApiController]
    public class CountryController : Controller
    {
        private readonly ICountryService _countryService;
        private readonly IContext _context;

        #region ctor

        public CountryController(ICountryService countryService, IContext context)
        {
            _countryService = countryService;
            _context = context;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets Countries
        /// </summary>
        [SwaggerOperation(Summary = "Get Countries with given parameters (from,to) or empty",
            Description = "Get Countries with given parameters (from,to) or empty")]
        [HttpGet]
        [Route("countries")]
        public async Task<IActionResult> GetCountries([FromQuery] bool to, [FromQuery] bool from)
        {
            var request = new GetCountriesRequest
            {
                Context = _context,
                To = to,
                From = from
            };

            var result = await _countryService.GetCountries(request);

            return CountryResponseFactory.CountriesResponse(result);
        }

        /// <summary>
        /// Gets a country with given id
        /// </summary>
        [SwaggerOperation(Summary = "Gets a country with given id",
            Description = "Gets a country with given id")]
        [HttpGet]
        [Route("countries/{resourceId?}")]
        public async Task<IActionResult> GetCountry(int resourceId)
        {
            if (!resourceId.IsNumericAndGreaterThenZero())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var request = new GetCountryRequest
            {
                Context = _context,
                ResourceId = resourceId
            };

            var result = await _countryService.GetCountry(request);

            return CountryResponseFactory.CountryResponse(result);
        }

        #endregion
    }
}
