using Gateway.Cargo.Api.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using Serilog;
using Gateway.Logger.Core.Configuration;
using Gateway.Logger.Core.Models;
using Microsoft.AspNetCore.Http;

var builder = WebApplication.CreateBuilder(args);

var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

var environmentFile = environment != null ? $"appsettings.{environment}.json" : "appsettings.json";

var configuration = new ConfigurationBuilder()
    .AddJsonFile(environmentFile, optional: true, reloadOnChange: true)
    .AddJsonFile("config/appsettings.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables()
    .AddCommandLine(args)
    .Build();

Log.Logger = LoggerConfigurationBuilder.GetLoggerConfiguration(new LogConfiguration("Gateway.Cargo.Api", Environment.MachineName, configuration));

try
{
	Log.Warning("Gateway Cargo Api started...");
}
catch (Exception ex)
{
	Log.Fatal(ex, " Gateway Cargo Api terminated unexpectedly...");
}

builder.Services.RegisterDbContext(builder.Configuration);
builder.Services.RegisterMappers();
builder.Services.RegisterServices();
builder.Services.RegisterHttpContext();

builder.Services.AddControllers();

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services.AddResponseCompression();
builder.Services.AddHttpClient();

builder.Services.AddCors(options => options.AddPolicy("AllowAll", p => p.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader()));

builder.Host.UseSerilog()
    .ConfigureAppConfiguration((context, config) =>
    {
        config.AddJsonFile("config/appsettings.json", optional: true, reloadOnChange: true);
    });

var app = builder.Build();

app.UseSwagger();
app.UseSwaggerUI();

app.UseHttpsRedirection();

app.ConfigureExceptionHandler();
app.UseRouting();

app.UseCors("AllowAll");

app.UseHsts();

app.UseEndpoints(endpoints =>
{
	endpoints.MapControllers();
});

app.UseSerilogRequestLogging(opts => opts.EnrichDiagnosticContext = LogEnricher.EnrichFromRequest);

app.Run();

static class LogEnricher
{
	public static void EnrichFromRequest(IDiagnosticContext diagnosticContext, HttpContext httpContext)
	{
		diagnosticContext.Set("ClientIp", httpContext.Connection.RemoteIpAddress?.ToString());
		diagnosticContext.Set("HttpMethod", httpContext.Request.Method);
		diagnosticContext.Set("UserAgent", httpContext.Request.Headers["User-Agent"].FirstOrDefault());
	}
}
