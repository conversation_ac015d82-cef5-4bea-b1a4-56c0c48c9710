﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Gateway.Biometrics.Application.InventoryAttribute.DTO
{
    public class BaseInventoryAttributeRequest : BaseServiceRequest
    {
        [Required]
        [MaxLength(50)]
        public string Name { get; set; }

        [Required]
        public string FieldType { get; set; }

        [Required]
        public string DataType { get; set; }

        [Required]
        public string DefaultValue { get; set; }

        [Required]
        public int MaxLength { get; set; }

        [Required]
        public bool IsRequired { get; set; }

        [Required]
        public bool IsSingleValue { get; set; }

        [Required]
        public int SortOrder { get; set; }

        [Required]
        public int Status { get; set; }

        public BaseInventoryAttributeRequest()
        {

        }
    }


}

