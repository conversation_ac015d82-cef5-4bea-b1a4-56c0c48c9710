﻿using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.ClientConfiguration.DTO
{
    public class CreateClientConfigurationResult : BaseServiceResult<CreateClientConfigurationStatus>
    {
        public int Id { get; set; }
    }
    
    public enum CreateClientConfigurationStatus
    {
        Successful,
        ResourceExists,
        InvalidInput,
        OfficeNotFound,
        CabinNotFound,
        CountryNotFound,
        HostNameAllreadyRegistered,
        NeurotecLicenseNotFound
    }
}