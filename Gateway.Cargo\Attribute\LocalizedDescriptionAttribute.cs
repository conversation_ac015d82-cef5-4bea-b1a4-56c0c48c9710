﻿using System.ComponentModel;
using System.Resources;
using Gateway.Cargo.Resources;

namespace Gateway.Cargo.Attribute
{
    public class LocalizedDescriptionAttribute : DescriptionAttribute
    {
        private readonly ResourceManager _resourceManager;

        public LocalizedDescriptionAttribute(string resourceKey)
        {
            _resourceManager = new ResourceManager(typeof(EnumResources));
            ResourceKey = resourceKey;
        }

        public override string Description
        {
            get
            {
                var description = _resourceManager.GetString(ResourceKey);
                return string.IsNullOrWhiteSpace(description) ? $"[[{ResourceKey}]]" : description;
            }
        }

        public string ResourceKey { get; }
    }
}
