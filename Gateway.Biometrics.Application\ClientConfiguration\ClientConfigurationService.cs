﻿using System;
using AutoMapper;
using Gateway.Biometrics.Application.ClientConfiguration.DTO;
using Gateway.Biometrics.Application.Plugin.DTO;
using Gateway.Biometrics.Persistence;
using Gateway.Biometrics.Resources;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Entity.Entities.Inventory;
using Microsoft.EntityFrameworkCore;
using Gateway.Biometrics.Application.Inventory.Validator;
using Gateway.Biometrics.Application.ClientConfiguration.Validator;
using Gateway.Core.Pagination;
using Gateway.Validation;
using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.Cabin.Validator;
using Gateway.Biometrics.Entity.Entities.ClientConfiguration;
using Gateway.Biometrics.Application.InventoryDefinition.DTO;
using Gateway.Extensions;
using Gateway.Biometrics.Entity.Entities.NeuroTec;

namespace Gateway.Biometrics.Application.ClientConfiguration
{
    public class ClientConfigurationService : IClientConfigurationService
    {
        private readonly BiometricsDbContext _dbContext;
        private readonly IMapper _mapper;
        private readonly IValidationService _validationService;

        #region ctor

        public ClientConfigurationService(BiometricsDbContext dbContext, IMapper mapper, IValidationService validationService)
        {
            _dbContext = dbContext;
            _mapper = mapper;
            _validationService = validationService;
        }
        #endregion

        #region Public Methods

        /// <summary>
        /// This method is outdated. GetClientConfigurationByHostName2 will be used instead.
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<GetClientConfigurationByHostNameResult> GetClientConfigurationByHostName(GetClientConfigurationByHostNameRequest request)
        {
            if (string.IsNullOrEmpty(request.HostName))
            {
                return new GetClientConfigurationByHostNameResult
                {
                    Status = GetClientConfigurationByNameStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = new List<string>
                    {
                        string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(request.HostName))
                    }
                };
            }

            var clientConfiguration = await _dbContext.ClientConfiguration.FirstOrDefaultAsync(x =>
                x.HostName == request.HostName && x.IsDeleted == false);

            clientConfiguration.ClientConfigurationInventories =
                clientConfiguration.ClientConfigurationInventories.Where(x => !x.IsDeleted).ToList();
            var clientConfigurationDto = _mapper.Map<ClientConfigurationForHostDto>(clientConfiguration);
            clientConfigurationDto.ConfigurationDate = clientConfiguration.UpdatedAt ?? clientConfiguration.CreatedAt.Value;

            //WebApi tipinde inventory ekle
            clientConfigurationDto.Inventories.Add(new InventoryDto() { InventoryDefinition = new InventoryDefinitionDto() { InventoryTypeId = 0, InventoryType = new InventoryTypeDto() { Name = "WebApi" } } });

            clientConfigurationDto.Inventories.AddRange(clientConfigurationDto.ClientConfigurationInventories.Select(x => x.Inventory).ToList());

            var inventoryTypeIds = clientConfigurationDto.Inventories.Select(n => n.InventoryDefinition.InventoryTypeId).ToList();

            clientConfigurationDto.Plugins = _mapper.Map<List<PluginDto>>(_dbContext.Plugin.Where(x => inventoryTypeIds.Contains(x.InventoryTypeId) && !x.IsDeleted).ToList());

            return new GetClientConfigurationByHostNameResult()
            {
                ClientConfiguration = clientConfigurationDto,
                Status = GetClientConfigurationByNameStatus.Successful,
                Message = ServiceResources.RESOURCE_RETRIEVED
            };
        }

        public async Task<GetClientConfigurationByHostNameResult> GetClientConfigurationByHostName2(GetClientConfigurationByHostNameRequest request)
        {
            if (string.IsNullOrEmpty(request.HostName))
            {
                return new GetClientConfigurationByHostNameResult
                {
                    Status = GetClientConfigurationByNameStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = new List<string>
                    {
                        string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(request.HostName))
                    }
                };
            }

            var clientConfiguration = await _dbContext.ClientConfiguration.FirstOrDefaultAsync(x =>
                x.HostName == request.HostName && x.IsDeleted == false);

            //clientConfiguration.ClientConfigurationInventories =
            //    clientConfiguration.ClientConfigurationInventories.Where(x => !x.IsDeleted).ToList();

            var clientConfigurationDto = _mapper.Map<ClientConfigurationForHostDto>(clientConfiguration);
            //clientConfigurationDto.ConfigurationDate = clientConfiguration.UpdatedAt ?? clientConfiguration.CreatedAt;
            clientConfigurationDto.ConfigurationDate = clientConfiguration.Cabin.UpdatedAt != null ? clientConfiguration.Cabin.UpdatedAt.Value : clientConfiguration.Cabin.CreatedAt.Value;

            //WebApi tipinde inventory ekle
            clientConfigurationDto.Cabin.Inventories.Insert(0, new InventoryDto() { InventoryDefinition = new InventoryDefinitionDto() { InventoryTypeId = 0, InventoryType = new InventoryTypeDto() { Name = "WebApi" } } });

            //clientConfigurationDto.Inventories.AddRange(clientConfigurationDto.ClientConfigurationInventories.Select(x => x.Inventory).ToList());

            var inventoryTypeIds = clientConfigurationDto.Cabin.Inventories.Select(n => n.InventoryDefinition.InventoryTypeId).ToList();

            clientConfigurationDto.Plugins = _mapper.Map<List<PluginDto>>(_dbContext.Plugin.Where(x => inventoryTypeIds.Contains(x.InventoryTypeId) && !x.IsDeleted).ToList());
            clientConfigurationDto.CountryId = clientConfigurationDto.Cabin.Office.CountryId;

            return new GetClientConfigurationByHostNameResult()
            {
                ClientConfiguration = clientConfigurationDto,
                Status = GetClientConfigurationByNameStatus.Successful,
                Message = ServiceResources.RESOURCE_RETRIEVED
            };
        }


        public async Task<GetClientConfigurationResult> GetClientConfiguration(GetClientConfigurationRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetClientConfigurationValidator), request);

            if (!validationResult.IsValid)
                return new GetClientConfigurationResult
                {
                    Status = GetClientConfigurationStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var existingClientConfiguration = await _dbContext.ClientConfiguration.Where(p => p.Id == request.ResourceId && !p.IsDeleted)
                .FirstOrDefaultAsync();

            if (existingClientConfiguration == null)
                return new GetClientConfigurationResult
                {
                    Status = GetClientConfigurationStatus.NotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };

            var cabin = await _dbContext.Cabin
                .Where(n =>
                    n.Id == existingClientConfiguration.CabinId && !n.IsDeleted)
                .FirstOrDefaultAsync();

            if (cabin == null)
                return new GetClientConfigurationResult
                {
                    Status = GetClientConfigurationStatus.CabinNotFound,
                    Message = ServiceResources.CABIN_NOT_FOUND
                };

            var office = await _dbContext.Office
                .Where(n =>
                    n.Id == existingClientConfiguration.OfficeId && !n.IsDeleted)
                .FirstOrDefaultAsync();

            if (office == null)
                return new GetClientConfigurationResult
                {
                    Status = GetClientConfigurationStatus.OfficeNotFound,
                    Message = ServiceResources.OFFICE_NOT_FOUND
                };

            var assignedInventoryIdsAll = _dbContext.ClientConfigurationInventory
                .Where(n => !n.IsDeleted).Select(n => n.InventoryId);

            var unAssignedInventories = _dbContext.Inventory.Where(n => !assignedInventoryIdsAll.Contains(n.Id) && !n.IsDeleted).ToList();
            var unAssignedClientConfigurationInventories = unAssignedInventories
                .Select(n => new ClientConfigurationInventory()
                {
                    Inventory = n,
                    InventoryId = n.Id
                }).ToList();


            //var clientConfiguration = new ClientConfigurationDto
            //{
            //    Id = existingClientConfiguration.Id,
            //    CabinId = existingClientConfiguration.CabinId,
            //    OfficeId = existingClientConfiguration.OfficeId,
            //    CountryId = existingClientConfiguration.CountryId,
            //    Status = existingClientConfiguration.Status,
            //    Description = existingClientConfiguration.Description,
            //    HostName = existingClientConfiguration.HostName,
            //    LicenseId = existingClientConfiguration.LicenseId,
            //    ClientConfigurationInventories =
            //        _mapper.Map<List<ClientConfigurationInventoryDto>>(existingClientConfiguration
            //            .ClientConfigurationInventories),
            //    UnAssignedClientConfigurationInventories =
            //        _mapper.Map<List<ClientConfigurationInventoryDto>>(unAssignedClientConfigurationInventories)

            //};

            var clientConfigurationGet = _mapper.Map<ClientConfigurationDto>(existingClientConfiguration);

            #region Neurotec License
            var neuroTecLicenseInDb = _dbContext.NeurotecLicense.FirstOrDefault(n => !n.IsDeleted && n.HostName == existingClientConfiguration.HostName);
            clientConfigurationGet.NeurotecLicenseNumber = neuroTecLicenseInDb?.LicenseNumber;
            #endregion

            clientConfigurationGet.UnAssignedClientConfigurationInventories =
                _mapper.Map<List<ClientConfigurationInventoryDto>>(unAssignedClientConfigurationInventories);

            return new GetClientConfigurationResult
            {
                Status = GetClientConfigurationStatus.Successful,
                Message = ServiceResources.RESOURCE_FOUND,
                ClientConfiguration = clientConfigurationGet
            };
        }


        public async Task<CreateClientConfigurationResult> CreateClientConfiguration(CreateClientConfigurationRequest request)
        {
            var validationResult = _validationService.Validate(typeof(CreateClientConfigurationValidator), request);

            if (!validationResult.IsValid)
                return new CreateClientConfigurationResult
                {
                    Status = CreateClientConfigurationStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            request.HostName = request.HostName.Trim();
            var cabin = _dbContext.Cabin.FirstOrDefault(n => !n.IsDeleted && n.Id == request.CabinId);
            if (cabin == null)
            {
                return new CreateClientConfigurationResult
                {
                    Status = CreateClientConfigurationStatus.CabinNotFound,
                    Message = ServiceResources.CABIN_NOT_FOUND,
                    ValidationMessages = validationResult.ErrorMessages
                };
            }

            var newClientConfiguration = new Entity.Entities.ClientConfiguration.ClientConfiguration()
            {
                HostName = request.HostName,
                Description = request.Description,
                Status = request.Status,
                CountryId = request.CountryId,
                ProvinceId = request.ProvinceId,
                OfficeId = cabin.OfficeId,
                CabinId = request.CabinId,
                LicenseId = request.LicenseId,
            };

            #region Neurotec License

            Entity.Entities.NeuroTec.NeurotecLicense neurotecLicenseCurrentByRequestLicenseNumber = null;

            if (!request.NeurotecLicenseNumber.IsNullOrWhitespace())
            {
                neurotecLicenseCurrentByRequestLicenseNumber = _dbContext.NeurotecLicense.FirstOrDefault(n => n.LicenseNumber == request.NeurotecLicenseNumber && n.LicenseTypeId == 1);

                if (neurotecLicenseCurrentByRequestLicenseNumber == null)
                {
                    return new CreateClientConfigurationResult
                    {
                        Status = CreateClientConfigurationStatus.NeurotecLicenseNotFound,
                        Message = ServiceResources.NEUROTEC_LICENSE_NOT_FOUND,
                    };
                }
            }

            var neuroTecLicenseCurrent = _dbContext.NeurotecLicense.FirstOrDefault(n => n.HostName == request.HostName);

            if (neurotecLicenseCurrentByRequestLicenseNumber != null)
            {
                neurotecLicenseCurrentByRequestLicenseNumber.HostName = request.HostName;
                neurotecLicenseCurrentByRequestLicenseNumber.UpdatedAt = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
                neurotecLicenseCurrentByRequestLicenseNumber.CreatedAt = DateTime.SpecifyKind(neurotecLicenseCurrentByRequestLicenseNumber.CreatedAt.Value, DateTimeKind.Utc);

                _dbContext.NeurotecLicense.Update(neurotecLicenseCurrentByRequestLicenseNumber);
            }

            if (neuroTecLicenseCurrent != null && neuroTecLicenseCurrent.LicenseNumber != request.NeurotecLicenseNumber)
            {
                neuroTecLicenseCurrent.HostName = null;
                neuroTecLicenseCurrent.UpdatedAt = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
                neuroTecLicenseCurrent.CreatedAt = DateTime.SpecifyKind(neuroTecLicenseCurrent.CreatedAt.Value, DateTimeKind.Utc);

                _dbContext.NeurotecLicense.Update(neuroTecLicenseCurrent);
            }

            // Neurotec license usage log
            var neurotecLicenseUsageLog = new NeurotecLicenseUsageLog()
            {
                LicenseNumber = request.NeurotecLicenseNumber,
                HostName = request.HostName,
                CreatedAt = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc)
            };

            await _dbContext.NeurotecLicenseUsageLog.AddAsync(neurotecLicenseUsageLog);

            #endregion

            await _dbContext.ClientConfiguration.AddAsync(newClientConfiguration);
            await _dbContext.SaveChangesAsync();

            foreach (var clientConfigurationInventory in request.ClientConfigurationInventories)
            {
                var newClientConfigurationInventory = new ClientConfigurationInventory()
                {
                    ClientConfigurationId = newClientConfiguration.Id,
                    InventoryId = clientConfigurationInventory.InventoryId
                };

                //clientConfigurationInventory.ClientConfigurationId = newClientConfiguration.Id;
                //await _dbContext.ClientConfigurationInventory.AddAsync(clientConfigurationInventory);

                await _dbContext.ClientConfigurationInventory.AddAsync(newClientConfigurationInventory);
            }



            await _dbContext.SaveChangesAsync();

            return new CreateClientConfigurationResult
            {
                Status = CreateClientConfigurationStatus.Successful,
                Message = ServiceResources.RESOURCE_CREATED,
                Id = newClientConfiguration.Id
            };
        }

        public async Task<UpdateClientConfigurationResult> UpdateClientConfiguration(UpdateClientConfigurationRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateClientConfigurationValidator), request);

            if (!validationResult.IsValid)
                return new UpdateClientConfigurationResult
                {
                    Status = UpdateClientConfigurationStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            request.HostName = request.HostName.Trim();
            var clientConfigurationInDb = await _dbContext.ClientConfiguration
                .Where(q => q.Id == request.ClientConfigurationId).FirstOrDefaultAsync();

            if (clientConfigurationInDb == null)
                return new UpdateClientConfigurationResult
                {
                    Status = UpdateClientConfigurationStatus.ClientConfigurationNotFound,
                    Message = ServiceResources.CLIENT_CONFIGURATION_NOT_FOUND,
                };

            bool isHostNameChanged = request.HostName != clientConfigurationInDb.HostName;

            clientConfigurationInDb.HostName = request.HostName;
            clientConfigurationInDb.Description = request.Description;
            clientConfigurationInDb.Status = request.Status;
            clientConfigurationInDb.CountryId = request.CountryId;
            clientConfigurationInDb.ProvinceId = request.ProvinceId;
            clientConfigurationInDb.OfficeId = request.OfficeId;
            clientConfigurationInDb.CabinId = request.CabinId;
            clientConfigurationInDb.LicenseId = request.LicenseId;

            clientConfigurationInDb.IsDeleted = false;
            clientConfigurationInDb.UpdatedAt = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
            clientConfigurationInDb.CreatedAt = DateTime.SpecifyKind(clientConfigurationInDb.CreatedAt.Value, DateTimeKind.Utc);

            _dbContext.ClientConfiguration.Update(clientConfigurationInDb);

            foreach (var clientConfigurationInventory in clientConfigurationInDb.ClientConfigurationInventories)
            {
                clientConfigurationInventory.IsDeleted = true;
                clientConfigurationInventory.DeletedAt = DateTime.Now;
            }

            foreach (var clientConfigurationInventory in request.ClientConfigurationInventories)
            {
                var newClientConfigurationInventory = new ClientConfigurationInventory()
                {
                    ClientConfigurationId = clientConfigurationInDb.Id,
                    InventoryId = clientConfigurationInventory.InventoryId
                };
                await _dbContext.ClientConfigurationInventory.AddAsync(newClientConfigurationInventory);
            }

            #region Neurotec License

            Entity.Entities.NeuroTec.NeurotecLicense neurotecLicenseCurrentByRequestLicenseNumber = null;

            if (!request.NeurotecLicenseNumber.IsNullOrWhitespace())
            {
                neurotecLicenseCurrentByRequestLicenseNumber = _dbContext.NeurotecLicense.FirstOrDefault(n => n.LicenseNumber == request.NeurotecLicenseNumber && n.LicenseTypeId == 1);

                if (neurotecLicenseCurrentByRequestLicenseNumber == null)
                {
                    return new UpdateClientConfigurationResult
                    {
                        Status = UpdateClientConfigurationStatus.NeurotecLicenseNotFound,
                        Message = ServiceResources.NEUROTEC_LICENSE_NOT_FOUND,
                    };
                }
            }

            var neuroTecLicenseCurrent = _dbContext.NeurotecLicense.FirstOrDefault(n => n.HostName == clientConfigurationInDb.HostName);
            string neuroTecLicenseNumberCurrent = neuroTecLicenseCurrent?.LicenseNumber;


            bool isNeuroTecLicenseNumberChanged = request.NeurotecLicenseNumber != neuroTecLicenseNumberCurrent;
            if (isHostNameChanged || isNeuroTecLicenseNumberChanged)
            {
                if (isHostNameChanged && !isNeuroTecLicenseNumberChanged)
                {
                    if (neuroTecLicenseCurrent != null)
                    {
                        neuroTecLicenseCurrent.HostName = request.HostName;
                        neuroTecLicenseCurrent.UpdatedAt = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);

                        _dbContext.NeurotecLicense.Update(neuroTecLicenseCurrent);
                    }
                }
                else if (!isHostNameChanged && isNeuroTecLicenseNumberChanged)
                {
                    if (neurotecLicenseCurrentByRequestLicenseNumber != null)
                    {
                        neurotecLicenseCurrentByRequestLicenseNumber.HostName = request.HostName;
                        neurotecLicenseCurrentByRequestLicenseNumber.UpdatedAt = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
                        neurotecLicenseCurrentByRequestLicenseNumber.CreatedAt = DateTime.SpecifyKind(neurotecLicenseCurrentByRequestLicenseNumber.CreatedAt.Value, DateTimeKind.Utc);

                        _dbContext.NeurotecLicense.Update(neurotecLicenseCurrentByRequestLicenseNumber);
                    }

                    if (neuroTecLicenseCurrent != null)
                    {
                        neuroTecLicenseCurrent.HostName = null;
                        neuroTecLicenseCurrent.UpdatedAt = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
                        neuroTecLicenseCurrent.CreatedAt = DateTime.SpecifyKind(neuroTecLicenseCurrent.CreatedAt.Value, DateTimeKind.Utc);

                        _dbContext.NeurotecLicense.Update(neuroTecLicenseCurrent);
                    }
                }
                else if (isHostNameChanged && isNeuroTecLicenseNumberChanged)
                {

                    if (neurotecLicenseCurrentByRequestLicenseNumber != null)
                    {
                        neurotecLicenseCurrentByRequestLicenseNumber.HostName = request.HostName;
                        neurotecLicenseCurrentByRequestLicenseNumber.UpdatedAt = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
                        neurotecLicenseCurrentByRequestLicenseNumber.CreatedAt = DateTime.SpecifyKind(neurotecLicenseCurrentByRequestLicenseNumber.CreatedAt.Value, DateTimeKind.Utc);

                        _dbContext.NeurotecLicense.Update(neurotecLicenseCurrentByRequestLicenseNumber);
                    }

                    if (neuroTecLicenseCurrent != null)
                    {
                        neuroTecLicenseCurrent.HostName = null;
                        neuroTecLicenseCurrent.UpdatedAt = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
                        neuroTecLicenseCurrent.CreatedAt = DateTime.SpecifyKind(neuroTecLicenseCurrent.CreatedAt.Value, DateTimeKind.Utc);

                        _dbContext.NeurotecLicense.Update(neuroTecLicenseCurrent);
                    }
                }

                // Neurotec license usage log
                var neurotecLicenseUsageLog = new NeurotecLicenseUsageLog()
                {
                    LicenseNumber = request.NeurotecLicenseNumber,
                    HostName = request.HostName,
                    CreatedAt = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc)
                };

                await _dbContext.NeurotecLicenseUsageLog.AddAsync(neurotecLicenseUsageLog);

            }


            #endregion

            await _dbContext.SaveChangesAsync();

            return new UpdateClientConfigurationResult
            {
                Status = UpdateClientConfigurationStatus.Successful,
                Message = ServiceResources.RESOURCE_UPDATED,
                Id = clientConfigurationInDb.Id
            };
        }


        public async Task<DeleteClientConfigurationResult> DeleteClientConfiguration(DeleteClientConfigurationRequest request)
        {
            var validationResult = _validationService.Validate(typeof(DeleteClientConfigurationValidator), request);

            if (!validationResult.IsValid)
                return new DeleteClientConfigurationResult
                {
                    Status = DeleteClientConfigurationStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var clientConfiguration = await _dbContext.ClientConfiguration
                .Where(p => !p.IsDeleted && p.Id == request.ClientConfigurationId).FirstOrDefaultAsync();

            if (clientConfiguration == null)
                return new DeleteClientConfigurationResult
                {
                    Status = DeleteClientConfigurationStatus.ResourceNotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };

            clientConfiguration.DeletedAt = DateTime.Now;
            clientConfiguration.IsDeleted = true;

            _dbContext.ClientConfiguration.Update(clientConfiguration);

            foreach (var clientConfigurationInventory in clientConfiguration.ClientConfigurationInventories)
            {
                clientConfigurationInventory.DeletedAt = DateTime.Now;
                clientConfigurationInventory.IsDeleted = true;
                _dbContext.ClientConfigurationInventory.Update(clientConfigurationInventory);
            }

            await _dbContext.SaveChangesAsync();

            return new DeleteClientConfigurationResult
            {
                Status = DeleteClientConfigurationStatus.Successful,
                Message = ServiceResources.RESOURCE_DELETED
            };
        }

        public async Task<GetPaginatedClientConfigurationsResult> GetPaginatedClientConfigurations(GetPaginatedClientConfigurationsRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetPaginatedClientConfigurationsValidator), request);

            if (!validationResult.IsValid)
                return new GetPaginatedClientConfigurationsResult
                {
                    Status = GetPaginatedClientConfigurationsStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var queryClientConfigurations = _dbContext.ClientConfiguration
                .Where(p => !p.IsDeleted);

            if (request.FilterCountryId.HasValue)
            {
                var cabinIdsByCountry = (from office in _dbContext.Office
                                         join cabin in _dbContext.Cabin on office.Id equals cabin.OfficeId
                                         where office.CountryId == request.FilterCountryId.Value
                                         select cabin.Id);

                queryClientConfigurations = queryClientConfigurations.Where(n => cabinIdsByCountry.Contains(n.CabinId));
            }

            var listClientConfigurations = await queryClientConfigurations.ToListAsync();

            if (listClientConfigurations == null || listClientConfigurations.Count == 0)
                return new GetPaginatedClientConfigurationsResult
                {
                    Status = GetPaginatedClientConfigurationsStatus.ResourceNotFound,
                    Message = ServiceResources.INVENTORY_NOT_FOUND
                };

            var clientConfigurations = new GetPaginatedClientConfigurationsResult
            {

                ClientConfigurations = listClientConfigurations.Select(p =>
                    _mapper.Map<ClientConfigurationDto>(p)).ToList()
            };

            var paginationResult = PagedResultsFactory.CreatePagedResult(
                clientConfigurations.ClientConfigurations.AsQueryable(), request.Pagination.PageNumber, request.Pagination.PageSize,
                request.Pagination.OrderBy, request.Pagination.Ascending);

            return paginationResult == null
                ? new GetPaginatedClientConfigurationsResult
                {
                    ClientConfigurations = null,
                    Status = GetPaginatedClientConfigurationsStatus.ResourceNotFound,
                    Message = ServiceResources.INVALID_INPUT_ERROR
                }
                : new GetPaginatedClientConfigurationsResult
                {
                    ClientConfigurations = paginationResult.Results,
                    Status = GetPaginatedClientConfigurationsStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    TotalNumberOfPages = paginationResult.TotalNumberOfPages,
                    TotalNumberOfRecords = paginationResult.TotalNumberOfRecords,
                };
        }



        #endregion
    }
}
