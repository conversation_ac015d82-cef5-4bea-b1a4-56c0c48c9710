﻿using Gateway.Biometrics.Application.Appeal.DTO;
using System;
using System.Collections.Generic;

namespace Gateway.Biometrics.Application.DemographicInformation.DTO
{
    public class GetAppealsResult : BaseServiceResult<GetAppealsStatus>
    {
        public List<AppealDto> Appeals { get; set; }

    }
    
      
    public enum GetAppealsStatus
    {
        Successful,
        InvalidInput,
        ResourceNotFound,
        CountryNotFound
    }
}
