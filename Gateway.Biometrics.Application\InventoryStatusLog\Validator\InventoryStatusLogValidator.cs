﻿using FluentValidation;
using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.InventoryStatusLog.DTO;
using Gateway.Biometrics.Resources;
using Gateway.Extensions;

namespace Gateway.Biometrics.Application.InventoryStatusLog.Validator
{
    internal class GetInventoryStatusLogValidator : AbstractValidator<GetInventoryStatusLogRequest>
    {
        public GetInventoryStatusLogValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.ResourceId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.ResourceId)));
            });
        }
    }

    internal class CreateInventoryStatusLogValidator : AbstractValidator<CreateInventoryStatusLogRequest>
    {
        public CreateInventoryStatusLogValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {

                if (item.HostName.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.HostName)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.CabinId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.CabinId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.OfficeId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.OfficeId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.CountryId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.CountryId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.ProvinceId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.ProvinceId)));
            });


            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Status.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Status)));
            });

        }
    }

    internal class GetPaginatedInventoryStatusLogsValidator : AbstractValidator<GetPaginatedInventoryStatusLogsRequest>
    {
        public GetPaginatedInventoryStatusLogsValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (string.IsNullOrWhiteSpace(item.Pagination.OrderBy))
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Pagination.OrderBy)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageSize.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageSize)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageNumber.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageNumber)));
            });


            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.FilterInventoryId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.FilterInventoryId)));
            });
        }
    }

    internal class GetLastInventoryStatusLogsForEachInventoriesOfCabinValidator : AbstractValidator<GetLastInventoryStatusLogsForEachInventoriesOfCabinRequest>
    {
        public GetLastInventoryStatusLogsForEachInventoriesOfCabinValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.CabinId.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.CabinId)));
            });
        }
    }
}