﻿using System;
using System.Linq;
using FluentValidation;
using Gateway.Extensions;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Entity.Entities.Inventory;
using Gateway.Biometrics.Resources;
using Gateway.Validation;
using Gateway.Biometrics.Application.Office.DTO;

namespace Gateway.Biometrics.Application.Office.Validator
{

    internal class GetOfficeValidator : AbstractValidator<GetOfficeRequest>
    {
        public GetOfficeValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.ResourceId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.ResourceId)));
            });
        }
    }

    internal class CreateOfficeValidator : AbstractValidator<CreateOfficeRequest>
    {
        public CreateOfficeValidator()
        {
            RuleFor(p => p).Custom((item, context) => {

                if (item.Name.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Name)));
            });

            RuleFor(p => p).Custom((item, context) => {

                if (item.OfficeCode.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.OfficeCode)));
            });

            RuleFor(p => p).Custom((item, context) => {

                if (item.Phone.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Phone)));
            });

            RuleFor(p => p).Custom((item, context) => {

                if (item.Address.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Address)));
            });
            
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Status.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Status)));
            });
            
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.CountryId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.CountryId)));
            });

            //RuleFor(p => p).Custom((item, context) =>
            //{
            //    if (!item.BranchId.IsNumericAndGreaterThenZero())
            //        context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.BranchId)));
            //});
            
        }
    }

    internal class UpdateOfficeValidator : AbstractValidator<UpdateOfficeRequest>
    {
        public UpdateOfficeValidator()
        {
            RuleFor(p => p).Custom((item, context) => {

                if (item.Name.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Name)));
            });

            RuleFor(p => p).Custom((item, context) => {

                if (item.OfficeCode.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.OfficeCode)));
            });

            RuleFor(p => p).Custom((item, context) => {

                if (item.Phone.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Phone)));
            });

            RuleFor(p => p).Custom((item, context) => {

                if (item.Address.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Address)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Status.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Status)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.CountryId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.CountryId)));
            });

            //RuleFor(p => p).Custom((item, context) =>
            //{
            //    if (!item.BranchId.IsNumericAndGreaterThenZero())
            //        context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.BranchId)));
            //});

        }
    }
    internal class DeleteOfficeValidator : AbstractValidator<DeleteOfficeRequest>
    {
        public DeleteOfficeValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.OfficeId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.OfficeId)));
            });
        }
    }

    internal class GetPaginatedOfficesValidator : AbstractValidator<GetPaginatedOfficesRequest>
    {
        public GetPaginatedOfficesValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (string.IsNullOrWhiteSpace(item.Pagination.OrderBy))
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Pagination.OrderBy)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageSize.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageSize)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageNumber.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageNumber)));
            });
        }
    }
}