﻿using AutoMapper;
using Gateway.Biometrics.Api.Mapping;
using Gateway.Biometrics.Application.Appeal;
using Gateway.Biometrics.Application.Branch;
using Gateway.Biometrics.Application.Cabin;
using Gateway.Biometrics.Application.Category;
using Gateway.Biometrics.Application.ClientConfiguration;
using Gateway.Biometrics.Application.Country;
using Gateway.Biometrics.Application.DemographicInformation;
using Gateway.Biometrics.Application.Innovatrics;
using Gateway.Biometrics.Application.Inventory;
using Gateway.Biometrics.Application.InventoryDefinition;
using Gateway.Biometrics.Application.InventoryStatusLog;
using Gateway.Biometrics.Application.Neurotec;
using Gateway.Biometrics.Application.Office;
using Gateway.Biometrics.Application.Parameter;
using Gateway.Biometrics.Application.Supervisor;
using Gateway.Biometrics.Application.User;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Persistence;
using Gateway.ObjectStoring;
using Gateway.ObjectStoring.Minio;
using Gateway.Validation;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Portal.Gateway.ExternalServices;
using Portal.Gateway.ExternalServices.Contracts;

namespace Gateway.Biometrics.Api.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection RegisterMappers(this IServiceCollection services)
        {
            var mapperConfiguration = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(new ApiMappingProfile());
            });

            var mapper = mapperConfiguration.CreateMapper();

            services.AddSingleton(typeof(IMapper), _ => mapper);

            return services;
        }

        public static IServiceCollection RegisterServices(this IServiceCollection services)
        {
            services.AddScoped<IValidationService, ValidationService>();
            services.AddScoped<ICategoryService, CategoryService>();
            services.AddScoped<ICountryService, CountryService>();
            services.AddScoped<IBranchService, BranchService>();
            services.AddScoped<IDemographicInformationService, DemographicInformationService>();
            services.AddScoped<IClientConfigurationService, ClientConfigurationService>();
            services.AddScoped<IAppealService, AppealService>();
            services.AddScoped<IFileStorage, MinioFileStorage>();
            services.AddScoped<IBucketNamingNormalizer, MinioBucketNamingNormalizer>();
            services.AddScoped<ICabinService, CabinService>();
            services.AddScoped<IOfficeService, OfficeService>();
            services.AddScoped<IClientConfigurationService, ClientConfigurationService>();
            services.AddScoped<IInventoryDefinitionService, InventoryDefinitionService>();
            services.AddScoped<IInventoryService, InventoryService>();
            services.AddScoped<IParameterService, ParameterService>();
            services.AddScoped<IInnovatricsService, InnovatricsService>();
            services.AddScoped<IInventoryStatusLogService, InventoryStatusLogService>();
            services.AddScoped<ISupervisorService, SupervisorService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<ILdapService, LdapService>();
            services.AddScoped<INeurotecService, NeurotecService>();

            return services;
        }

        public static IServiceCollection RegisterDbContext(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddDbContext<BiometricsDbContext>(options => options.UseNpgsql(configuration["ConnectionStrings:GatewayBiometricsDbConnection"]));
            return services;
        }

        public static IServiceCollection RegisterExternalDbContext(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddDbContext<ExternalDbContext>(options => options.UseNpgsql(configuration["ConnectionStrings:GatewayExternalApiDbConnection"]));
            return services;
        }

        public static IServiceCollection RegisterHttpContext(this IServiceCollection services)
        {
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddScoped<IContextFactory, ContextFactory>();
            services.AddTransient(p => p.GetRequiredService<IContextFactory>().Create());

            return services;
        }
    }
}
