﻿using Gateway.Biometrics.Application.Innovatrics.Dis;
using Gateway.Biometrics.Application.Innovatrics.DTO;
using IConfiguration = Microsoft.Extensions.Configuration.IConfiguration;

namespace Gateway.Biometrics.Application.Innovatrics
{
    public static class InnovatricsDisServiceProviderFactory
    {
        public static IInnovatricsDisServiceProvider GetInstance(IConfiguration configuration)
        {            
            return SetConfigParam(configuration);
        }

        private static IInnovatricsDisServiceProvider SetConfigParam(IConfiguration configuration)
        {
            var disServiceProvider = new InnovatricsDisServiceProvider();
            var disServiceConfig = new DisServiceConfig
            {
                ApiVersion = configuration["Innovatrics:Dis:ApiVersion"],
                ServiceUrl = configuration["Innovatrics:Dis:ServiceUrl"],
                Token = configuration["Innovatrics:Dis:Token"]               
            };

            disServiceProvider.Initialize(disServiceConfig);

            return disServiceProvider;
        }
    }
}
