﻿using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Entity.Entities.Inventory;
using System;
using System.ComponentModel.DataAnnotations;
using Gateway.Biometrics.Application.Country.DTO;

namespace Gateway.Biometrics.Application.Office.DTO
{
    public class OfficeDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string OfficeCode { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public int Status { get; set; }
        public int CountryId { get; set; }
        public int BranchId { get; set; }

        public virtual CountryDto Country { get; set; } = new CountryDto();
    }
}
