﻿namespace Gateway.Biometrics.Entity.Enum
{
    public enum GenericAttributeTypeEnum
    {
        ApplicationConfigurations,
        Type,
        Brand,
        Model,
        Manufacturer
    }

    public enum InventoryStatusEnum
    {
        Offline = 0,
        Online = 1,
        PluggedButNotInitialized = 2,
        InitializedButUnplugged = 3,
        NotInitializedAndUnplugged = 4,
        Unknown = -1
    }
}
