﻿using Gateway.Biometrics.Entity.Entities.Category;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace Gateway.Biometrics.Entity.Entities.Inventory
{
    public class InventoryDefinition : BaseEntity
    {
        public int InventoryTypeId { get; set; }
        public string Description { get; set; }
        public virtual InventoryType InventoryType { get; set; }
        public virtual List<InventoryValueSet> InventoryValueSets { get; set; }
    }
}
