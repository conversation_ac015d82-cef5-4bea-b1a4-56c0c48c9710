﻿using Gateway.Biometrics.Application.Appeal.DTO;
using System;
using System.Collections.Generic;

namespace Gateway.Biometrics.Application.DemographicInformation.DTO
{
    public class GetFullAppealMetaDataResult : BaseServiceResult<GetFullAppealMetaDataStatus>
    {
        public AppealMetaDataDto AppealMetaData { get; set; }

    }
    
      
    public enum GetFullAppealMetaDataStatus
    {
        Successful,
        InvalidInput,
        ResourceNotFound,
        AppealNotFound
    }
}
