<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<IsPackable>false</IsPackable>
		<IsTestProject>true</IsTestProject>
		<PreserveCompilationContext>true</PreserveCompilationContext>

		<CollectCoverage>true</CollectCoverage>
		<CoverletOutput>$(MSBuildProjectDirectory)/../TestResults/coverage.cobertura.xml</CoverletOutput>
		<CoverletOutputFormat>cobertura</CoverletOutputFormat>
		<Include>[*]*</Include>
		<Exclude>[xunit.*]*,[*]Portal.Gateway.Api.IntegrationTests*</Exclude>
	</PropertyGroup>

	<ItemGroup>
		<None Update="xunit.runner.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<Compile Remove="Properties\**" />
		<EmbeddedResource Remove="Properties\**" />
		<None Remove="Properties\**" />
	</ItemGroup>

	<ItemGroup>
		<Content Remove="appsettings*.json" />
		<None Include="appsettings*.json" CopyToOutputDirectory="Never" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="appsettings.json" />
		<None Include="appsettings*.json" CopyToOutputDirectory="Never" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="appsettings.Development.json" />
		<None Remove="appsettings.json" />
		<None Remove="appsettings.Local.json" />
		<None Remove="appsettings.Production.json" />
		<None Remove="appsettings.Staging.json" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="appsettings.Development.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Include="appsettings.Production.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Include="appsettings.Staging.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Include="appsettings.Local.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Hosting" Version="2.2.7" />
		<PackageReference Include="FluentAssertions" Version="8.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.16" />
		<PackageReference Include="Bogus" Version="35.6.1" />
		<PackageReference Include="Moq" Version="4.20.72" />
		<PackageReference Include="Testcontainers.RabbitMq" Version="4.0.0" />
		<PackageReference Include="Verify.Xunit" Version="28.3.2" />
		<PackageReference Include="xunit.runner.visualstudio" Version="2.8.2">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Testcontainers" Version="4.0.0" />
		<PackageReference Include="Testcontainers.PostgreSql" Version="4.0.0" />
		<PackageReference Include="xunit" Version="2.9.2" />
		<PackageReference Include="xunit.analyzers" Version="1.17.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="xunit.categories" Version="2.0.8" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
		<PackageReference Include="coverlet.collector" Version="6.0.2">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="coverlet.msbuild" Version="6.0.2">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Update="SonarAnalyzer.CSharp" Version="9.32.0.97167" />
	</ItemGroup>
	<ItemGroup>
		<Using Include="Xunit" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\Portal.Gateway.Api\Portal.Gateway.Api.csproj" />
		<ProjectReference Include="..\..\Portal.Gateway.Common.Utility\Portal.Gateway.Common.Utility.csproj" />
		<ProjectReference Include="..\..\Portal.Gateway.Entity\Portal.Gateway.Entity.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="TestData\SeedData\" />
	</ItemGroup>

	<ItemGroup>
		<None Update="integration-test-doc.md">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="TestData\SeedData\test_data_seed_script.sql">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>