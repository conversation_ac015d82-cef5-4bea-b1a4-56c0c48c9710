﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Gateway.Cargo.Entity.Entities.Application
{
    public class ApplicationExtraFee
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public int ApplicationId { get; set; }

        [Required]
        public int ExtraFeeId { get; set; }

        [Required]
        public int Quantity { get; set; }

        [Required]
        public decimal Price { get; set; }

        [Required]
        public decimal Tax { get; set; }

        [Required]
        public decimal TaxRatio { get; set; }

        [Required]
        public decimal ServiceTax { get; set; }

        [Required]
        public decimal BasePrice { get; set; }

        [Required]
        public int CurrencyId { get; set; }

        public bool? IsRemoved { get; set; }

        public int? CreatedBy { get; set; }

        public DateTime? CreatedAt { get; set; }

        public bool? IsCancelled { get; set; }

        public int? CancelledBy { get; set; }

        public DateTime? CancelledAt { get; set; }

        public bool IsCancellationStatus { get; set; }

        public int? RemovedBy { get; set; }

        public DateTime? RemovedAt { get; set; }

        public int? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }

        public int? DeletedBy { get; set; }

        public DateTime? DeletedAt { get; set; }

        public Application Application { get; set; }

        public ExtraFee.ExtraFee ExtraFee { get; set; }
    }
}
