﻿using AutoMapper;
using Gateway.Cargo.Api.Models.Report;
using Gateway.Cargo.Application.Report.Dto.Request;
using Gateway.Cargo.Application.Status.Dto.Request;
using ShipmentRequest = Gateway.Cargo.Dto.Request.ShipmentRequest;

namespace Gateway.Cargo.Api.Mapping
{
    public class ApiMappingProfile : Profile
    {
        public ApiMappingProfile()
        {
	        CreateMap<Models.Request.ShipmentRequestModel, ShipmentRequest>();
	        CreateMap<Models.Request.UpdateStatusRequestModel, UpdateStatusRequest>();
            CreateMap<Models.Request.Application, Dto.Request.Application>();
            CreateMap<Models.Request.Branch, Dto.Request.Branch>();

            CreateMap<ReportRequestModelByBranches, ReportRequestByBranches>();

            CreateMap<BaseReportRequestModel<ReportRequestModelByBranches>, BaseReportRequest<ReportRequestByBranches>>().ReverseMap();
            CreateMap<BaseReportRequestModel<ReportRequestModelByBranch>, BaseReportRequest<ReportRequestByBranch>>().ReverseMap();
        }
    }
}
