﻿using Gateway.Cargo.UPS;
using System.Collections.Generic;
using Gateway.Cargo.Entity.Entities.Branch;
using IConfiguration = Microsoft.Extensions.Configuration.IConfiguration;
using Gateway.Cargo.LastMile;
using Gateway.Cargo.LastMile.Dto;

namespace Gateway.Cargo.Application
{
	public static class CargoServiceProviderFactory
	{
        private static Dictionary<byte, ICargoServiceProvider> _handler;

		public static ICargoServiceProvider GetInstance(CargoBranch cargoBranch, IConfiguration configuration)
		{
            PopulateHandler();
            return SetConfigParam(cargoBranch, _handler[cargoBranch.CargoProviderId], configuration);
		}

		private static ICargoServiceProvider SetConfigParam(CargoBranch cargoBranch, ICargoServiceProvider cargoServiceProvider, IConfiguration configuration)
		{
			switch (cargoBranch.CargoProviderId)
			{
				case (byte)CourierType.UPS:
					var upsServiceConfig = new UpsServiceConfig
					{
						BaseAddress = configuration["CargoProviders:UPS:ServiceUrl"],
						UserName = configuration["CargoProviders:UPS:UserName"],
						Password = configuration["CargoProviders:UPS:Password"],
						AccessLicenseNumber = configuration["CargoProviders:UPS:AccessLicenseNumber"],
						ShipperNumber = "97A0A9"
                    };
					cargoServiceProvider.Initialize(upsServiceConfig);
					break;
                case (byte)CourierType.LastMile:
                    var lastMileServiceConfig = new LastMileServiceConfig
                    {
                        BaseAddress = cargoBranch.BaseAddress,
                        BasePublicAddress = cargoBranch.BasePublicAddress,
                        Email = cargoBranch.UserName,
                        Password = cargoBranch.Password,
                        Token = cargoBranch.ApiKey
                    };
                    cargoServiceProvider.Initialize(lastMileServiceConfig);
                    break;
            }

			return cargoServiceProvider;
		}

		private static void PopulateHandler()
		{
			_handler = new Dictionary<byte, ICargoServiceProvider>
			{
				{(byte) CourierType.UPS, new UpsServiceProvider()},
				{(byte) CourierType.LastMile, new LastMileServiceProvider()}
            };
		}

	}

	public enum CourierType : byte
	{
		UPS = 1,
		LastMile = 2
	}
}
