﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gateway.Cargo.Entity.Entities.Branch
{
    public class BranchApplicationCountry
    {
        public BranchApplicationCountry()
        {
            IsActive = true;
        }

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Branch id from Branch entity
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// Branch authorized country id from Country entity
        /// </summary>
        public int CountryId { get; set; }

        [Column(TypeName = "citext"), MaxLength(1000)]
        public string Note { get; set; }

        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }

        public Branch Branch { get; set; }

    }
}
