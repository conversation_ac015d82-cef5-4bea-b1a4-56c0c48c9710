﻿using Gateway.Biometrics.Application;
using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.Biometrics.Api.Models.Cabin
{
    public static class CabinResponseFactory
    {
        public static ObjectResult GetCabinResponse(GetCabinResult result)
        {
            switch (result.Status)
            {
                case GetCabinStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.Cabin
                    })
                    { StatusCode = HttpStatusCodes.Created };
                case GetCabinStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case GetCabinStatus.NotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.CABIN_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.CABIN_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case GetCabinStatus.OfficeNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.OFFICE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.OFFICE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }
       
        public static ObjectResult CreateCabinResponse(CreateCabinResult result)
        {
            switch (result.Status)
            {
                case CreateCabinStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_CREATED),
                        Message = result.Message,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.Created };
                case CreateCabinStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case CreateCabinStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };
                case CreateCabinStatus.OfficeNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.OFFICE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.OFFICE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult UpdateCabinResponse(UpdateCabinResult result)
        {
            switch (result.Status)
            {
                case UpdateCabinStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_UPDATED),
                        Message = result.Message,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case UpdateCabinStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case UpdateCabinStatus.CabinNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.CABIN_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.CABIN_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateCabinStatus.OfficeNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.OFFICE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.OFFICE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateCabinStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult DeleteCabinResponse(DeleteCabinResult result)
        {
            switch (result.Status)
            {
                case DeleteCabinStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_DELETED),
                        Message = result.Message,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case DeleteCabinStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case DeleteCabinStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult GetPaginatedCabinsResponse(GetPaginatedCabinsResult result)
        {
            switch (result.Status)
            {
                case GetPaginatedCabinsStatus.Successful:
                    return new ObjectResult(new BasePaginationApiResponse()
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.Cabins,
                        TotalNumberOfPages = result.TotalNumberOfPages,
                        TotalNumberOfRecords = result.TotalNumberOfRecords
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case GetPaginatedCabinsStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case GetPaginatedCabinsStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }
    }
}