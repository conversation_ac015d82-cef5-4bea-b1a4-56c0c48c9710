﻿using AutoMapper;
using Gateway.External.Api.Factories.ResponseFactory;
using Gateway.External.Api.Models.Management;
using Gateway.External.Api.Models.Slot;
using Gateway.External.Application.Management;
using Gateway.External.Application.Management.Dto;
using Gateway.External.Core.Context;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;

namespace Gateway.External.Api.Controllers
{
    [Authorize]
    [Route("api")]
    [ApiController]
    public class ManagementController : Controller
    {
        private readonly IContext _context;
        private readonly IManagementService _managementService;

        #region ctor

        public ManagementController(IContext context, IManagementService managementService)
        {
            _context = context;
            _managementService = managementService;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Get a list of branches
        /// </summary>
        [SwaggerOperation(Summary = "Get a list of branches",
	        Description = "Get a list of branches")]
        [HttpGet]
        [Route("branches/search")]
        public async Task<IActionResult> GetBranches()
        {
	        var result = await _managementService.GetBranches(new GetBranchesRequest()
	        {
                Context = _context
	        });

	        return ManagementResponseFactory.GetBranchesResponse(result);
        }

		/// <summary>
		/// Get vas type price with given branchId and vasTypeId
		/// </summary>
		/// <param name="branchId"></param>  
		/// <param name="vasTypeId"></param>  
		[SwaggerOperation(Summary = "Get vas type price with given branchId and vasTypeId",
	        Description = "Get vas type price with given branchId and vasTypeId")]
        [HttpGet]
        [Route("branches/{branchId?}/vastype/{vasTypeId?}")]
        public async Task<IActionResult> GetBranchVasTypePrice(int branchId, int vasTypeId)
        {
            var serviceRequest = new GetVasTypePriceRequest
	        {
		        Context = _context,
		        BranchId = branchId,
		        VasTypeId = vasTypeId
	        };

	        var result = await _managementService.GetVasTypePrice(serviceRequest);

	        return ManagementResponseFactory.GetVasTypePriceResponse(result);
        }

        /// <summary>
        /// Get Application Scan Sycle Status By Branch
        /// </summary>
        /// /// <param name="branchId"></param>  
        [SwaggerOperation(Summary = "Get Application Scan Sycle Status By Branch", Description = "Get Application Scan Sycle Status By Branch")]
        [HttpGet]
        [Route("branches/{branchId?}/applications-status/search")]
        public async Task<IActionResult> GetApplicationScanSycleStatusByBranch(int? branchId)
        {
            var serviceRequest = new GetApplicationScanSycleStatusRequest
            {
                BranchId = branchId,
                Context = _context
            };

            var result = await _managementService.GetApplicationScanSycleStatus(serviceRequest);

            return BaseResponseFactory.CreateResponse(result, result.ApplicationStatuses);
        }

        /// <summary>
        /// Gets holidays with given request
        /// </summary>
        [SwaggerOperation(Summary = "Gets holidays with given request",
            Description = "Gets holidays with given request")]
        [HttpGet]
        [Route("branches/{branchId?}/holidays")]
        public async Task<IActionResult> GetBranchHolidayDays(int branchId)
        {
            var serviceRequest = new GetBranchHolidayDaysRequest
            {
                BranchId = branchId,
                Context = _context,
            };

            var result = await _managementService.GetBranchHolidayDays(serviceRequest);

            return BaseResponseFactory.CreateResponse(result, result.Data);
        }

        /// <summary>
        /// Gets holidays with given request
        /// </summary>
        /// /// <param name="branchId"></param>  
        [SwaggerOperation(Summary = "Gets branch payment choices with given request",
            Description = "Gets branch payment choices with given request")]
        [HttpGet]
        [Route("branches/{branchId?}/payment-options")]
        public async Task<IActionResult> GetBranchPaymentOptions(int branchId)
        {
            var serviceRequest = new GetBranchPaymentOptionsRequest
            {
                BranchId = branchId,
                Context = _context,
            };

            var result = await _managementService.GetBranchPaymentOptions(serviceRequest);

            return BaseResponseFactory.CreateResponse(result, result.Data);
        }

        #endregion
    }
}
