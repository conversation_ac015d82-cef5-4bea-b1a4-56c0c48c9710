﻿using Innovatrics.IFace;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace Gateway.Biometrics.Application.Innovatrics.DTO
{
    public class InnovatricsIFaceData
    {
        public byte[] ImageData { get; set; }
        
        public Rectangle CropRect { get; set; }

        public StringBuilder AnalyseImageDetails { get; set; }
        public Dictionary<FaceAttributeId, ICAOResult> VerificationResult { get; set; }
    }

    public enum PhotoCaptureType
    {
        None,
        AutoCapture,
        ForceCapture,
        Scan,
    }

    public enum ICAOResult
    {
        Ok = 0,
        NotOk = 1,
    }
}
