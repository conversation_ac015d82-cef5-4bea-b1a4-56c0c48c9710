﻿using System.Collections.Generic;

namespace Gateway.Biometrics.Application.Category.DTO
{
    public class GetCategoriesByParentNameResult : BaseServiceResult<GetCategoriesByParentNameStatus>
    {
        public IEnumerable<GenericAttributeDto> Categories { get; set; }
    }
    public enum GetCategoriesByParentNameStatus
    {
        Successful,
        InvalidInput,
        ResourceNotFound
    }
}
