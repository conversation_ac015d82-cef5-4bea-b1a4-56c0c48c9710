﻿using Gateway.Biometrics.Application.InventoryAttribute.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.Biometrics.Api.Models.InventoryAttribute
{
    public static class InventoryAttributeResponseFactory
    {
        public static ObjectResult CreateInventoryAttributeResponse(CreateInventoryAttributeResult result)
        {
            switch (result.Status)
            {
                case CreateInventoryAttributeStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.SUCCESS,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_CREATED),
                            Message = result.Message,
                            Data = new
                            {
                                Id = result.Id
                            }
                        })
                        { StatusCode = HttpStatusCodes.Created };
                case CreateInventoryAttributeStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.INPUT_ERROR,
                            Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                            Message = result.Message,
                            ValidationMessages = result.ValidationMessages
                        })
                        { StatusCode = HttpStatusCodes.InvalidInput };

                case CreateInventoryAttributeStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.ResourceExist };
                default:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.FAILED),
                            Message = ServiceResources.FAILED
                        })
                        { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult UpdateInventoryAttributeResponse(UpdateInventoryAttributeResult result)
        {
            switch (result.Status)
            {
                case UpdateInventoryAttributeStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_UPDATED),
                        Message = result.Message,
                        Data = result.Id
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case UpdateInventoryAttributeStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case UpdateInventoryAttributeStatus.InventoryAttributeNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INVENTORY_ATTRIBUTE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.INVENTORY_ATTRIBUTE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateInventoryAttributeStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult DeleteInventoryAttributeResponse(DeleteInventoryAttributeResult result)
        {
            switch (result.Status)
            {
                case DeleteInventoryAttributeStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.SUCCESS,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_DELETED),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.Ok };
                case DeleteInventoryAttributeStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.INPUT_ERROR,
                            Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                            Message = result.Message,
                            ValidationMessages = result.ValidationMessages
                        })
                        { StatusCode = HttpStatusCodes.InvalidInput };
                case DeleteInventoryAttributeStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.FAILED),
                            Message = ServiceResources.FAILED
                        })
                        { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult GetPaginatedInventoryAttributesResponse(GetPaginatedInventoryAttributesResult result)
        {
            switch (result.Status)
            {
                case GetPaginatedInventoryAttributesStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.SUCCESS,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                            Message = result.Message,
                            Data = result.InventoryAttributes
                    })
                        { StatusCode = HttpStatusCodes.Ok };
                case GetPaginatedInventoryAttributesStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.INPUT_ERROR,
                            Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                            Message = result.Message,
                            ValidationMessages = result.ValidationMessages
                        })
                        { StatusCode = HttpStatusCodes.InvalidInput };
                case GetPaginatedInventoryAttributesStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.FAILED),
                            Message = ServiceResources.FAILED
                        })
                        { StatusCode = HttpStatusCodes.InternalError };
            }
        }
    }
}