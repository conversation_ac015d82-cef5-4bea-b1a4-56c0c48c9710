﻿using System;
using System.Linq;
using FluentValidation;
using Gateway.Extensions;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Entity.Entities.Inventory;
using Gateway.Biometrics.Resources;
using Gateway.Validation;
using Gateway.Biometrics.Application.InventoryAttribute.DTO;

namespace Gateway.Biometrics.Application.InventoryAttribute.Validator
{
    internal class CreateInventoryAttributeValidator : AbstractValidator<CreateInventoryAttributeRequest>
    {
        public CreateInventoryAttributeValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.MaxLength.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.MaxLength)));
            });
            
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Status.IsNumeric())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Status)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.SortOrder.IsNumeric())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.SortOrder)));
            });


            RuleFor(p => p).Custom((item, context) =>
            {
                var maxLength = item.GetMaxLength(nameof(item.Name));
                if (item.Name?.Length > maxLength)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_MAX_LENGTH_ERROR, nameof(item.Name), maxLength));
            });

            RuleFor(p => p).Custom((item, context) => {
                
                if (item.FieldType.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.FieldType)));
            });

           RuleFor(p => p).Custom((item, context) => {
                
                if (item.DataType.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.DataType)));
            });


        }
    }

    internal class UpdateInventoryAttributeValidator : AbstractValidator<UpdateInventoryAttributeRequest>
    {
        public UpdateInventoryAttributeValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.MaxLength.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.MaxLength)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Status.IsNumeric())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Status)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.SortOrder.IsNumeric())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.SortOrder)));
            });


            RuleFor(p => p).Custom((item, context) =>
            {
                var maxLength = item.GetMaxLength(nameof(item.Name));
                if (item.Name?.Length > maxLength)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_MAX_LENGTH_ERROR, nameof(item.Name), maxLength));
            });

            RuleFor(p => p).Custom((item, context) => {

                if (item.FieldType.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.FieldType)));
            });

            RuleFor(p => p).Custom((item, context) => {

                if (item.DataType.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.DataType)));
            });
        }
    }
    internal class DeleteInventoryAttributeValidator : AbstractValidator<DeleteInventoryAttributeRequest>
    {
        public DeleteInventoryAttributeValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.InventoryAttributeId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.InventoryAttributeId)));
            });
        }
    }

    internal class GetPaginatedInventoryAttributesValidator : AbstractValidator<GetPaginatedInventoryAttributesRequest>
    {
        public GetPaginatedInventoryAttributesValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (string.IsNullOrWhiteSpace(item.Pagination.OrderBy))
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Pagination.OrderBy)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageSize.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageSize)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageNumber.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageNumber)));
            });
        }
    }
}