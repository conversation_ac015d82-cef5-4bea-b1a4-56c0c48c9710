﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Gateway.Biometrics.Application.Inventory.DTO
{
    public class BaseInventoryRequest : BaseServiceRequest
    {
        [Required]
        public int InventoryDefinitionId { get; set; }

        [Required]
        [StringLength(50)]
        public string SerialNumber { get; set; }

        [StringLength(1000)]
        public string Description { get; set; }

        [Required]
        public int Status { get; set; }

        public virtual List<InventoryIpCameraDto> IpCameras { get; set; }

        public BaseInventoryRequest()
        {

        }
    }


}

