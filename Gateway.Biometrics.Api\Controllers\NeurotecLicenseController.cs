﻿using AutoMapper;
using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;
using Gateway.Biometrics.Api.Models.Neurotec;
using Gateway.Biometrics.Application.Neurotec;
using Gateway.Biometrics.Application.Neurotec.DTO;
using Gateway.Extensions;

namespace Gateway.Biometrics.Api.Controllers
{
    //[Authorize]
    [Route("api")]
    [ApiController]
    public class NeurotecLicenseController : Controller
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private readonly INeurotecService _neurotecService;

        #region ctor

        public NeurotecLicenseController(IContext context, IMapper mapper, INeurotecService neurotecService)
        {
            _context = context;
            _mapper = mapper;
            _neurotecService = neurotecService;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Get cabin by id
        /// </summary>
        /// <param name="resourceId"></param>
        [HttpGet]
        [Route("NeurotecLicenses/get/{resourceId?}")]
        public async Task<IActionResult> GetNeurotecLicense(int resourceId)
        {
            var serviceRequest = new GetNeurotecLicenseRequest()
            {
                Context = _context,
                ResourceId = resourceId
            };

            var result = await _neurotecService.GetNeurotecLicense(serviceRequest);

            return NeurotecLicenseResponseFactory.GetNeurotecLicenseResponse(result);
        }


        /// <summary>
        /// Get paginated list of neurotec licenses
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Get paginated list of NeurotecLicenses", 
            Description = "Get paginated list of NeurotecLicenses")]
        [HttpPost]
        [Route("neurotecLicenses/search")]
        public async Task<IActionResult> GetPaginatedNeurotecLicenses(GetPaginatedNeurotecLicensesRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetPaginatedNeurotecLicensesRequest>(request);
            serviceRequest.Context = _context;

            var result = await _neurotecService.GetPaginatedNeurotecLicenses(serviceRequest);

            return NeurotecLicenseResponseFactory.GetPaginatedNeurotecLicensesResponse(result);
        }

        /// <summary>
        /// Update selected neurotec licenses
        /// </summary>
        /// <param name="resourceId"></param>  
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Update an existing neurotec licenses",
            Description = "Update an existing neurotec licenses")]
        [HttpPut]
        [Route("NeurotecLicenses/update/{resourceId?}")]
        public async Task<IActionResult> UpdateNeurotecLicense(int resourceId, UpdateNeurotecLicenseRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<UpdateNeurotecLicenseRequest>(request);         
            serviceRequest.Id = resourceId;
            serviceRequest.Context = _context;

            var result = await _neurotecService.UpdateNeurotecLicense(serviceRequest);

            return NeurotecLicenseResponseFactory.UpdateNeurotecLicenseResponse(result);
        }

        #endregion
    }
}