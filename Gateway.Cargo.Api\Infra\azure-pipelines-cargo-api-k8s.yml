trigger:
  branches:
    include:
      - development
      - staging
      - master

pool:
  name: LinuxPool

extends:
  template: /Infra/azure-pipelines-build-k8s-template-v2.yml
  ${{ if eq(variables['Build.SourceBranchName'], 'staging') }}:
    parameters:
      registry: harbor-sta
      repository: sta/gatewayportals-cargo-api
      dockerfile: Gateway.Cargo.Api/Dockerfile
      publishpath: Gateway.Cargo.Api/Infra/kustomize
  ${{ elseif eq(variables['Build.SourceBranchName'], 'master') }}:
    parameters:
      registry: harbor-prod
      repository: prod/gatewayportals-cargo-api
      dockerfile: Gateway.Cargo.Api/Dockerfile
      publishpath: Gateway.Cargo.Api/Infra/kustomize
  ${{ else }}:
    parameters:
      registry: harbor-dev
      repository: dev/gatewayportals-cargo-api
      dockerfile: Gateway.Cargo.Api/Dockerfile
      publishpath: Gateway.Cargo.Api/Infra/kustomize
