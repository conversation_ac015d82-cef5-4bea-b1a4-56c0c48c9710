﻿using Gateway.Biometrics.Application.Attributes;
using Gateway.Biometrics.Resources;
using System.ComponentModel;

namespace Gateway.Biometrics.Application.Enums
{
    public static class Enums
    {
        public enum VisaCategoryType
        {
            [LocalizedDescription(nameof(EnumResources.Unspecified))]
            Unspecified = 0,
            [LocalizedDescription(nameof(EnumResources.Touristic))]
            Touristic = 1,
            [LocalizedDescription(nameof(EnumResources.Health))]
            Health = 2,
            [LocalizedDescription(nameof(EnumResources.FamilyAndFriendVisit))]
            FamilyAndFriendVisit = 3,
            [LocalizedDescription(nameof(EnumResources.Business))]
            Business = 4,
            [LocalizedDescription(nameof(EnumResources.CulturalSportive))]
            CulturalSportive = 5,
            [LocalizedDescription(nameof(EnumResources.Student))]
            Student = 6,
            [LocalizedDescription(nameof(EnumResources.WorkPermit))]
            WorkPermit = 7,
            [LocalizedDescription(nameof(EnumResources.Transit))]
            Transit = 8,
            [LocalizedDescription(nameof(EnumResources.FamilyUnion))]
            FamilyUnion = 9,
            [LocalizedDescription(nameof(EnumResources.Driver))]
            Driver = 10,
            [LocalizedDescription(nameof(EnumResources.Consenting))]
            Consenting = 11,
            [LocalizedDescription(nameof(EnumResources.Servant))]
            Servant = 12,
            [LocalizedDescription(nameof(EnumResources.Montage))]
            Montage = 13,
            [LocalizedDescription(nameof(EnumResources.NonApplicationInsurance))]
            NonApplicationInsurance = 14,
            [LocalizedDescription(nameof(EnumResources.EntryBanned))]
            EntryBanned = 15,
            [LocalizedDescription(nameof(EnumResources.ApprovedWorkPermit))]
            ApprovedWorkPermit = 16,
            [LocalizedDescription(nameof(EnumResources.VisaTransfer))]
            VisaTransfer = 17,
            [LocalizedDescription(nameof(EnumResources.ConferenceFair))]
            ConferenceFair = 18,
            [LocalizedDescription(nameof(EnumResources.MinistryOfHealthApplication))]
            MinistryOfHealthApplication = 19,
            [LocalizedDescription(nameof(EnumResources.Companion))]
            Companion = 20,
            [LocalizedDescription(nameof(EnumResources.PatientCompanion))]
            PatientCompanion = 21,
            [LocalizedDescription(nameof(EnumResources.StudentCompanion))]
            StudentCompanion = 22,
            [LocalizedDescription(nameof(EnumResources.WorkPermitCompanion))]
            WorkPermitCompanion = 23
		}

        public enum ApplicationType
        {
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeNormal))]
            Normal = 1,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeFree))]
            Free = 2,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationInsurance))]
            NonApplicationInsurance = 3,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPcr))]
            NonApplicationPcr = 4,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeTurquois))]
            Turquois = 5,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeTurquoisPremium))]
            TurquoisPremium = 6,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeTurquoisGratis))]
            TurquoisGratis = 7,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPrintOut))]
            NonApplicationPrintOut = 8,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPhotocopy))]
            NonApplicationPhotocopy = 9,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPhotograph))]
            NonApplicationPhotograph = 10
        }

        public enum ChannelType
        {
            Portal = 1,
            B2B = 2,
            Mobile = 3
        }

        public enum ApplicantType
        {
            [LocalizedDescription(nameof(EnumResources.ApplicantTypeIndividual))]
            Individual = 1,
            [LocalizedDescription(nameof(EnumResources.ApplicantTypeFamily))]
            Family = 2,
            [LocalizedDescription(nameof(EnumResources.ApplicantTypeGroup))]
            Group = 3,
            [LocalizedDescription(nameof(EnumResources.ApplicantTypeRepresentative))]
            Representative = 5
        }

        public enum VasType
        {
            [LocalizedDescription(nameof(EnumResources.PrimeTime))]
            PrimeTime = 1,
            [LocalizedDescription(nameof(EnumResources.VIP))]
            Vip = 2,
            [LocalizedDescription(nameof(EnumResources.Platinum))]
            Platinum = 3,
            [LocalizedDescription(nameof(EnumResources.MBS))]
            Mbs = 4,
            [LocalizedDescription(nameof(EnumResources.DocumentEditingService))]
            DocumentEditingService = 5,
            [LocalizedDescription(nameof(EnumResources.FlexibleAppointment))]
            FlexibleAppointment = 6
        }

        public enum SlotType
        {
            [LocalizedDescription(nameof(EnumResources.Individual))]
            Individual = 1,
            [LocalizedDescription(nameof(EnumResources.Agency))]
            Agency = 2
        }

        public enum ApplicationStatus
        {
            [LocalizedDescription(nameof(EnumResources.ApplicationStatusActive))]
            Active = 1,
            [LocalizedDescription(nameof(EnumResources.ApplicationStatusCancelled))]
            Cancelled = 2,
            [LocalizedDescription(nameof(EnumResources.ApplicationStatusPartiallyRefunded))]
            PartiallyRefunded = 3
        }

        public enum Gender
        {
            [LocalizedDescription(nameof(EnumResources.Male))]
            Male = 1,
            [LocalizedDescription(nameof(EnumResources.Female))]
            Female = 2
        }
        public enum RelationShip
        {
            [LocalizedDescription(nameof(EnumResources.Wife))]
            Wife = 1,
            [LocalizedDescription(nameof(EnumResources.Child))]
            Child = 2
        }

        public enum ApplicationStatusType
        {
            ApplicationTaken = 1,
            DataDone = 2,
            SendToEmbassy = 3,
            HandDeliveredToApplicant = 15,
            OutscanToCourier = 18,
            BiometricEnrollmentDone = 22,
            OutscanPassportWithRejectionToCourier = 25,
            ReceivedAtVisaCenter = 31,
            WaitingApproval = 34,
            FileWithdrewAccordingtoCustomerRequest = 35

        }

        public enum IncorrectApplicationStatusReason
        {
            [LocalizedDescription(nameof(EnumResources.WithdrawalByApplicant))]
            WithdrawalByApplicant = 1,
            [LocalizedDescription(nameof(EnumResources.ConsulateDecision))]
            ConsulateDecision = 2,
            [LocalizedDescription(nameof(EnumResources.ConsulateCanceledRefund))]
            ConsulateCanceledRefund = 3,
            [LocalizedDescription(nameof(EnumResources.OfficeWaitingForMissingDocuments))]
            OfficeWaitingForMissingDocuments = 4,
            [LocalizedDescription(nameof(EnumResources.Other))]
            Other = 5
        }

        public enum ApplicationStatusTrackingView
        {
            [LocalizedDescription(nameof(EnumResources.ApplicationTaken))]
            ApplicationTaken,
            [LocalizedDescription(nameof(EnumResources.ApplicationUnderEvaluation))]
            ApplicationUnderEvaluation,
            [LocalizedDescription(nameof(EnumResources.HandDeliveredToTheApplicant))]
            HandDeliveredToTheApplicant,
            [LocalizedDescription(nameof(EnumResources.DeliveredToCargo))]
            DeliveredToCargo,
            [LocalizedDescription(nameof(EnumResources.ReceivedAtVisaCenter))]
            ReceivedAtVisaCenter,
            [LocalizedDescription(nameof(EnumResources.FileWithdrewAccordingtoCustomerRequest))]
            FileWithdrewAccordingtoCustomerRequest
        }

    } 
}
