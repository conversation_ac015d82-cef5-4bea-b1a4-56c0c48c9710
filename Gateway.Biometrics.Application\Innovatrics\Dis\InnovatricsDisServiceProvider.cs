﻿using Gateway.Biometrics.Application.Innovatrics.DTO;
using Gateway.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using static Org.BouncyCastle.Math.EC.ECCurve;

namespace Gateway.Biometrics.Application.Innovatrics.Dis
{
    public class InnovatricsDisServiceProvider : IInnovatricsDisServiceProvider
    {
        private WebHeaderCollection _headers;
        private DisServiceConfig _config;
        private DisEndPointRouting _routing;

        public void Initialize<TConfig>(TConfig config)
        {
            _config = config as DisServiceConfig;

            if (_config == null)
                throw new Exception();

            _headers = new WebHeaderCollection
            {
                { "Authorization", $"Bearer {_config.Token}" }
            };

            _routing = new DisEndPointRouting(_config.ServiceUrl, _config.ApiVersion);
        }

        public async Task<CreateFaceResult> Create(string base64Image)
        {
            var request = new CreateFaceRequest() { image = new CreateFaceRequestImage() { data = base64Image } };
            var response = await RestHttpClient.Create().Post<CreateFaceResult>(_routing.Create, _headers, request);


            if (response == null)
            {
                return new CreateFaceResult()
                {
                    Status = CreateFaceStatus.ExternalServiceError,
                };
            }
            else if (response.Detection == null && response.ErrorCode == null)
            {
                return new CreateFaceResult()
                {
                    Status = CreateFaceStatus.ExternalServiceError,
                };
            }

            return response;
        }

        public async Task<DeleteFaceResult> Delete(string faceId)
        {
            _routing.FaceId = faceId;
            var response = await RestHttpClient.Create().Delete<DeleteFaceResult>(_routing.Delete, _headers);

            return response;
        }

        public async Task<FaceGlassesResult> Glasses(string faceId)
        {
            _routing.FaceId = faceId;
            var response = await RestHttpClient.Create().Get<FaceGlassesResult>(_routing.Glasses, _headers);

            if (response == null)
            {
                return new FaceGlassesResult()
                {
                    Status = FaceGlassesStatus.ExternalServiceError,
                };
            }

            return response;
        }

        public async Task<FaceQualityResult> Quality(string faceId)
        {
            _routing.FaceId = faceId;
            var response = await RestHttpClient.Create().Get<FaceQualityResult>(_routing.Quality, _headers);

            if (response == null)
            {
                return new FaceQualityResult()
                {
                    Status = FaceQualityStatus.ExternalServiceError,
                };
            }

            return response;
        }
    }
}
