﻿using System.Collections.Generic;

namespace Gateway.Biometrics.Application.Category.DTO
{
    public class GetPaginatedCategoriesResult : BaseServiceResult<GetPaginatedCategoriesStatus>
    {
        public IEnumerable<GenericAttributeDto> Categories { get; set; }
    }
    
    public enum GetPaginatedCategoriesStatus
    {
        Successful,
        InvalidInput,
        ResourceNotFound
    }
}
