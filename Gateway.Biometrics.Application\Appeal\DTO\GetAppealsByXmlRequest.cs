﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Appeal.DTO
{
    public class GetAppealsByXmlRequest : BaseServiceRequest
    {
        public string PassportNumber { get; set; }

        public string Name { get; set; }

        public string Surname { get; set; }

        public int Gender { get; set; }

        public string MotherName { get; set; }

        public string FatherName { get; set; }

        public DateTime BirthDate { get; set; }

        public string MaidenName { get; set; }

    }
}
