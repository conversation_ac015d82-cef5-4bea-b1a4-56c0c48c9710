﻿namespace Gateway.Cargo.LastMile.Dto.Result
{
    public class LastMileTrackingResult : LastMileBaseServiceResult<BaseLastMileStatus, LastMileTrackingResult.TrackingResult>
    {
        public class TrackingResult
        {
            public string Id { get; set; }
            public string TrackNumber { get; set; }
            public string ShipmentId { get; set; }
            public string RecipientName { get; set; }
            public List<TrackingHistory> PackageTrackings { get; set; }
            public string Source { get; set; }
            public string Destination { get; set; }

            public class TrackingHistory
            {
                public string Date { get; set; }
                public bool IsLatest { get; set; }
                public TrackingHistoryStatus Status { get; set; }
                public class TrackingHistoryStatus
                {
                    public string Internal { get; set; }
                    public string External { get; set; }
                    public string Description { get; set; }
                }
            }
        }
    }
}
