﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Mapping\**" />
    <Content Remove="Mapping\**" />
    <EmbeddedResource Remove="Mapping\**" />
    <None Remove="Mapping\**" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.4" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="8.1.1" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Gateway.Biometrics.Application\Gateway.Biometrics.Application.csproj" />
  </ItemGroup>
</Project>