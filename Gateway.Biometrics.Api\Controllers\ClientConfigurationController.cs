﻿using Gateway.Biometrics.Api.Models.ClientConfiguration;
using Gateway.Biometrics.Application.ClientConfiguration;
using Gateway.Biometrics.Application.ClientConfiguration.DTO;
using Gateway.Biometrics.Core.Context;
using Gateway.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;
using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Resources;
using AutoMapper;
using Gateway.Biometrics.Api.Models.Cabin;
using Gateway.Biometrics.Application.Cabin.DTO;

namespace Gateway.Biometrics.Api.Controllers
{
    //[Authorize]
    [Route("api")]
    [ApiController]
    public class ClientConfigurationController : Controller
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private readonly IClientConfigurationService _clientConfigurationService;

        #region ctor

        public ClientConfigurationController(IContext context, IMapper mapper, IClientConfigurationService clientConfigurationService)
        {
            _context = context;
            _mapper = mapper;
            _clientConfigurationService = clientConfigurationService;
        }
        #endregion

        #region Public Methods

        /// <summary>
        /// Gets a client configuration with given host name
        /// </summary>
        [SwaggerOperation(Summary = "Gets a client configuration with given host name",
            Description = "Gets a client configuration with given host name")]
        [HttpGet]
        [Route("clientConfiguration/getConfigurationByHostName/{hostName}")]
        public async Task<IActionResult> GetClientConfigurationByHostName(string hostName)
        { 
            var request = new GetClientConfigurationByHostNameRequest
            {
                Context = _context,
                HostName = hostName
            };

            var result = await _clientConfigurationService.GetClientConfigurationByHostName(request);

            return ClientConfigurationResponseFactory.GetClientConfigurationByHostName(result);
        }

        /// <summary>
        /// Gets a client configuration with given host name
        /// </summary>
        [SwaggerOperation(Summary = "Gets a client configuration with given host name",
            Description = "Gets a client configuration with given host name")]
        [HttpGet]
        [Route("clientConfiguration/getConfigurationByHostName2/{hostName}")]
        public async Task<IActionResult> GetClientConfigurationByHostName2(string hostName)
        {
            var request = new GetClientConfigurationByHostNameRequest
            {
                Context = _context,
                HostName = hostName
            };

            var result = await _clientConfigurationService.GetClientConfigurationByHostName2(request);

            return ClientConfigurationResponseFactory.GetClientConfigurationByHostName(result);
        }

        /// <summary>
        /// Get ClientConfiguration by id
        /// </summary>
        /// <param name="resourceId"></param>
        [HttpGet]
        [Route("ClientConfigurations/get/{resourceId?}")]
        public async Task<IActionResult> GetClientConfiguration(int resourceId)
        {
            var serviceRequest = new GetClientConfigurationRequest()
            {
                Context = _context,
                ResourceId = resourceId
            };

            var result = await _clientConfigurationService.GetClientConfiguration(serviceRequest);

            return ClientConfigurationResponseFactory.GetClientConfigurationResponse(result);
        }



        /// <summary>
        /// Creates a new client configuration
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Creates a new client configuration",
            Description = "Creates a new client configuration")]
        [HttpPost]
        [Route("ClientConfigurations/create")]
        public async Task<IActionResult> CreateClientConfiguration(CreateClientConfigurationRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<CreateClientConfigurationRequest>(request);
            serviceRequest.Context = _context;

            var result = await _clientConfigurationService.CreateClientConfiguration(serviceRequest);

            return ClientConfigurationResponseFactory.CreateClientConfigurationResponse(result);
        }

        /// <summary>
        /// Delete an existing client configuration
        /// </summary>
        /// <param name="resourceId"></param>  
        [SwaggerOperation(Summary = "Delete an existing client configuration",
            Description = "Delete an existing client configuration")]
        [HttpDelete]
        [Route("ClientConfigurations/delete/{resourceId?}")]
        public async Task<IActionResult> DeleteClientConfiguration(int resourceId)
        {
            if (!resourceId.IsNumericAndGreaterThenZero())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = new DeleteClientConfigurationRequest
            {
                ClientConfigurationId = resourceId,
                Context = _context
            };

            var result = await _clientConfigurationService.DeleteClientConfiguration(serviceRequest);

            return ClientConfigurationResponseFactory.DeleteClientConfigurationResponse(result);
        }



        /// <summary>
        /// Get paginated list of client configurations
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Get paginated list of ClientConfigurations",
            Description = "Get paginated list of ClientConfigurations")]
        [HttpPost]
        [Route("ClientConfigurations/search")]
        public async Task<IActionResult> GetPaginatedClientConfigurations(GetPaginatedClientConfigurationsRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetPaginatedClientConfigurationsRequest>(request);
            serviceRequest.Context = _context;

            var result = await _clientConfigurationService.GetPaginatedClientConfigurations(serviceRequest);

            return ClientConfigurationResponseFactory.GetPaginatedClientConfigurationsResponse(result);
        }

        /// <summary>
        /// Update selected client configuration
        /// </summary>
        /// <param name="resourceId"></param>  
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Update an existing client configuration",
            Description = "Update an existed client configuration")]
        [HttpPut]
        [Route("ClientConfigurations/update/{resourceId?}")]
        public async Task<IActionResult> UpdateClientConfiguration(int resourceId, UpdateClientConfigurationRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<UpdateClientConfigurationRequest>(request);
            serviceRequest.ClientConfigurationId = resourceId;
            serviceRequest.Context = _context;

            var result = await _clientConfigurationService.UpdateClientConfiguration(serviceRequest);

            return ClientConfigurationResponseFactory.UpdateClientConfigurationResponse(result);
        }


        #endregion
    }
}
