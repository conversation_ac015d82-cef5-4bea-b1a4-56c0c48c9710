﻿using Gateway.Biometrics.Application.Cabin;
using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.Parameter;
using Gateway.Biometrics.Application.Parameter.DTO;
using Gateway.Biometrics.Core.Context;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Nest;
using Portal.Gateway.Contracts.Entities.Constants;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Api.Controllers
{
    [Route("api/health")]
    [ApiController]
    public class HealthController : Controller
    {
        private readonly IParameterService _parameterService;

        public HealthController(IParameterService parameterService)
        {
            _parameterService = parameterService;
        }

        [AllowAnonymous]
        [HttpGet("check")]
        public string Check()
        {
            return "ok";
        }

        [AllowAnonymous]
        [HttpGet("isready")]
        public async Task<string> IsReady()
        {
            var serviceRequest = new InventoryTypesRequestDto();

            var result = await _parameterService.GetInventoryTypesAsync(serviceRequest);

            return "ok";
        }
    }
}
