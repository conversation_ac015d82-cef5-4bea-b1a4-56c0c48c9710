﻿using Gateway.Biometrics.Application.Branch.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.Biometrics.Api.Models.Branch
{
    public static class BranchResponseFactory
    {
        public static ObjectResult BranchesResponse(BranchesResult result)
        {
            switch (result.Status)
            {
                case GetBranchesStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.Branches
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case GetBranchesStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.INPUT_ERROR,
                            Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                            Message = result.Message,
                            ValidationMessages = result.ValidationMessages
                        })
                        { StatusCode = HttpStatusCodes.InvalidInput };
                case GetBranchesStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                            Message = result.Message,
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                case GetBranchesStatus.BranchNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.BRANCH_NOT_FOUND),
                            Message = result.Message,
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

    }
}
