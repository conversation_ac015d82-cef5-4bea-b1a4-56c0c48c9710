﻿using System;
using System.Collections.Generic;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.InventoryDefinition.DTO
{
    public class GetPaginatedInventoryDefinitionsResult : BasePaginationServiceListResult<GetPaginatedInventoryDefinitionsStatus>
    {
        public IEnumerable<InventoryDefinitionDto> InventoryDefinitions { get; set; }
    }
    public enum GetPaginatedInventoryDefinitionsStatus
    {
        Successful,
        InvalidInput,
        ResourceNotFound
    }
}
