﻿using Gateway.Cargo.Dto.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Gateway.Cargo.Application.Report.Dto.Request
{
    public class BaseReportRequest<T> : BaseServiceRequest where T : class
    {
        public int UserId { get; set; }

        public T Request { get; set; }

        public byte? StatusId { get; set; }
    }

    public class ReportRequestByBranch
    {
        [Required]
        public int BranchId { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }
    }

    public class ReportRequestByBranches
    {
        [Required]
        public List<int> BranchIds { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }
    }
}
