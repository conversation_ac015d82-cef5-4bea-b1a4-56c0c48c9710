﻿using Microsoft.AspNetCore.Http;
using System;

namespace Gateway.Biometrics.Core.Context
{
    internal class Context : IContext
    {
        public string RequestId { get; } = $"{Guid.NewGuid():N}";
        public string TraceId { get; } = $"{Guid.NewGuid():N}";
        public string IpAddress { get; }
        public string UserAgent { get; }
        public string AcceptLanguage { get; }
        public int LanguageId { get; set; }
        public IIdentityContext Identity { get; }

        private Context()
        {
        }

        public Context(HttpContext context) : this(context.TraceIdentifier, new IdentityContext(context))
        {
            IpAddress = context.Connection.RemoteIpAddress.ToString();
            UserAgent = context.Request.Headers["User-Agent"].ToString();
            AcceptLanguage = context.Request.Headers["Accept-Language"].ToString();
            LanguageId = GetLanguageId();           
        }

        private Context(string traceId, IIdentityContext identity)
        {
            TraceId = traceId;
            Identity = identity;
        }       

        private int GetLanguageId()
        {
            switch (AcceptLanguage)
            {
                case "en":
                    return 2;
                case null:
                    return 1;
                case "tr-TR":
                    return 1;
                default:
                    return 1;
            }
        }

        public static IContext Empty => new Context();
    }
}
