﻿using AutoMapper;
using Gateway.Core.Responses;
using Gateway.External.Api.Models.Report;
using Gateway.External.Application.Report;
using Gateway.External.Application.Report.Dto.Requests;
using Gateway.External.Application.Report.Dto.Response;
using Gateway.External.Core.Context;
using Gateway.External.Resources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;

namespace Gateway.External.Api.Controllers
{
	[Authorize]
	[Route("api")]
	[ApiController]
	public class ReportController : Controller
	{
		private readonly IContext _context;
		private readonly IMapper _mapper;
		private readonly IReportService _reportService;

		#region ctor

		public ReportController(IContext context, IReportService reportService, IMapper mapper)
		{
			_context = context;
			_reportService = reportService;
			_mapper = mapper;
		}

		#endregion

		#region Public Methods

		/// <summary>
		/// Gets Reports by given report id 
		/// </summary>
		/// <param name="resourceId"></param>  
		[SwaggerOperation(Summary = "Gets Reports by given report id ",
			Description = "Gets Reports by given report id ")]
		[HttpPost]
		[Route("reports/{resourceId}")]
		public async Task<IActionResult> GetReport(int resourceId, [FromBody] GetReportRequestModel requestModel)
		{
			if (requestModel == null)
				return BaseResponseFactory.CreateResponse(new GetReportResult()
				{
					Status = GetReportStatus.BadRequest,
					Message = ServiceResources.INVALID_REQUEST
				});

			var serviceRequest = _mapper.Map<GetReportRequest>(requestModel);
			serviceRequest.ResourceId = resourceId;
			serviceRequest.Context = _context;

			var result = await _reportService.GetReport(serviceRequest);

			return result.Status != GetReportStatus.Successful ?
				BaseResponseFactory.CreateResponse(result) :
				File(result.FileContent, result.ContentType, result.FileName);
		}

		#endregion
	}
}
