﻿using FluentValidation;
using Gateway.Biometrics.Application.Category.DTO;
using Gateway.Extensions;
using Gateway.Biometrics.Resources;

namespace Gateway.Biometrics.Application.Category.Validator
{
    internal class CreateCategoryValidator : AbstractValidator<CreateCategoryRequest>
    {
        public CreateCategoryValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Name.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Name)));
            });
            
            RuleFor(p => p).Custom((item, context) =>
            {
                var maxLength = item.GetMaxLength(nameof(item.Description));
                if (item.Description?.Length > maxLength)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_MAX_LENGTH_ERROR, nameof(item.Description), maxLength));
            });

        }
    }

    internal class UpdateCategoryValidator : AbstractValidator<UpdateCategoryRequest>
    {
        public UpdateCategoryValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Name.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Name)));
            });
            
            RuleFor(p => p).Custom((item, context) =>
            {
                var maxLength = item.GetMaxLength(nameof(item.Description));
                if (item.Description?.Length > maxLength)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_MAX_LENGTH_ERROR, nameof(item.Description), maxLength));
            });
        }
    }
   
    internal class DeleteCategoryValidator : AbstractValidator<DeleteCategoryRequest>
    {
        public DeleteCategoryValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.CategoryId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.CategoryId)));
            });
        }
    }

    internal class GetPaginatedCategoriesValidator : AbstractValidator<GetPaginatedCategoriesRequest>
    {
        public GetPaginatedCategoriesValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (string.IsNullOrWhiteSpace(item.Pagination.OrderBy))
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Pagination.OrderBy)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageSize.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageSize)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageNumber.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageNumber)));
            });
        }
    }
}