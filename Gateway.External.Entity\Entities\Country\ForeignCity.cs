﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gateway.External.Entity.Entities.Country
{
	public class ForeignCity
	{
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int Id { get; set; }

		[Required]
		public int CountryId { get; set; }

		[Required]
		public string Name { get; set; }

		public string PostalCode { get; set; }

		public bool IsActive { get; set; }

		public bool IsDeleted { get; set; }
	}
}
