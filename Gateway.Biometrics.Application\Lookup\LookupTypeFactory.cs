﻿using System;
using System.Collections.Generic;

namespace Gateway.Biometrics.Application.Lookup
{
    internal static class LookupTypeFactory
    {
        private static Dictionary<int, Type> _handler;

        public static Type GetInstance(int typeId)
        {
            if (IsHandlerNull()) PopulateHandler();

            return _handler[typeId];
        }

        public static bool IsTypeExist(int typeId)
        {
            if (IsHandlerNull()) PopulateHandler();

            return _handler.ContainsKey(typeId);
        }

        private static bool IsHandlerNull()
        {
            return _handler == null;
        }

        private static void PopulateHandler()
        {
            _handler = new Dictionary<int, Type>
            {
                { 1, typeof(Enums.Enums.VisaCategoryType) },
                { 2, typeof(Enums.Enums.ApplicationType) },
                { 3, typeof(Enums.Enums.ApplicantType) },
                { 4, typeof(Enums.Enums.VasType) },
                { 5, typeof(Enums.Enums.SlotType) },
                { 6, typeof(Enums.Enums.ApplicationStatus) },
                { 7, typeof(Enums.Enums.Gender) },
                { 8, typeof(Enums.Enums.RelationShip) },
            };
        }
    }
}
