﻿using Gateway.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Innovatrics.DTO
{
    public class CreateFaceRequest : BaseHttpRequest
    {
        public CreateFaceRequestImage image { get; set; }
        public CreateFaceRequestDetection detection { get; set; } = new CreateFaceRequestDetection();
    }

    public class CreateFaceRequestImage
    {
        public string data { get; set; }
    }

    public class CreateFaceRequestDetection
    {
        public string mode { get; set; } = "STRICT";
        public FaceSizeRatio faceSizeRatio { get; set; } = new FaceSizeRatio();
    }

    public class FaceSizeRatio
    {
        public double min { get; set; } = 0.05;
        public double max { get; set; } = 0.5;
    }
}
