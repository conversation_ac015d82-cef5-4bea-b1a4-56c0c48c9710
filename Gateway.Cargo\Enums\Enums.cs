﻿using System.ComponentModel;
using Gateway.Cargo.Attribute;
using Gateway.Cargo.Resources;

namespace Gateway.Cargo.Enums
{
    public static class Enums
    {
        public enum CargoTrackingStatus
        {
            [LocalizedDescription(nameof(EnumResources.OrderProcessedReadyForUPS))]
            OrderProcessedReadyForUPS = 3,
            [LocalizedDescription(nameof(EnumResources.InTransit))]
            InTransit = 5,
            [LocalizedDescription(nameof(EnumResources.OnVehicleForDelivery))]
            OnVehicleForDelivery = 6,
            [LocalizedDescription(nameof(EnumResources.Delivered))]
            Delivered = 11,
            [LocalizedDescription(nameof(EnumResources.Waiting))]
            Waiting = 98, //LastMile
            [LocalizedDescription(nameof(EnumResources.UnderProcessing))]
            UnderProcessing = 99, //LastMile
            [LocalizedDescription(nameof(EnumResources.UndefinedStatus))]
            UndefinedStatus = 100, //LastMile


        }

        public enum CargoProviderType
        {
            [LocalizedDescription(nameof(EnumResources.UPS))]
            UPS = 1,
            [LocalizedDescription(nameof(EnumResources.LastMile))]
            LastMile = 2
        }

        public enum Code : byte
        {
	        [LocalizedDescription(nameof(EnumResources.UpsSaver))]
	        UpsSaver = 65
        }

        public enum LastMileStatus
        {
            [Description("b0e8cb8d-2c6b-4dcb-b67a-4bd033065571")]
            ReadyToShip,
            [Description("540545f0-5559-4efe-9662-63a47a23f16c")]
            UnderProcessing,
        }
    }
}
