﻿using Gateway.Core.CustomAttributes;
using Gateway.Core.Responses;
using System.Collections.Generic;
using Gateway.Cargo.Resources;
using Gateway.Core.Responses.Models;

namespace Gateway.Cargo.Application.CargoTrack.Dto.Response
{
    public class GetCargoAreaResult : BaseServiceResult<GetCargoAreaStatus>
    {
        public List<GetCargoAreaResultDto> AreaList { get; set; }
    }

    public class GetCargoAreaResultDto
    {
        public string Id { get; set; }
        public string Title { get; set; }
    }

    public enum GetCargoAreaStatus
    {
        [CustomHttpStatus(Code = "SUCCESS", Resources = typeof(ServiceResources), Status = "SUCCESS", StatusCode = HttpStatusCodes.Ok)]
        Successful,

        [CustomHttpStatus(Code = "INVALID_INPUT_ERROR", Resources = typeof(ServiceResources), Status = "INVALID_INPUT_ERROR", StatusCode = HttpStatusCodes.InvalidInput)]
        InvalidInput,

        [CustomHttpStatus(Code = "BAD_REQUEST", Resources = typeof(ServiceResources), Status = "BAD_REQUEST", StatusCode = HttpStatusCodes.BadRequest)]
        BadRequest,

        [CustomHttpStatus(Code = "RESOURCE_NOT_FOUND", Resources = typeof(ServiceResources), Status = "RESOURCE_NOT_FOUND", StatusCode = HttpStatusCodes.ResourceNotFound)]
        NotFound,

        [CustomHttpStatus(Code = "INTERNAL_SERVICE_ERROR", Resources = typeof(ServiceResources), Status = "INTERNAL_SERVICE_ERROR", StatusCode = HttpStatusCodes.InternalServerError)]
        InternalServerError,

        [CustomHttpStatus(Code = "EXTERNAL_SERVICE_ERROR", Resources = typeof(ServiceResources), Status = "EXTERNAL_SERVICE_ERROR", StatusCode = HttpStatusCodes.ServiceUnavailable)]
        ExternalServiceError
    }

}
