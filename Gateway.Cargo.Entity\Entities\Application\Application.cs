﻿using Gateway.Cargo.Entity.Entities.Cargo;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Gateway.Cargo.Entity.Entities.Branch;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Gateway.Cargo.Entity.Entities.Sap;

namespace Gateway.Cargo.Entity.Entities.Application
{
    public class Application
    {
        public Application()
        {
            IsActive = true;
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public int BranchApplicationCountryId { get; set; }

        [Required]
        public int ApplicantTypeId { get; set; }

        [Required]
        public int ApplicationTypeId { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string PassportNumber { get; set; }

        public DateTime? PassportExpireDate { get; set; }

        [Required]
        public int ApplicationPassportStatusId { get; set; }

        public int? AgencyId { get; set; }

        public int? CustomerId { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string Name { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string Surname { get; set; }

        [Required]
        public DateTime BirthDate { get; set; }

        [Required]
        public int GenderId { get; set; }

        [Required]
        public int NationalityId { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string Email { get; set; }

        [Required]
        public string PhoneNumber1 { get; set; }
        public string PhoneNumber2 { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(200)]
        public string Address { get; set; }

        [Required]
        public int ApplicationStatusId { get; set; }

        public int? RelationalApplicationId { get; set; }

        public int? RelationShipId { get; set; }

        [Required]
        public DateTimeOffset ApplicationTime { get; set; }

        [Column(TypeName = "citext"), MaxLength(500)]
        public string Note { get; set; }

        [Required]
        public int StatusId { get; set; }

        [Required]
        public int CreatedBy { get; set; }

        public int? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public int? PreApplicationApplicantId { get; set; }

        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }

        public ApplicationStatus ApplicationStatus { get; set; }
        public Agency.Agency Agency { get; set; }
        public ICollection<ApplicationStatusHistory> ApplicationStatusHistories { get; set; }
        public ICollection<ApplicationCancellation> ApplicationCancellations { get; set; }
        public ICollection<ApplicationExtraFee> ApplicationExtraFees { get; set; }
        public ICollection<SapApplicationOrder> SapApplicationOrders { get; set; }
        public ICollection<CargoTrack> CargoTracks { get; set; }

        public BranchApplicationCountry BranchApplicationCountry { get; set; }

        public Country.Country Nationality { get; set; }

        public User.User User { get; set; }
    }

    public class ApplicationEntityConfiguration : IEntityTypeConfiguration<Application>
    {
        public void Configure(EntityTypeBuilder<Application> builder)
        {
            builder
                .HasOne(m => m.Nationality)
                .WithMany(p => p.Applications)
                .HasForeignKey(d => d.NationalityId).OnDelete(DeleteBehavior.Restrict).IsRequired();

            builder
                .HasOne(m => m.ApplicationStatus)
                .WithMany(p => p.Applications)
                .HasForeignKey(d => d.ApplicationStatusId).OnDelete(DeleteBehavior.Restrict).IsRequired();

            builder
                .HasOne(m => m.User)
                .WithMany(p => p.Applications)
                .HasForeignKey(d => d.CreatedBy).OnDelete(DeleteBehavior.Restrict).IsRequired();
        }
    }
}
