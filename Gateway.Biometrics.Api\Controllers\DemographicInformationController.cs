﻿using System;
using AutoMapper;
using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Api.Models.DemographicInformation;
using Gateway.Biometrics.Application.DemographicInformation;
using Gateway.Biometrics.Application.DemographicInformation.DTO;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Resources;
using Gateway.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Api.Controllers
{
    [Authorize]
    [Route("api")]
    [ApiController]
    public class DemographicInformationController : Controller
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private readonly IDemographicInformationService _demographicInformationService;

        #region ctor

        public DemographicInformationController(IContext context, IMapper mapper, IDemographicInformationService demographicInformationService)
        {
            _context = context;
            _mapper = mapper;
            _demographicInformationService = demographicInformationService;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets demographic informations about application by passport number
        /// </summary>
        /// <param name="passportNumber"></param>  
        [SwaggerOperation(Summary = "Gets demographic informations about application by passport number",
            Description = "Gets demographic informations about application by passport number")]
        [HttpGet]
        [Route("biometrics/{passportNumber}")]
        public async Task<IActionResult> GetDemographicInformationByPassport(string passportNumber)
        {
            if (passportNumber.IsNullOrWhitespace())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = new GetDemographicInformationByPassportRequest
            {
                PassportNumber = passportNumber,
                Context = _context
            };

            var result = await _demographicInformationService.GetDemographicInformationByPassport(serviceRequest);

            return DemographicInformationResponseFactory.GetDemographicInformationByPassportResponse(result);
        }

        /// <summary>
        /// Gets demographic informations about application by passport number
        /// </summary>
        /// <param name="passportNumber"></param>  
        [SwaggerOperation(Summary = "Gets demographic informations about application by passport number",
            Description = "Gets demographic informations about application by passport number")]
        [HttpGet]
        [Route("biometrics/bypassportandnationality/{passportNumber}/{nationalityId}")]
        public async Task<IActionResult> GetDemographicInformationByPassportAndNationality(string passportNumber, string nationalityId)
        {
            if (passportNumber.IsNullOrWhitespace())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = new GetDemographicInformationByPassportAndNationalityRequest
            {
                PassportNumber = passportNumber,
                NationalityId = Int32.Parse(nationalityId),
                Context = _context
            };

            var result = await _demographicInformationService.GetDemographicInformationByPassportAndNationality(serviceRequest);

            return DemographicInformationResponseFactory.GetDemographicInformationByPassportAndNationalityResponse(result);
        }

        #endregion
    }
}
