﻿using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.Inventory.DTO
{

    public class GetInventoryResult : BaseServiceResult<GetInventoryStatus>
    {
        public InventoryDto Inventory { get; set; }
    }

  
    public enum GetInventoryStatus
    {
        Successful,
        InvalidInput,
        NotFound,
        InventoryNotFound,
        InventoryDefinitionNotFound,
    }
}
