﻿using System.Collections.Generic;

namespace Gateway.Biometrics.Api.Models
{
    public class BasePaginationApiResponse
    {
        public string Status { get; set; }
        public string Code { get; set; }
        public string Message { get; set; }
        public List<string> ValidationMessages { get; set; }
        public List<string> ErrorMessages { get; set; }
        public object Data { get; set; }

        public int TotalNumberOfPages { get; set; }
        public int TotalNumberOfRecords { get; set; }

        public BasePaginationApiResponse()
        {
            ValidationMessages = new List<string>();
            ErrorMessages = new List<string>();          
        }
    }
}
