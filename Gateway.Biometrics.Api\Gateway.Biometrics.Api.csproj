﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AspNetCoreHostingModel>OutOfProcess</AspNetCoreHostingModel>
    <EnvironmentName>__ASPNETCORE_ENVIRONMENT__</EnvironmentName>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
  </PropertyGroup>
  <ItemGroup>
    <None Include="..\.dockerignore" Link=".dockerignore">
      <DependentUpon>$(DockerDefaultDockerfile)</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.12" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.12" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.4" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="8.1.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="8.1.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="8.1.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="8.1.1" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Gateway.Biometrics.Application\Gateway.Biometrics.Application.csproj" />
    <ProjectReference Include="..\Gateway.Biometrics.Resources\Gateway.Biometrics.Resources.csproj" />
    <ProjectReference Include="..\Gateway.Extensions\Gateway.Extensions.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Content Update="web.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <None Update="iengine.lic">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>