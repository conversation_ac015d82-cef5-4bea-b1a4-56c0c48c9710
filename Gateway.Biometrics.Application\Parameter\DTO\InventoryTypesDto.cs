﻿using System.Collections.Generic;

namespace Gateway.Biometrics.Application.Parameter.DTO
{
    public class InventoryTypesDto
    {
        public IList<InventoryTypeSelectDto> InventoryTypes { get; set; }
    }
    public class InventoryTypeSelectDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    public class InventoryTypesRequestDto
    {
        //public int? CountryId { get; set; }
    }

    public class InventoryTypesResult : BaseServiceDataResult<InventoryTypesDto>
    {
        public int Id { get; set; }
    }
}
