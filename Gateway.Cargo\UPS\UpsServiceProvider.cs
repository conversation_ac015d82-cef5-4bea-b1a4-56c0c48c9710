﻿using Gateway.Cargo.Dto.Request;
using Gateway.Cargo.Dto.Response;
using Gateway.Cargo.UPS.Dto.Request;
using Gateway.Cargo.UPS.Dto.Response;
using Gateway.Http;
using System.Net;
using Gateway.Cargo.Resources;
using Portal.Gateway.Common.Utility.Extensions;
using Serilog;

namespace Gateway.Cargo.UPS
{
	public class UpsServiceProvider : IUpsServiceProvider
	{
		private UpsServiceConfig? _config;
        private WebHeaderCollection _headers;
        private string _baseAddress;
        private static readonly ILogger Logger = Log.ForContext<UpsServiceProvider>();

        public void Initialize<TConfig>(TConfig config)
		{
			_config = config as UpsServiceConfig;

			if (_config == null) throw new Exception();

            _baseAddress = _config.BaseAddress;

            _headers = new WebHeaderCollection
            {
                { "UserName", _config.UserName },
                { "Password", _config.Password },
                { "AccessLicenseNumber", _config.AccessLicenseNumber },
                { "transId", Guid.NewGuid().ToString() },
                { "transactionSrc", "testSrc" },
                { "Accept", "application/json" }
            };
        }

        public async Task<CargoTrackingResultDto> Tracking(string inquiryNumber, string packageId)
        {
            var url = $"{_baseAddress}track/v1/details/{inquiryNumber}";
            var response = await RestHttpClient.Create().Get<UPSTrackingResult>(url, _headers);

            if (response?.TrackResponse == null)
                return new CargoTrackingResultDto
                {
                    Status = response?.Response?.ResponseStatus?.Code == null ? GetTrackingStatus.InvalidInput : (GetTrackingStatus)response.Status,
                    Message = response?.Response?.Errors?.FirstOrDefault()?.Message
                };

            return new CargoTrackingResultDto
            {
                Status = GetTrackingStatus.Successful,
                CargoTrackingResult = new CargoTrackingResult
                {
                    InquiryNumber = response.TrackResponse?.Shipment.FirstOrDefault()?.InquiryNumber,
                    TrackingNumber = response.TrackResponse?.Shipment?.FirstOrDefault()?.Package?.FirstOrDefault()?.TrackingNumber,
					LastStatus = response.TrackResponse?.Shipment?.FirstOrDefault()?.Package?.FirstOrDefault()?.Activity?.Select(p => new TrackingStatus
                    {
                        Date = p.Date,
                        Time = p.Time,
                        Description = p.Status?.Description,
                        StatusCode = Convert.ToByte(p.Status?.StatusCode)
                    }).FirstOrDefault(),
                    Status = response.TrackResponse?.Shipment?.FirstOrDefault()?.Package?.FirstOrDefault()?.Activity?.Select(p => new TrackingStatus
                    {
                        Date = p.Date,
                        Time = p.Time,
                        Description = p.Status?.Description,
						StatusCode = Convert.ToByte(p.Status?.StatusCode) 
                    }).ToList()
                }
            };
        }

        public async Task<ShipmentResultDto> Shipment(ShipmentRequest request)
		{
			var shipperAddress = new List<string>
            {
                request.Branch.BranchAddress?.Length > 35
                    ? request.Branch.BranchAddress?.Substring(0, 35)
                    : request.Branch.BranchAddress
            };

            var shipper = new Shipper
			{
				Name = "Gateway" + "-" + request.Branch.BranchCityName,
				Address = new Dto.Request.Address
				{
					AddressLine = shipperAddress,
					City = request.Branch?.BranchCityName,
					CountryCode = request.Branch?.BranchCountryCode,
					//PostalCode = ""
				},
				Phone = new Phone { Number = request.Branch?.Phone.Trim('+', '(', ')').Replace(" ", string.Empty).Substring(0, request.Branch.Phone.Length - 1) },
				ShipperNumber = _config?.ShipperNumber
            };

            var name = request.Application.Name + " " + request.Application.Surname;

            var shipTo = new ShipTo();

            if (request.Application.NameOfSecondContactPerson != null)
            {
                var shipToAddress = new List<string>
                {
                    request.Application.Address.Length > 35
                    ? request.Application.Address.Substring(0, 35)
                    : request.Application.Address,
                    request.Application.Phone2
                };

                shipTo.AttentionName = request.Application.NameOfSecondContactPerson.Length > 35
                        ? request.Application.NameOfSecondContactPerson.Substring(0, 35)
                        : request.Application.NameOfSecondContactPerson;
                shipTo.Name = name.Length > 35
                        ? name.Substring(0, 35)
                        : name;
                shipTo.Address = new Dto.Request.Address
                {
                    AddressLine = shipToAddress,
                    City = request.Application.City,
                    CountryCode = request.Branch?.BranchCountryCode,
                    PostalCode = request.Application.PostalCode?.TrimStart(new[] { '0' })
                };

                shipTo.Phone = new Phone { Number = request.Application.Phone1 };
            }

            else
            {
                var shipToAddress = new List<string>
                {
                    request.Application.Address.Length > 35
                    ? request.Application.Address.Substring(0, 35)
                    : request.Application.Address,
                    request.Application.Phone2 != string.Empty
                    ? request.Application.Phone1 + " - " + request.Application.Phone2
                    : request.Application.Phone1
                };

                shipTo.Name = name.Length > 35
                        ? name.Substring(0, 35)
                        : name;
                shipTo.Address = new Dto.Request.Address
                {
                    AddressLine = shipToAddress,
                    City = request.Application.City,
                    CountryCode = request.Branch?.BranchCountryCode,
                    PostalCode = request.Application.PostalCode?.TrimStart(new[] { '0' })
                };
            }

			var shipFrom = new ShipFrom
			{
				Name = "Gateway" + "-" + request.Branch?.BranchCityName,
				Address = new Dto.Request.Address
                {
					AddressLine = shipperAddress,
					City = request.Branch?.BranchCityName, 
					CountryCode = request.Branch?.BranchCountryCode,
					//PostalCode = 
				},
				Phone = new Phone { Number = request.Application.Phone1 }
			};

			var payment = new PaymentInformation
			{
				ShipmentCharge = new ShipmentCharge
				{
					Type = "01",
					BillShipper = new BillShipper { AccountNumber = _config?.ShipperNumber }  
				}
			};

            var applicationId = request.ApplicationId.ToApplicationNumber();

            var referenceNumber = new ReferenceNumber
            {
                BarCodeIndicator = applicationId,
                Value = applicationId
            };

            var service = new Service
			{
				Code = ((byte)Enums.Enums.Code.UpsSaver).ToString(), 
				Description = EnumResources.UpsSaver
			};

			var package = new Dto.Request.Package
			{
				Description = "Passport",
				Packaging = new Packaging
				{
					Code = "01"
				},
				//PackageWeight = new PackageWeight
				//{
				//	UnitOfMeasurement = new UnitOfMeasurement { Code = "00" },
				//	Weight = "0.1"
				//}
			};

			var packList = new List<Dto.Request.Package> { package };

			var shipmentRequest = new UPSShipmentRequest
			{
				ShipmentRequest = new UPSShipment
				{
					Shipment = new Dto.Request.Shipment
					{
						Shipper = shipper,
						ShipTo = shipTo,
						ShipFrom = shipFrom,
						Package = packList,
						Service = service,
						PaymentInformation = payment,
                        ReferenceNumber = referenceNumber
                    },
					LabelSpecification = new LabelSpecification
					{
						LabelImageFormat = new LabelImageFormat { Code = "GIF" }
					}
				}
			};

            var url = $"{_baseAddress}ship/v1/shipments";
            var response = await RestHttpClient.Create().Post<UPSShipmentResult>(url, _headers, shipmentRequest, false);

            if (response?.ShipmentResponse?.Response?.ResponseStatus?.Code != "1")
            {
                Logger.Error($"Error-Code: {response?.Response?.Errors?.FirstOrDefault()?.Code}, Error-Message: {response?.Response?.Errors?.FirstOrDefault()?.Message}, BranchId: {request.Context.Identity.BranchId}, UserId: {request.Context.Identity.UserId}");

                return new ShipmentResultDto
                {
                    Status = response?.Response?.ResponseStatus?.Code == null ? GetShipmentStatus.InvalidInput : (GetShipmentStatus)response.Status,
                    Message = response?.Response?.Errors?.FirstOrDefault()?.Message,
                    ShipmentResult = new ShipmentResult { ApplicationId = request.ApplicationId }
                };
            }

            return new ShipmentResultDto
            {
				Status = GetShipmentStatus.Successful,
                ShipmentResult = new ShipmentResult
				{
					ApplicationId = request.ApplicationId,
					GraphicImage = response.ShipmentResponse.ShipmentResults.PackageResults.ShippingLabel.GraphicImage,
					TrackingNumber = response.ShipmentResponse.ShipmentResults.PackageResults.TrackingNumber
				}
			};
		}

        public async Task<ShipmentResultDto> LabelRecovery(LabelRecoveryServiceRequest request)
        {
            var labelRecoveryRequest = new UPSLabelRecoveryRequest
            {
                LabelRecoveryRequest = new LabelRecoveryRequest
                {
                    LabelSpecification = new LabelSpecification
                    {
                        LabelImageFormat = new LabelImageFormat { Code = "GIF" }
                    },
                    Translate = new Translate
                    {
                        LanguageCode = "eng",
                        DialectCode = "US",
                        Code = "01"
                    },
                    TrackingNumber = request.TrackingNumber
                }
            };

            var url = $"{_baseAddress}ship/v1/shipments/labels";
            var response = await RestHttpClient.Create().Post<UPSLabelRecoveryResponse>(url, _headers, labelRecoveryRequest, false);

            if (response?.LabelRecoveryResponse?.Response?.ResponseStatus?.Code != "1")
            {
                Logger.Error($"Error-Code: {response?.Response?.Errors?.FirstOrDefault()?.Code}, Error-Message: {response?.Response?.Errors?.FirstOrDefault()?.Message}");

                return new ShipmentResultDto
                {
                    Status = response?.Response?.ResponseStatus?.Code == null ? GetShipmentStatus.InvalidInput : (GetShipmentStatus)response.Status,
                    Message = response?.Response?.Errors?.FirstOrDefault()?.Message
                };
            }

            return new ShipmentResultDto
            {
                Status = GetShipmentStatus.Successful,
                ShipmentResult = new ShipmentResult
                {
                    GraphicImage = response.LabelRecoveryResponse.LabelResults.LabelImage.GraphicImage
                }
            };
        }

        public Task<GetAreaResultDto> GetArea(string governorateId)
        {
            throw new NotImplementedException();
        }

        public Task<UpdateStatusDto> UpdateStatus(UpdateStatusRequest request)
        {
            throw new NotImplementedException();
        }

        public Task<GetGovernorateResultDto> GetGovernorate()
        {
            throw new NotImplementedException();
        }
    }
}
