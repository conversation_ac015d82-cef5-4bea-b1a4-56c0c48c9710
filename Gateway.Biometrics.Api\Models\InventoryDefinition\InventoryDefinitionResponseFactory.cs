﻿using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.InventoryDefinition.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.Biometrics.Api.Models.InventoryDefinition
{
    public static class InventoryDefinitionResponseFactory
    {

        public static ObjectResult GetInventoryDefinitionResponse(GetInventoryDefinitionResult result)
        {
            switch (result.Status)
            {
                case GetInventoryDefinitionStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.InventoryDefinition
                    })
                    { StatusCode = HttpStatusCodes.Created };
                case GetInventoryDefinitionStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case GetInventoryDefinitionStatus.NotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INVENTORY_DEFINITION_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.INVENTORY_DEFINITION_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case GetInventoryDefinitionStatus.InventoryTypeNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INVENTORY_TYPE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.INVENTORY_TYPE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }


        public static ObjectResult CreateInventoryDefinitionResponse(CreateInventoryDefinitionResult result)
        {
            switch (result.Status)
            {
                case CreateInventoryDefinitionStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_CREATED),
                        Message = result.Message,
                        Data = new
                        {
                            Id = result.Id
                        }
                    })
                    { StatusCode = HttpStatusCodes.Created };
                case CreateInventoryDefinitionStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case CreateInventoryDefinitionStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult UpdateInventoryDefinitionResponse(UpdateInventoryDefinitionResult result)
        {
            switch (result.Status)
            {
                case UpdateInventoryDefinitionStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_UPDATED),
                        Message = result.Message,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case UpdateInventoryDefinitionStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case UpdateInventoryDefinitionStatus.InventoryDefinitionNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INVENTORY_DEFINITION_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.INVENTORY_DEFINITION_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateInventoryDefinitionStatus.InventoryTypeNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INVENTORY_TYPE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.INVENTORY_TYPE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateInventoryDefinitionStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult DeleteInventoryDefinitionResponse(DeleteInventoryDefinitionResult result)
        {
            switch (result.Status)
            {
                case DeleteInventoryDefinitionStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_DELETED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case DeleteInventoryDefinitionStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case DeleteInventoryDefinitionStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult GetPaginatedInventoryDefinitionsResponse(GetPaginatedInventoryDefinitionsResult result)
        {
            switch (result.Status)
            {
                case GetPaginatedInventoryDefinitionsStatus.Successful:
                    return new ObjectResult(new BasePaginationApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.InventoryDefinitions,
                        TotalNumberOfRecords = result.TotalNumberOfRecords,
                        TotalNumberOfPages = result.TotalNumberOfPages
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case GetPaginatedInventoryDefinitionsStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case GetPaginatedInventoryDefinitionsStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }
    }
}