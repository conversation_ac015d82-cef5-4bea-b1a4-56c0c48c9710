﻿using System.Collections.Generic;
using System;
using Gateway.Core.Pagination;
using Gateway.Biometrics.Application.Inventory.DTO;

namespace Gateway.Biometrics.Api.Models.Inventory
{
    public class BaseInventoryRequestModel
    {
        public int InventoryDefinitionId { get; set; }

        public string SerialNumber { get; set; }
        public string Description { get; set; }

        public int Status { get; set; }
        public virtual List<InventoryIpCameraDto> IpCameras { get; set; }

        public BaseInventoryRequestModel()
        {
        }
    }

    public class CreateInventoryRequestModel : BaseInventoryRequestModel
    {
    }

    public class UpdateInventoryRequestModel : BaseInventoryRequestModel
    {
    }

    public class DeleteInventoryRequestModel
    {
        public int InventoryId { get; set; }
    }

    public class DeleteInventoryApplicantRequestModel
    {
        public int InventoryId { get; set; }

    }

    public class GetPaginatedInventoriesRequestModel
    {
        public int? FilterInventoryTypeId { get; set; }
        public PaginationRequest Pagination { get; set; }
    }
}