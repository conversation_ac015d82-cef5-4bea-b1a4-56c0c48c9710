﻿using Gateway.Biometrics.Application.InventoryStatusLog.DTO;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.InventoryStatusLog
{
    public interface IInventoryStatusLogService
    {
        Task<GetInventoryStatusLogResult> GetInventoryStatusLog(GetInventoryStatusLogRequest request);
        Task<CreateInventoryStatusLogResult> CreateInventoryStatusLog(CreateInventoryStatusLogRequest request);
        Task<GetPaginatedInventoryStatusLogsResult> GetPaginatedInventoryStatusLogs(GetPaginatedInventoryStatusLogsRequest request);
        Task<GetLastInventoryStatusLogsForEachInventoriesOfCabinResult> GetLastInventoryStatusLogsForEachInventoriesOfCabin(GetLastInventoryStatusLogsForEachInventoriesOfCabinRequest request);
    }
}