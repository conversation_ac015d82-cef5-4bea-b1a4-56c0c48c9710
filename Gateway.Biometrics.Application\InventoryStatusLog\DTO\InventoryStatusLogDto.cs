﻿using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Entity.Enum;
using System;

namespace Gateway.Biometrics.Application.InventoryStatusLog.DTO
{
    public class InventoryStatusLogDto
    {
        public int Id { get; set; }
        public string HostName { get; set; }
        public int InventoryId { get; set; }
        public int CabinId { get; set; }
        public int OfficeId { get; set; }
        public int CountryId { get; set; }
        public int ProvinceId { get; set; }
        public string ErrorMessage { get; set; }
        public InventoryStatusEnum Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public InventoryDto Inventory { get; set; }

    }
}