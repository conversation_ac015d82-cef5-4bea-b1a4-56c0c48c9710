﻿using FluentValidation;
using Gateway.Biometrics.Application.Appeal.DTO;
using Gateway.Biometrics.Application.Appeal.Validator;
using Gateway.Biometrics.Application.Innovatrics.DTO;
using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Resources;
using Gateway.Extensions;

namespace Gateway.Biometrics.Application.Innovatrics.Validator
{

    public class AnalyseFaceValidator : AbstractValidator<AnalyseFaceRequest>
    {
        public AnalyseFaceValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.Base64Image.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Base64Image)));
            });

        }
    }
}
