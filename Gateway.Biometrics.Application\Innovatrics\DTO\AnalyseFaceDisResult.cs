﻿using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Application;

namespace Gateway.Biometrics.Application.Innovatrics.DTO
{
    public class AnalyseFaceDisResult : BaseServiceResult<AnalyseFaceDisResultStatus>
    {
        
        public FaceQualityResult IFaceData { get; set; }
    }

    public enum AnalyseFaceDisResultStatus
    {
        Successful,
        InvalidInput,
        ThereIsNoFace,
        ServerError,
        CreateFaceError,
        FaceGlassesError,
        FaceQualityError
    }
}
