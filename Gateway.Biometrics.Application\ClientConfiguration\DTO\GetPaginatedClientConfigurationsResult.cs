﻿using System;
using System.Collections.Generic;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.ClientConfiguration.DTO
{
    public class GetPaginatedClientConfigurationsResult : BasePaginationServiceListResult<GetPaginatedClientConfigurationsStatus>
    {
        public IEnumerable<ClientConfigurationDto> ClientConfigurations { get; set; }
    }
    public enum GetPaginatedClientConfigurationsStatus
    {
        Successful,
        InvalidInput,
        ResourceNotFound
    }
}
