﻿using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.InventoryStatusLog.DTO
{
    public class CreateInventoryStatusLogResult : BaseServiceResult<CreateInventoryStatusLogStatus>
    {
        public int Id { get; set; }
    }
    
    public enum CreateInventoryStatusLogStatus
    {
        Successful,
        ResourceExists,
        InvalidInput,
        InventoryNotFound
    }
}