﻿using FluentValidation;
using Gateway.Biometrics.Application.User.DTO;
using Gateway.Biometrics.Resources;
using Gateway.Extensions;

namespace Gateway.Biometrics.Application.User.Validator
{
    public static class UserValidator
    {
        internal class PortalUserLoginValidator : AbstractValidator<PortalUserLoginRequest>
        {
            public PortalUserLoginValidator()
            {
                RuleFor(p => p).Custom((item, context) =>
                {
                    if (item.UserName.IsNullOrWhitespace())
                        context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.UserName)));
                });

                RuleFor(p => p).Custom((item, context) =>
                {
                    if (item.Password.IsNullOrWhitespace())
                        context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Password)));
                });

                RuleFor(p => p).Custom((item, context) =>
                {
                    if (item.HostName.IsNullOrWhitespace())
                        context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.HostName)));
                });
            }
        }


    }
}
