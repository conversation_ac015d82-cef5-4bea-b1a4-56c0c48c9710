﻿using Gateway.Biometrics.Resources;
using Gateway.Core.CustomAttributes;
using Gateway.Core.Responses.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Innovatrics.DTO
{
    public class FaceQualityResult : BaseServiceResult<FaceQualityStatus>
    {
        public string ErrorCode { get; set; }

        public FaceAttributeDto Confidence { get; set; }
        public FaceAttributeDto GlassStatus { get; set; }
        public FaceAttributeDto HeavyFrame { get; set; }
        public FaceAttributeDto Sharpness { get; set; }
        public FaceAttributeDto Brightness { get; set; }
        public FaceAttributeDto Contrast { get; set; }
        public FaceAttributeDto UniqueIntensityLevels { get; set; }
        public FaceAttributeDto Shadow { get; set; }
        public FaceAttributeDto NoseShadow { get; set; }
        public FaceAttributeDto Specularity { get; set; }
        public FaceAttributeDto BackgroundUniformity { get; set; }
        public FaceAttributeDto RedRightEye { get; set; }
        public FaceAttributeDto RedLeftEye { get; set; }
        public FaceAttributeDto Roll { get; set; }
        public FaceAttributeDto Yaw { get; set; }
        public FaceAttributeDto Pitch { get; set; }
        public FaceAttributeDto EyeDistance { get; set; }
        public FaceAttributeDto EyeGaze { get; set; }
        public FaceAttributeDto RightEye { get; set; }
        public FaceAttributeDto LeftEye { get; set; }
        public FaceAttributeDto Mouth { get; set; }
        public FaceAttributeDto FaceSize { get; set; }
        public FaceAttributeDto FaceRelativeArea { get; set; }
        public FaceAttributeDto FaceRelativeAreaInImage { get; set; }        

    }

    public enum FaceQualityStatus
    {
        [CustomHttpStatus(Code = "SUCCESS", Resources = typeof(ServiceResources), Status = "SUCCESS", StatusCode = HttpStatusCodes.Ok)]
        Successful,

        [CustomHttpStatus(Code = "INVALID_INPUT_ERROR", Resources = typeof(ServiceResources), Status = "INVALID_INPUT_ERROR", StatusCode = HttpStatusCodes.InvalidInput)]
        InvalidInput,

        [CustomHttpStatus(Code = "BAD_REQUEST", Resources = typeof(ServiceResources), Status = "BAD_REQUEST", StatusCode = HttpStatusCodes.BadRequest)]
        BadRequest,

        [CustomHttpStatus(Code = "RESOURCE_NOT_FOUND", Resources = typeof(ServiceResources), Status = "RESOURCE_NOT_FOUND", StatusCode = HttpStatusCodes.ResourceNotFound)]
        NotFound,

        [CustomHttpStatus(Code = "INTERNAL_SERVICE_ERROR", Resources = typeof(ServiceResources), Status = "INTERNAL_SERVICE_ERROR", StatusCode = HttpStatusCodes.InternalServerError)]
        InternalServerError,

        [CustomHttpStatus(Code = "EXTERNAL_SERVICE_ERROR", Resources = typeof(ServiceResources), Status = "EXTERNAL_SERVICE_ERROR", StatusCode = HttpStatusCodes.ServiceUnavailable)]
        ExternalServiceError
    }
}
