﻿using Gateway.Http;
using Newtonsoft.Json;

namespace Gateway.Cargo.LastMile.Dto.Request
{
    public class LastMilePackageRequest:BaseHttpRequest
    {
        [JsonProperty("shipmentId")]
        public string ShipmentId { get; set; }

        [JsonProperty("address")]
        public string Address { get; set; }

        [JsonProperty("areaId")]
        public string AreaId { get; set; }

        [JsonProperty("docId")]
        public string DocId { get; set; }

        [JsonProperty("holderName")]
        public string HolderName { get; set; }

        [JsonProperty("packageType")]
        public string PackageType { get; set; }

        [Json<PERSON>roperty("recipientPhoneNumber")]
        public string RecipientPhoneNumber { get; set; }

        [Json<PERSON>roperty("secondRecipientPhoneNumber")]
        public string SecondRecipientPhoneNumber { get; set; }
    }
}
