﻿using Gateway.Biometrics.Application.Category.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.Biometrics.Api.Models.Category
{
    public static class CategoryResponseFactory
    {
        public static ObjectResult CreateCategoryResponse(CreateCategoryResult result)
        {
            switch (result.Status)
            {
                case CreateCategoryStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.SUCCESS,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_CREATED),
                            Message = result.Message,
                            Data = new
                            {
                                Id = result.Id
                            }
                        })
                        { StatusCode = HttpStatusCodes.Created };
                case CreateCategoryStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.INPUT_ERROR,
                            Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                            Message = result.Message,
                            ValidationMessages = result.ValidationMessages
                        })
                        { StatusCode = HttpStatusCodes.InvalidInput };
                
                case CreateCategoryStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.ResourceExist };
                default:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.FAILED),
                            Message = ServiceResources.FAILED
                        })
                        { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult UpdateCategoryResponse(UpdateCategoryResult result)
        {
            switch (result.Status)
            {
                case UpdateCategoryStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_UPDATED),
                        Message = result.Message,
                        Data = result.Id
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case UpdateCategoryStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case UpdateCategoryStatus.CategoryNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.Category_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.Category_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateCategoryStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult DeleteCategoryResponse(DeleteCategoryResult result)
        {
            switch (result.Status)
            {
                case DeleteCategoryStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.SUCCESS,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_DELETED),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.Ok };
                case DeleteCategoryStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.INPUT_ERROR,
                            Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                            Message = result.Message,
                            ValidationMessages = result.ValidationMessages
                        })
                        { StatusCode = HttpStatusCodes.InvalidInput };
                case DeleteCategoryStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.FAILED),
                            Message = ServiceResources.FAILED
                        })
                        { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult GetPaginatedCategoriesResponse(GetPaginatedCategoriesResult result)
        {
            switch (result.Status)
            {
                case GetPaginatedCategoriesStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.SUCCESS,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                            Message = result.Message,
                            Data = new
                            {
                              Categorys = result.Categories
                            }
                        })
                        { StatusCode = HttpStatusCodes.Ok };
                case GetPaginatedCategoriesStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.INPUT_ERROR,
                            Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                            Message = result.Message,
                            ValidationMessages = result.ValidationMessages
                        })
                        { StatusCode = HttpStatusCodes.InvalidInput };
                case GetPaginatedCategoriesStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.FAILED),
                            Message = ServiceResources.FAILED
                        })
                        { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult GetCategoriesByParentName(GetCategoriesByParentNameResult result)
        {
            switch (result.Status)
            {
                case GetCategoriesByParentNameStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.Categories
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case GetCategoriesByParentNameStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case GetCategoriesByParentNameStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult GetCategoriesByParentId(GetCategoriesByParentIdResult result)
        {
            switch (result.Status)
            {
                case GetCategoriesByParentIdStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.Categories
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case GetCategoriesByParentIdStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case GetCategoriesByParentIdStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }
    }
}