﻿using System.Collections.Generic;

namespace Gateway.Biometrics.Core
{
    public interface IBaseServiceResult<TStatus>
    {
        string Message { get; set; }
        List<string> ValidationMessages { get; set; }
        TStatus Status { get; set; }

    }

    public interface IBasePaginationServiceListResult<TStatus>
    {
        string Message { get; set; }

        TStatus Status { get; set; }

        List<string> ValidationMessages { get; set; }

        int TotalNumberOfPages { get; set; }

        int TotalNumberOfRecords { get; set; }
    }

    public interface IBaseServiceDataResult<T>
    {
        string Message { get; set; }
        List<string> ValidationMessages { get; set; }
        T Data { get; set; }

    }
}
