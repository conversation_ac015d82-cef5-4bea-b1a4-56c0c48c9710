﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Gateway.Biometrics.Persistence;
using Gateway.Biometrics.Resources;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Gateway.Core.Pagination;
using Gateway.Extensions;
using Gateway.Biometrics.Application.Lookup;
using Gateway.Biometrics.Application.Inventory.Validator;
using Gateway.Biometrics.Application.InventoryAttribute.DTO;
using Gateway.Biometrics.Application.InventoryAttribute.Validator;

namespace Gateway.Biometrics.Application.InventoryAttribute
{
    public class InventoryAttributeService : IInventoryAttributeService
    {
        private readonly BiometricsDbContext _dbContext;
        private readonly IValidationService _validationService;

        public InventoryAttributeService(IValidationService validationService, BiometricsDbContext dbContext)
        {
            _validationService = validationService;
            _dbContext = dbContext;
        }

        public async Task<CreateInventoryAttributeResult> CreateInventoryAttribute(CreateInventoryAttributeRequest request)
        {
            var validationResult = _validationService.Validate(typeof(CreateInventoryAttributeValidator), request);

            if (!validationResult.IsValid)
                return new CreateInventoryAttributeResult
                {
                    Status = CreateInventoryAttributeStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };


            var newInventoryAttribute = new Entity.Entities.Inventory.InventoryAttribute
            {

                Name = request.Name,
                FieldType = request.FieldType,
                DataType = request.DataType,
                DefaultValue = request.DefaultValue,
                MaxLength = request.MaxLength,
                SortOrder = request.SortOrder,
                Status = request.Status,
                IsRequired = request.IsRequired,
                IsSingleValue = request.IsSingleValue
            };

            await _dbContext.InventoryAttribute.AddAsync(newInventoryAttribute);

            await _dbContext.SaveChangesAsync();

            return new CreateInventoryAttributeResult
            {
                Status = CreateInventoryAttributeStatus.Successful,
                Message = ServiceResources.RESOURCE_CREATED,
                Id = newInventoryAttribute.Id
            };
        }

        public async Task<UpdateInventoryAttributeResult> UpdateInventoryAttribute(UpdateInventoryAttributeRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateInventoryAttributeValidator), request);

            if (!validationResult.IsValid)
                return new UpdateInventoryAttributeResult
                {
                    Status = UpdateInventoryAttributeStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var inventoryAttribute = await _dbContext.InventoryAttribute
                .Where(q => q.Id == request.InventoryAttributeId).FirstOrDefaultAsync();

            if (inventoryAttribute == null)
                return new UpdateInventoryAttributeResult
                {
                    Status = UpdateInventoryAttributeStatus.InventoryAttributeNotFound,
                    Message = ServiceResources.INVENTORY_NOT_FOUND,
                };


            inventoryAttribute.Name = request.Name;
            inventoryAttribute.FieldType = request.FieldType;
            inventoryAttribute.DataType = request.DataType;
            inventoryAttribute.DefaultValue = request.DefaultValue;
            inventoryAttribute.Status = request.Status;
            inventoryAttribute.MaxLength = request.MaxLength;
            inventoryAttribute.SortOrder = request.SortOrder;
            inventoryAttribute.IsRequired = request.IsRequired;
            inventoryAttribute.IsSingleValue = request.IsSingleValue;

            inventoryAttribute.IsDeleted = false;
            inventoryAttribute.UpdatedAt = DateTime.Now;

            _dbContext.InventoryAttribute.Update(inventoryAttribute);

            await _dbContext.SaveChangesAsync();

            return new UpdateInventoryAttributeResult
            {
                Status = UpdateInventoryAttributeStatus.Successful,
                Message = ServiceResources.RESOURCE_UPDATED,
                Id = inventoryAttribute.Id
            };
        }

        public async Task<DeleteInventoryAttributeResult> DeleteInventoryAttribute(DeleteInventoryAttributeRequest request)
        {
            var validationResult = _validationService.Validate(typeof(DeleteInventoryValidator), request);

            if (!validationResult.IsValid)
                return new DeleteInventoryAttributeResult
                {
                    Status = DeleteInventoryAttributeStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var inventoryAttribute = await _dbContext.InventoryAttribute
                .Where(p => !p.IsDeleted && p.Id == request.InventoryAttributeId).FirstOrDefaultAsync();

            if (inventoryAttribute == null)
                return new DeleteInventoryAttributeResult
                {
                    Status = DeleteInventoryAttributeStatus.ResourceNotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };

            inventoryAttribute.DeletedAt = DateTime.Now;
            inventoryAttribute.IsDeleted = true;

            _dbContext.InventoryAttribute.Update(inventoryAttribute);

            await _dbContext.SaveChangesAsync();

            return new DeleteInventoryAttributeResult
            {
                Status = DeleteInventoryAttributeStatus.Successful,
                Message = ServiceResources.RESOURCE_DELETED
            };
        }

        public async Task<GetPaginatedInventoryAttributesResult> GetPaginatedInventoryAttributes(GetPaginatedInventoryAttributesRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetPaginatedInventoryAttributesValidator), request);

            if (!validationResult.IsValid)
                return new GetPaginatedInventoryAttributesResult
                {
                    Status = GetPaginatedInventoryAttributesStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var queryInventoryAttributes = await _dbContext.InventoryAttribute
                .Where(p => !p.IsDeleted).ToListAsync();

            if (queryInventoryAttributes == null || queryInventoryAttributes.Count == 0)
                return new GetPaginatedInventoryAttributesResult
                {
                    Status = GetPaginatedInventoryAttributesStatus.ResourceNotFound,
                    Message = ServiceResources.INVENTORY_NOT_FOUND
                };

            var inventories = new GetPaginatedInventoryAttributesResult
            {
                InventoryAttributes = queryInventoryAttributes.Select(p => new InventoryAttributeDto
                {
                    Id = p.Id,
                    Name = p.Name,
                    FieldType = p.FieldType,
                    DataType = p.DataType,
                    DefaultValue = p.DefaultValue,
                    MaxLength = p.MaxLength,
                    SortOrder = p.SortOrder,
                    Status = p.Status,
                    IsRequired = p.IsRequired,
                    IsSingleValue = p.IsSingleValue,
                }).ToList()
            };

            var paginationResult = PagedResultsFactory.CreatePagedResult(
                inventories.InventoryAttributes.AsQueryable(), request.Pagination.PageNumber, request.Pagination.PageSize,
                request.Pagination.OrderBy, request.Pagination.Ascending);

            return paginationResult == null
                ? new GetPaginatedInventoryAttributesResult
                {
                    InventoryAttributes = null,
                    Status = GetPaginatedInventoryAttributesStatus.ResourceNotFound,
                    Message = ServiceResources.INVALID_INPUT_ERROR
                }
                : new GetPaginatedInventoryAttributesResult
                {
                    InventoryAttributes = paginationResult.Results,
                    Status = GetPaginatedInventoryAttributesStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED
                };
        }

    
        private static LookupValue GetLookupValue(int enumType, int? value)
        {
            return EnumExtensions.GetEnumAsDictionary(LookupTypeFactory.GetInstance(enumType))
                .Select(x => new LookupValue { Id = x.Key.ToString(), DisplayValue = x.Value })
                .FirstOrDefault(p => p.Id == value.ToString());
        }

    }
}