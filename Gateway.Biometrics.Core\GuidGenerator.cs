﻿using System.Security.Cryptography;
using System.Text;
using System;

namespace Gateway.Biometrics.Core
{
    public static class GuidGenerator
    {
        private const string ValidAlphaNumericChar = "ABCDEFGHJKLMNPRSTUVWXYZabcdefghjklmnprstuvwxyz1234567890";

        public static string Generate(int size = 15)
        {
            var result = new StringBuilder();

            using var rng = new RNGCryptoServiceProvider();
            var uintBuffer = new byte[sizeof(uint)];

            var length = size;

            while (length-- > 0)
            {
                rng.GetBytes(uintBuffer);

                var num = BitConverter.ToUInt32(uintBuffer, 0);

                result.Append(ValidAlphaNumericChar[(int)(num % (uint)ValidAlphaNumericChar.Length)]);
            }

            return result.ToString();
        }
    }
}
