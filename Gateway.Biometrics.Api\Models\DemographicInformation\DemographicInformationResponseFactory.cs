﻿using Gateway.Biometrics.Application.DemographicInformation.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.Biometrics.Api.Models.DemographicInformation
{
    public static class DemographicInformationResponseFactory
    {
        public static ObjectResult GetDemographicInformationByPassportResponse(GetDemographicInformationByPassportResult result)
        {
            return result.Status switch
            {
                GetDemographicInformationByPassportStatus.Successful => new ObjectResult(new BaseApiResponse
                {
                    Status = ServiceResources.SUCCESS,
                    Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                    Message = result.Message,
                    Data = result.ApplicationDto
                }) { StatusCode = HttpStatusCodes.Ok },
                GetDemographicInformationByPassportStatus.InvalidInput => new ObjectResult(new BaseApiResponse
                {
                    Status = ServiceResources.INPUT_ERROR,
                    Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                    Message = result.Message,
                    ValidationMessages = result.ValidationMessages
                }) { StatusCode = HttpStatusCodes.InvalidInput },
                GetDemographicInformationByPassportStatus.CountryNotFound => new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.COUNTRY_NOT_FOUND),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput },
                GetDemographicInformationByPassportStatus.ResourceNotFound => new ObjectResult(new BaseApiResponse
                {
                    Status = ServiceResources.RESOURCE_NOT_FOUND,
                    Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                    Message = result.Message
                }) { StatusCode = HttpStatusCodes.ResourceNotFound },
                _ => new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.InternalError }
            };
        }
    public static ObjectResult GetDemographicInformationByPassportAndNationalityResponse(GetDemographicInformationByPassportAndNationalityResult result)
        {
            return result.Status switch
            {
                GetDemographicInformationByPassportAndNationalityStatus.Successful => new ObjectResult(new BaseApiResponse
                {
                    Status = ServiceResources.SUCCESS,
                    Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                    Message = result.Message,
                    Data = result.ApplicationDto
                }) { StatusCode = HttpStatusCodes.Ok },
                GetDemographicInformationByPassportAndNationalityStatus.InvalidInput => new ObjectResult(new BaseApiResponse
                {
                    Status = ServiceResources.INPUT_ERROR,
                    Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                    Message = result.Message,
                    ValidationMessages = result.ValidationMessages
                }) { StatusCode = HttpStatusCodes.InvalidInput },
                GetDemographicInformationByPassportAndNationalityStatus.CountryNotFound => new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.COUNTRY_NOT_FOUND),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput },
                GetDemographicInformationByPassportAndNationalityStatus.ResourceNotFound => new ObjectResult(new BaseApiResponse
                {
                    Status = ServiceResources.RESOURCE_NOT_FOUND,
                    Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                    Message = result.Message
                }) { StatusCode = HttpStatusCodes.ResourceNotFound },
                _ => new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.InternalError }
            };
        }

    }
}
