﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Accompaniment" xml:space="preserve">
    <value>Refakat</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Aktif</value>
  </data>
  <data name="AdditionalDocumentSentToEmbassy" xml:space="preserve">
    <value>Ek evraklarla elçiliğe gönderildi</value>
  </data>
  <data name="AdditionalDocumentsReceivedAtEmbassy" xml:space="preserve">
    <value>Ek evraklar elçilikte teslim alındı</value>
  </data>
  <data name="AED" xml:space="preserve">
    <value>AED</value>
  </data>
  <data name="Agency" xml:space="preserve">
    <value>Acente</value>
  </data>
  <data name="AgencyCategoryCorporate" xml:space="preserve">
    <value>Kurumsal</value>
  </data>
  <data name="AgencyCategoryIndividual" xml:space="preserve">
    <value>Bireysel</value>
  </data>
  <data name="AgencyStatusActive" xml:space="preserve">
    <value>Aktif</value>
  </data>
  <data name="AgencyStatusOnHold" xml:space="preserve">
    <value>Beklemede</value>
  </data>
  <data name="AgencyStatusPassive" xml:space="preserve">
    <value>Pasif</value>
  </data>
  <data name="AgencyTypeFileTypeCertificate" xml:space="preserve">
    <value>Sertifika</value>
  </data>
  <data name="AgencyTypeFileTypeOther" xml:space="preserve">
    <value>Diğer</value>
  </data>
  <data name="Agriculture" xml:space="preserve">
    <value>Tarım</value>
  </data>
  <data name="Air" xml:space="preserve">
    <value>Hava</value>
  </data>
  <data name="AliensPassport" xml:space="preserve">
    <value>Yabancı pasaport</value>
  </data>
  <data name="ApplicantRequest" xml:space="preserve">
    <value>Başvuru sahibi isteği</value>
  </data>
  <data name="ApplicantTypeAgency" xml:space="preserve">
    <value>Acente</value>
  </data>
  <data name="ApplicantTypeFamily" xml:space="preserve">
    <value>Aile başvurusu</value>
  </data>
  <data name="ApplicantTypeGroup" xml:space="preserve">
    <value>Grup başvurusu</value>
  </data>
  <data name="ApplicantTypeIndividual" xml:space="preserve">
    <value>Bireysel başvuru</value>
  </data>
  <data name="ApplicantTypeRepresentative" xml:space="preserve">
    <value>Temsilci</value>
  </data>
  <data name="Application" xml:space="preserve">
    <value>Başvuru</value>
  </data>
  <data name="ApplicationCancellationTypeCancellation" xml:space="preserve">
    <value>İptal</value>
  </data>
  <data name="ApplicationCancellationTypePartialRefund" xml:space="preserve">
    <value>Kısmi iade</value>
  </data>
  <data name="ApplicationDone" xml:space="preserve">
    <value>Başvuru alındı</value>
  </data>
  <data name="ApplicationFileTypeBiometricPhoto" xml:space="preserve">
    <value>Biometrik fotoğraf</value>
  </data>
  <data name="ApplicationFileTypeDamageEntryStamp" xml:space="preserve">
    <value>(Hasar) Ülkeye giriş damgası</value>
  </data>
  <data name="ApplicationFileTypeDamageExpenseBill" xml:space="preserve">
    <value>(Hasar) Masraf dökümü</value>
  </data>
  <data name="ApplicationFileTypeDamageMedicalReport" xml:space="preserve">
    <value>(Hasar) Medikal rapor</value>
  </data>
  <data name="ApplicationFileTypeDamageStatement" xml:space="preserve">
    <value>(Hasar) Beyan</value>
  </data>
  <data name="ApplicationFileTypeFlightBooking" xml:space="preserve">
    <value>Uçak rezervasyonu</value>
  </data>
  <data name="ApplicationFileTypeHealthInsurance" xml:space="preserve">
    <value>Sağlık sigortası</value>
  </data>
  <data name="ApplicationFileTypeHotelReservation" xml:space="preserve">
    <value>Hotel rezervasyonu</value>
  </data>
  <data name="ApplicationFileTypeIncome" xml:space="preserve">
    <value>Gelir belgesi</value>
  </data>
  <data name="ApplicationFileTypeOther" xml:space="preserve">
    <value>Diğer</value>
  </data>
  <data name="ApplicationFileTypePassport" xml:space="preserve">
    <value>Pasaport</value>
  </data>
  <data name="ApplicationFileTypeRejectionDataPage" xml:space="preserve">
    <value>(Ret) Data sayfası</value>
  </data>
  <data name="ApplicationFileTypeRejectionPassport" xml:space="preserve">
    <value>(Ret) Pasaport</value>
  </data>
  <data name="ApplicationFileTypeRejectionReturnStatement" xml:space="preserve">
    <value>(Ret) İade beyannamesi</value>
  </data>
  <data name="ApplicationFileTypeSupportDocument" xml:space="preserve">
    <value>Diğer dokumanlar</value>
  </data>
  <data name="ApplicationFormElementAccomodationDetail" xml:space="preserve">
    <value>Konaklama bilgisi</value>
  </data>
  <data name="ApplicationFormElementApplicantsMartialStatus" xml:space="preserve">
    <value>Başvuranın Medeni Durumu</value>
  </data>
  <data name="ApplicationFormElementBankBalance" xml:space="preserve">
    <value>Banka bakiyesi</value>
  </data>
  <data name="ApplicationFormElementBankBalanceCurrency" xml:space="preserve">
    <value>Para birimi (Banka bakiyesi)</value>
  </data>
  <data name="ApplicationFormElementCityName" xml:space="preserve">
    <value>Ziyaret edilecek şehir</value>
  </data>
  <data name="ApplicationFormElementCompanyName" xml:space="preserve">
    <value>Firma adı</value>
  </data>
  <data name="ApplicationFormElementJob" xml:space="preserve">
    <value>Meslek</value>
  </data>
  <data name="ApplicationFormElementMonthlySalary" xml:space="preserve">
    <value>Aylık maaş</value>
  </data>
  <data name="ApplicationFormElementMonthlySalaryCurrency" xml:space="preserve">
    <value>Para birimi (Aylık maaş)</value>
  </data>
  <data name="ApplicationFormElementPassportExpiryDate" xml:space="preserve">
    <value>Pasaport Geçerlilik Tarihi</value>
  </data>
  <data name="ApplicationFormElementPersonTravelWith" xml:space="preserve">
    <value>Birlikte seyahat ettiği kişi</value>
  </data>
  <data name="ApplicationFormElementReimbursementSponsorDetail" xml:space="preserve">
    <value>Masraf karşılama sponsor detayı</value>
  </data>
  <data name="ApplicationFormElementReimbursementType" xml:space="preserve">
    <value>Masraf karşılama türü</value>
  </data>
  <data name="ApplicationFormElementRelativeLocation" xml:space="preserve">
    <value>Akrabanın bulunduğu yer</value>
  </data>
  <data name="ApplicationFormElementTotalYearInCompany" xml:space="preserve">
    <value>Kaç yıldır şirkette çalışıyor?</value>
  </data>
  <data name="ApplicationFormElementTotalYearInCountry" xml:space="preserve">
    <value>Kaç yıldır ülkede yaşıyor?</value>
  </data>
  <data name="ApplicationNotReceived" xml:space="preserve">
    <value>Başvuru alınmadı</value>
  </data>
  <data name="ApplicationReportOfRejectedPassports" xml:space="preserve">
    <value>Reddedilen pasaportların başvuru raporu</value>
  </data>
  <data name="ApplicationService" xml:space="preserve">
    <value>Uygulama servisi</value>
  </data>
  <data name="ApplicationStatusActive" xml:space="preserve">
    <value>Aktif</value>
  </data>
  <data name="ApplicationStatusCancelled" xml:space="preserve">
    <value>İptal edildi</value>
  </data>
  <data name="ApplicationStatusCompleted" xml:space="preserve">
    <value>Tamamlandı</value>
  </data>
  <data name="ApplicationStatusPartiallyRefunded" xml:space="preserve">
    <value>Kısmi iade yapıldı</value>
  </data>
  <data name="ApplicationStatusPassportsDelivered" xml:space="preserve">
    <value>Pasaport teslim edildi</value>
  </data>
  <data name="ApplicationStatusPassportsWaitingToBeDelivered" xml:space="preserve">
    <value>Pasaport teslim edilmeyi bekliyor</value>
  </data>
  <data name="ApplicationStatusPcrDelivered" xml:space="preserve">
    <value>Pcr sonucu alındı</value>
  </data>
  <data name="ApplicationStatusPcrWaiting" xml:space="preserve">
    <value>Pcr sonucu bekleniyor</value>
  </data>
  <data name="ApplicationTaken" xml:space="preserve">
    <value>Başvuru Alındı</value>
  </data>
  <data name="ApplicationTypeFree" xml:space="preserve">
    <value>Ücretsiz başvuru</value>
  </data>
  <data name="ApplicationTypeNonApplicationInsurance" xml:space="preserve">
    <value>Başvuru dışı sigorta</value>
  </data>
  <data name="ApplicationTypeNonApplicationPcr" xml:space="preserve">
    <value>Başvuru dışı PCR</value>
  </data>
  <data name="ApplicationTypeNonApplicationPhotocopy" xml:space="preserve">
    <value>Başvuru Dışı Fotokopi</value>
  </data>
  <data name="ApplicationTypeNonApplicationPhotograph" xml:space="preserve">
    <value>Başvuru Dışı Fotoğraf</value>
  </data>
  <data name="ApplicationTypeNonApplicationPrintOut" xml:space="preserve">
    <value>Başvuru Dışı Çıktı</value>
  </data>
  <data name="ApplicationTypeNormal" xml:space="preserve">
    <value>Normal başvuru</value>
  </data>
  <data name="ApplicationTypeTurquois" xml:space="preserve">
    <value>Turkuaz Başvuru</value>
  </data>
  <data name="ApplicationTypeTurquoisGratis" xml:space="preserve">
    <value>Turkuaz Gratis Başvuru</value>
  </data>
  <data name="ApplicationTypeTurquoisPremium" xml:space="preserve">
    <value>Turkuaz Premium Başvuru</value>
  </data>
  <data name="ApplicationUnderEvaluation" xml:space="preserve">
    <value>Başvurunuz Değerlendirme Aşamasındadır</value>
  </data>
  <data name="ApplicationWithoutPassport" xml:space="preserve">
    <value>Pasaportsuz başvuru</value>
  </data>
  <data name="ApplicationWithPassport" xml:space="preserve">
    <value>Pasaportlu başvuru</value>
  </data>
  <data name="Appointment" xml:space="preserve">
    <value>Başvuru</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>Onaylandı</value>
  </data>
  <data name="ApprovedWorkPermit" xml:space="preserve">
    <value>Onaylanmış çalışma izni</value>
  </data>
  <data name="April" xml:space="preserve">
    <value>Nisan</value>
  </data>
  <data name="Arabic" xml:space="preserve">
    <value>Arapça</value>
  </data>
  <data name="ArchaelogicalExcavation" xml:space="preserve">
    <value>Arkeolojik kazı</value>
  </data>
  <data name="ArmedSecurityForce" xml:space="preserve">
    <value>Silahlı güvenlik gücü</value>
  </data>
  <data name="ArtistPerformer" xml:space="preserve">
    <value>Sanatçı / oyuncu</value>
  </data>
  <data name="Asseco" xml:space="preserve">
    <value>Asseco</value>
  </data>
  <data name="AssignedArtists" xml:space="preserve">
    <value>Atanan sanatçılar</value>
  </data>
  <data name="AssignedForDuty" xml:space="preserve">
    <value>Görev için atandı</value>
  </data>
  <data name="AssignedFreeZoneWorkers" xml:space="preserve">
    <value>Atanan serbest bölge çalışanları</value>
  </data>
  <data name="AssignedJournalist" xml:space="preserve">
    <value>Atanan gazeteci</value>
  </data>
  <data name="AssignedLecturersAcademics" xml:space="preserve">
    <value>Atanan öğretim görevlileri / akademisyenler</value>
  </data>
  <data name="AssignedSportsperson" xml:space="preserve">
    <value>Atanan sporcu</value>
  </data>
  <data name="August" xml:space="preserve">
    <value>Ağustos</value>
  </data>
  <data name="AuthorizationDocument" xml:space="preserve">
    <value>Yetki belgesi</value>
  </data>
  <data name="B2B" xml:space="preserve">
    <value>B2B</value>
  </data>
  <data name="B2C" xml:space="preserve">
    <value>B2C</value>
  </data>
  <data name="Biometrics" xml:space="preserve">
    <value>Biometrik</value>
  </data>
  <data name="BiomeTRLightCameraMotion" xml:space="preserve">
    <value>BiomeTR ışık-kamera hareket</value>
  </data>
  <data name="Black" xml:space="preserve">
    <value>Siyah</value>
  </data>
  <data name="Blue" xml:space="preserve">
    <value>Mavi</value>
  </data>
  <data name="BranchApplicationCountryFileTypeDescription" xml:space="preserve">
    <value>Açıklama</value>
  </data>
  <data name="BranchApplicationCountryFileTypeInformation" xml:space="preserve">
    <value>Bilgilendirme</value>
  </data>
  <data name="BranchApplicationCountryFileTypeRequirements" xml:space="preserve">
    <value>Gerekli evraklar</value>
  </data>
  <data name="BreakingTravelDocuments" xml:space="preserve">
    <value>Seyahat belgelerinin şartlarını ihlal etmek</value>
  </data>
  <data name="Business" xml:space="preserve">
    <value>İş</value>
  </data>
  <data name="BusinessMeetingCommerce" xml:space="preserve">
    <value>İş toplantısı / ticaret</value>
  </data>
  <data name="ByAppointment" xml:space="preserve">
    <value>Randevulu</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>Kamera</value>
  </data>
  <data name="Cancelled" xml:space="preserve">
    <value>İptal edilen</value>
  </data>
  <data name="CaregiverAndBabysitter" xml:space="preserve">
    <value>Bakıcı ve bebek bakıcısı</value>
  </data>
  <data name="Cargo" xml:space="preserve">
    <value>Kargo</value>
  </data>
  <data name="Child" xml:space="preserve">
    <value>Çocuğu</value>
  </data>
  <data name="CommissionOfCrime" xml:space="preserve">
    <value>Suç komisyonu</value>
  </data>
  <data name="Companion" xml:space="preserve">
    <value>Refakat</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Tamamlandı</value>
  </data>
  <data name="ConferenceFair" xml:space="preserve">
    <value>Konferans - fuar</value>
  </data>
  <data name="ConferenceSeminarMeeting" xml:space="preserve">
    <value>Konferans seminer toplantısı</value>
  </data>
  <data name="Consenting" xml:space="preserve">
    <value>İstizan</value>
  </data>
  <data name="Construction" xml:space="preserve">
    <value>İnşaat</value>
  </data>
  <data name="ConsulateCanceledRefund" xml:space="preserve">
    <value>Konsolosluk iptal iade</value>
  </data>
  <data name="ConsulateDecision" xml:space="preserve">
    <value>Konsolosluk kararı</value>
  </data>
  <data name="Courier" xml:space="preserve">
    <value>Kurye</value>
  </data>
  <data name="CoursePurpose" xml:space="preserve">
    <value>Kurs amacı</value>
  </data>
  <data name="Created" xml:space="preserve">
    <value>Oluşturuldu</value>
  </data>
  <data name="CriminalRecords" xml:space="preserve">
    <value>Suç kayıtları</value>
  </data>
  <data name="CulinaryCookery" xml:space="preserve">
    <value>Aşçılık</value>
  </data>
  <data name="CulturalArtisticActivity" xml:space="preserve">
    <value>Kültürel sanatsal etkinlik</value>
  </data>
  <data name="CulturalSportive" xml:space="preserve">
    <value>Kültürel/sportif</value>
  </data>
  <data name="DataTravelHistoryQuestion1" xml:space="preserve">
    <value>Menşe ülkeniz (vatandaşlığınız) dışında bir ülkede resmi oturma izniniz varsa, o ülkeye geri dönme izniniz var mı?</value>
  </data>
  <data name="DataTravelHistoryQuestion2" xml:space="preserve">
    <value>Farklı bir son varış noktasına seyahat ediyorsanız ve Türkiye'den transit geçiş yapıyorsanız, son varış ülkeniz için giriş izniniz var mı?</value>
  </data>
  <data name="DataTravelHistoryQuestion3" xml:space="preserve">
    <value>Hiç Türkiye vizesi veya oturma/çalışma izni aldınız mı?</value>
  </data>
  <data name="DataTravelHistoryQuestion4" xml:space="preserve">
    <value>Hiç Türkiye vizesi veya oturma/çalışma izniniz reddedildi mi?</value>
  </data>
  <data name="DataTravelHistoryQuestion5" xml:space="preserve">
    <value>Türkiye'ye girişiniz hiç giriş noktasında reddedildi mi?</value>
  </data>
  <data name="DataTravelHistoryQuestion6" xml:space="preserve">
    <value>Hiç Türkiye'den sınır dışı edildiniz mi veya Türkiye'den ayrılmanız istendi mi?</value>
  </data>
  <data name="DataTravelHistoryQuestion7" xml:space="preserve">
    <value>Türkiye'de hiç vizenizi veya oturma/çalışma izninizi aştınız mı?</value>
  </data>
  <data name="DataTravelHistoryQuestion8" xml:space="preserve">
    <value>Türkiye'de hiç ciddi bir suç işlediniz mi?</value>
  </data>
  <data name="DataTravelHistoryQuestion9" xml:space="preserve">
    <value>Vizeniz veya oturma/çalışma izniniz Türk makamları tarafından hiç iptal edildi mi?</value>
  </data>
  <data name="December" xml:space="preserve">
    <value>Aralık</value>
  </data>
  <data name="Declaration" xml:space="preserve">
    <value>Beyanname</value>
  </data>
  <data name="Declined" xml:space="preserve">
    <value>Reddedilen</value>
  </data>
  <data name="Deleted" xml:space="preserve">
    <value>Silindi</value>
  </data>
  <data name="DeliveredToApplicant" xml:space="preserve">
    <value>Başvuru sahibine teslim edilen</value>
  </data>
  <data name="DeliveredToCargo" xml:space="preserve">
    <value>Kargoya Teslim Edildi</value>
  </data>
  <data name="DeliveredToPost" xml:space="preserve">
    <value>Kargoya teslim edilen</value>
  </data>
  <data name="DiplomaticPassport" xml:space="preserve">
    <value>Diplomatik pasaport</value>
  </data>
  <data name="DocumentEditing" xml:space="preserve">
    <value>Evrak Düzenleme</value>
  </data>
  <data name="DocumentEditingService" xml:space="preserve">
    <value>Evrak Düzenleme Servisi</value>
  </data>
  <data name="DocumentManagement" xml:space="preserve">
    <value>Doküman yönetimi</value>
  </data>
  <data name="DoneQualityCheck" xml:space="preserve">
    <value>Kalite kontrolü yapıldı</value>
  </data>
  <data name="Donor" xml:space="preserve">
    <value>Donor</value>
  </data>
  <data name="DoubleTransit" xml:space="preserve">
    <value>Çift geçiş</value>
  </data>
  <data name="Driver" xml:space="preserve">
    <value>Şoför</value>
  </data>
  <data name="DriverLorry" xml:space="preserve">
    <value>Sürücü kamyon</value>
  </data>
  <data name="DZD" xml:space="preserve">
    <value>DZD</value>
  </data>
  <data name="EducationAndTraining" xml:space="preserve">
    <value>Eğitim ve öğretim</value>
  </data>
  <data name="EducationInTurkishRepublicOfNorthernCyprus" xml:space="preserve">
    <value>Kuzey Kıbrıs Türk Cumhuriyeti'nde eğitim</value>
  </data>
  <data name="EducationPurpose" xml:space="preserve">
    <value>Eğitim amaçlı</value>
  </data>
  <data name="Emaa" xml:space="preserve">
    <value>Emaa</value>
  </data>
  <data name="EmployementPurposeSpecialEmploymentPurpose" xml:space="preserve">
    <value>Özel istihdam amaçlı</value>
  </data>
  <data name="Engineer" xml:space="preserve">
    <value>Mühendis</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>İngilizce</value>
  </data>
  <data name="EntryBanned" xml:space="preserve">
    <value>Giriş yasaklı</value>
  </data>
  <data name="ErrorOccurred" xml:space="preserve">
    <value>Hata oluştu</value>
  </data>
  <data name="EUR" xml:space="preserve">
    <value>EUR</value>
  </data>
  <data name="Evisa" xml:space="preserve">
    <value>E-Vize</value>
  </data>
  <data name="ExistingData" xml:space="preserve">
    <value>Mevcut veri</value>
  </data>
  <data name="Expired" xml:space="preserve">
    <value>Zaman aşımı</value>
  </data>
  <data name="ExtraFile" xml:space="preserve">
    <value>Ekstra dosya</value>
  </data>
  <data name="FaceAnalysisService" xml:space="preserve">
    <value>Yüz analiz servisi</value>
  </data>
  <data name="FailedQualityCheck" xml:space="preserve">
    <value>Başarısız kalite kontrolü</value>
  </data>
  <data name="FamilyAndFriendVisit" xml:space="preserve">
    <value>Aile ve arkadaş ziyareti</value>
  </data>
  <data name="FamilyUnificationPurpose" xml:space="preserve">
    <value>Aile birleşimi amaçlı</value>
  </data>
  <data name="FamilyUnion" xml:space="preserve">
    <value>Aile birleşimi</value>
  </data>
  <data name="Faulty" xml:space="preserve">
    <value>Arızalı</value>
  </data>
  <data name="FaultyDocumentation" xml:space="preserve">
    <value>Hatalı belgeler</value>
  </data>
  <data name="Febraury" xml:space="preserve">
    <value>Şubat</value>
  </data>
  <data name="Female" xml:space="preserve">
    <value>Kadın</value>
  </data>
  <data name="FestivalFairExhibition" xml:space="preserve">
    <value>Festival fuarı sergisi</value>
  </data>
  <data name="Fifteen_TwentyFive" xml:space="preserve">
    <value>15-25 yaş arası</value>
  </data>
  <data name="FileCouldNotBeUploaded" xml:space="preserve">
    <value>Dosya yüklenemedi</value>
  </data>
  <data name="FileWithdrewAccordingtoCustomerRequest" xml:space="preserve">
    <value>Başvuru Sahibi İsteği İle Dosya Çekildi</value>
  </data>
  <data name="FilmingDocumentaryPurpose" xml:space="preserve">
    <value>Belgesel amaçlı çekim</value>
  </data>
  <data name="FinanceAndBanking" xml:space="preserve">
    <value>Finans ve bankacılık</value>
  </data>
  <data name="FingerprintReader" xml:space="preserve">
    <value>Parmak izi okuyucu</value>
  </data>
  <data name="FlexibleAppointment" xml:space="preserve">
    <value>Esnek Randevu</value>
  </data>
  <data name="FlightTicket" xml:space="preserve">
    <value>Uçak bileti</value>
  </data>
  <data name="FreeGWServiceFee" xml:space="preserve">
    <value>GW servis ücreti alınmayan</value>
  </data>
  <data name="FreeVisaFee" xml:space="preserve">
    <value>Vize servis ücreti alınmayan</value>
  </data>
  <data name="FreightVisa" xml:space="preserve">
    <value>Navlun vizesi</value>
  </data>
  <data name="Friday" xml:space="preserve">
    <value>Cuma</value>
  </data>
  <data name="Garanti" xml:space="preserve">
    <value>Garanti</value>
  </data>
  <data name="Government" xml:space="preserve">
    <value>Memur</value>
  </data>
  <data name="Green" xml:space="preserve">
    <value>Yeşil</value>
  </data>
  <data name="HandDeliveredToApplicantAtEmbassy" xml:space="preserve">
    <value>Pasaport elçilikten elden teslim edildi</value>
  </data>
  <data name="HandDeliveredToTheApplicant" xml:space="preserve">
    <value>Başvuru Sahibine Elden Teslim Edildi</value>
  </data>
  <data name="HavingAndInadmissibleRelative" xml:space="preserve">
    <value>Having and inadmissible relative</value>
  </data>
  <data name="Health" xml:space="preserve">
    <value>Sağlık</value>
  </data>
  <data name="HealthGround" xml:space="preserve">
    <value>Sağlık Alanı</value>
  </data>
  <data name="HealthInsurance" xml:space="preserve">
    <value>Sağlık Sigortası</value>
  </data>
  <data name="HealthMedical" xml:space="preserve">
    <value>Sağlık / medikal</value>
  </data>
  <data name="HealthPublicSafety" xml:space="preserve">
    <value>Sağlık ve kamu güvenliği</value>
  </data>
  <data name="HimselfHerself" xml:space="preserve">
    <value>Kendisi</value>
  </data>
  <data name="HostComapny" xml:space="preserve">
    <value>Şirket tarafından</value>
  </data>
  <data name="HostPerson" xml:space="preserve">
    <value>Başka biri tarafından</value>
  </data>
  <data name="HotelReservation" xml:space="preserve">
    <value>Otel rezervasyonu</value>
  </data>
  <data name="HusbandOrWife" xml:space="preserve">
    <value>Eşi</value>
  </data>
  <data name="IllegalEntryCounrty" xml:space="preserve">
    <value>Ülkeye yasadışı giriş</value>
  </data>
  <data name="IncompletedApplication" xml:space="preserve">
    <value>Gün içerisinde tamamlanmayan/tamamlanamayan başvuru</value>
  </data>
  <data name="IncorrectApplicationStatusReport" xml:space="preserve">
    <value>Yanlış Başvuru Durumu Düzeltme Raporu</value>
  </data>
  <data name="IncorrectEntryIcr" xml:space="preserve">
    <value>ICR’a yanlış giriş</value>
  </data>
  <data name="Individual" xml:space="preserve">
    <value>Bireysel</value>
  </data>
  <data name="InformationTechnologies" xml:space="preserve">
    <value>Bilişim teknolojileri</value>
  </data>
  <data name="InProgress" xml:space="preserve">
    <value>İşleme alındı</value>
  </data>
  <data name="InQualityCheck" xml:space="preserve">
    <value>Kalite kontrolde</value>
  </data>
  <data name="INR" xml:space="preserve">
    <value>INR</value>
  </data>
  <data name="Insurance" xml:space="preserve">
    <value>Sigorta</value>
  </data>
  <data name="InsuranceError" xml:space="preserve">
    <value>Sigorta hatası</value>
  </data>
  <data name="InternshipAisec" xml:space="preserve">
    <value>Staj aiesec</value>
  </data>
  <data name="IntershipErasmus" xml:space="preserve">
    <value>Staj erasmusu</value>
  </data>
  <data name="IntershipIaeste" xml:space="preserve">
    <value>Staj iaeste</value>
  </data>
  <data name="IntershipVisa" xml:space="preserve">
    <value>Staj vizesi</value>
  </data>
  <data name="InvalidOperation" xml:space="preserve">
    <value>Geçersiz işlem</value>
  </data>
  <data name="InventoryType1" xml:space="preserve">
    <value>Inventory type 1</value>
  </data>
  <data name="InventoryType2" xml:space="preserve">
    <value>Inventory type 2</value>
  </data>
  <data name="InventoryType3" xml:space="preserve">
    <value>Inventory type 3</value>
  </data>
  <data name="Invitation" xml:space="preserve">
    <value>Davetiye</value>
  </data>
  <data name="IQD" xml:space="preserve">
    <value>IQD</value>
  </data>
  <data name="IraqCitizen" xml:space="preserve">
    <value>Irak vatandaşı başvuru sayısı</value>
  </data>
  <data name="Istizan" xml:space="preserve">
    <value>İstizan</value>
  </data>
  <data name="IstizanDelvieredToApplicant" xml:space="preserve">
    <value>İstizan pasaport başvuru sahibine teslim edildi</value>
  </data>
  <data name="IstizanOutscanToEmbassy" xml:space="preserve">
    <value>İstizan son karar için elçiliğe teslim edildi</value>
  </data>
  <data name="IstizanOutscanToOffice" xml:space="preserve">
    <value>Istizan pasaport ofise teslim edildi</value>
  </data>
  <data name="IstizanRejection" xml:space="preserve">
    <value>Istızan ret</value>
  </data>
  <data name="January" xml:space="preserve">
    <value>Ocak</value>
  </data>
  <data name="July" xml:space="preserve">
    <value>Temmuz</value>
  </data>
  <data name="June" xml:space="preserve">
    <value>Haziran</value>
  </data>
  <data name="KWD" xml:space="preserve">
    <value>KWD</value>
  </data>
  <data name="LackTravelHealthInsurance" xml:space="preserve">
    <value>Seyahat ve/veya sağlık sigortasının olmaması</value>
  </data>
  <data name="Land" xml:space="preserve">
    <value>Kara</value>
  </data>
  <data name="LegalProfessional" xml:space="preserve">
    <value>Hukuk uzmanı</value>
  </data>
  <data name="LetterOfAcceptance" xml:space="preserve">
    <value>Kabul mektubu</value>
  </data>
  <data name="LosingLegalStatus" xml:space="preserve">
    <value>Losing legal status</value>
  </data>
  <data name="LYD" xml:space="preserve">
    <value>LYD</value>
  </data>
  <data name="Male" xml:space="preserve">
    <value>Erkek</value>
  </data>
  <data name="March" xml:space="preserve">
    <value>Mart</value>
  </data>
  <data name="MaritalStatusDivorced" xml:space="preserve">
    <value>Boşanmış</value>
  </data>
  <data name="MaritalStatusMarried" xml:space="preserve">
    <value>Evli</value>
  </data>
  <data name="MaritalStatusSingle" xml:space="preserve">
    <value>Bekar</value>
  </data>
  <data name="MaritalStatusWidowWidower" xml:space="preserve">
    <value>Dul</value>
  </data>
  <data name="May" xml:space="preserve">
    <value>Mayıs</value>
  </data>
  <data name="MBS" xml:space="preserve">
    <value>MBS</value>
  </data>
  <data name="MedicalTreatmentPurposes" xml:space="preserve">
    <value>Tıbbi tedavi amaçlı</value>
  </data>
  <data name="MinistryOfHealthApplication" xml:space="preserve">
    <value>Bakanlık Sağlık Başvurusu</value>
  </data>
  <data name="MisrepresentationFactInformation" xml:space="preserve">
    <value>Gerçeğin / bilginin yanlış beyanı</value>
  </data>
  <data name="MisrepresentationFalseInformation" xml:space="preserve">
    <value>Yanlış beyan / yanlış bilgi</value>
  </data>
  <data name="MissingDocuments" xml:space="preserve">
    <value>Eksik belgeler</value>
  </data>
  <data name="MissingOrInvalidData" xml:space="preserve">
    <value>Eksik ya da geçersiz veri</value>
  </data>
  <data name="Monday" xml:space="preserve">
    <value>Pazartesi</value>
  </data>
  <data name="Montage" xml:space="preserve">
    <value>Montaj</value>
  </data>
  <data name="MontageAndRepairmentPurposes" xml:space="preserve">
    <value>Montaj ve onarım amaçlı</value>
  </data>
  <data name="MoreThanEighty" xml:space="preserve">
    <value>80 yaş üstü</value>
  </data>
  <data name="MoreThanFifty" xml:space="preserve">
    <value>50 ve üstü</value>
  </data>
  <data name="Mr" xml:space="preserve">
    <value>Mr</value>
  </data>
  <data name="Mrs" xml:space="preserve">
    <value>Mrs</value>
  </data>
  <data name="Ms" xml:space="preserve">
    <value>Ms</value>
  </data>
  <data name="Multiple" xml:space="preserve">
    <value>Çoklu</value>
  </data>
  <data name="Myself" xml:space="preserve">
    <value>Kendim</value>
  </data>
  <data name="NansenPassport" xml:space="preserve">
    <value>Nansen pasaport</value>
  </data>
  <data name="NavyBlue" xml:space="preserve">
    <value>Lacivert</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Hayır</value>
  </data>
  <data name="NonApplicationInsurance" xml:space="preserve">
    <value>Başvuru dışı sigorta</value>
  </data>
  <data name="NonApplicationPCR" xml:space="preserve">
    <value>Başvuru dışı pcr</value>
  </data>
  <data name="Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="NormalApplications" xml:space="preserve">
    <value>Normal Başvurular</value>
  </data>
  <data name="NormalPCR" xml:space="preserve">
    <value>PCR normal</value>
  </data>
  <data name="NotOrdered" xml:space="preserve">
    <value>Alınmadı</value>
  </data>
  <data name="November" xml:space="preserve">
    <value>Kasım</value>
  </data>
  <data name="NPR" xml:space="preserve">
    <value>NPR</value>
  </data>
  <data name="October" xml:space="preserve">
    <value>Ekim</value>
  </data>
  <data name="OfficeWaitingForMissingDocuments" xml:space="preserve">
    <value>Eksik Evraktan Ofiste Bekletme</value>
  </data>
  <data name="OfficialVisa" xml:space="preserve">
    <value>Rezmi vize</value>
  </data>
  <data name="OfficialVisit" xml:space="preserve">
    <value>Resmi ziyaret</value>
  </data>
  <data name="OnHold" xml:space="preserve">
    <value>Beklemede</value>
  </data>
  <data name="OperationIsSuccessful" xml:space="preserve">
    <value>İşlem başarılı</value>
  </data>
  <data name="Orange" xml:space="preserve">
    <value>Turuncu</value>
  </data>
  <data name="Ordered" xml:space="preserve">
    <value>Alındı</value>
  </data>
  <data name="OrdinaryPassport" xml:space="preserve">
    <value>Normal pasaport</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Diğer</value>
  </data>
  <data name="OutscanToCourrier" xml:space="preserve">
    <value>Kargoya teslim edildi</value>
  </data>
  <data name="OverstayedVisit" xml:space="preserve">
    <value>Gecikmiş ziyaret</value>
  </data>
  <data name="Passive" xml:space="preserve">
    <value>Pasif</value>
  </data>
  <data name="PassportCouldNotBeRead" xml:space="preserve">
    <value>Pasaport okunamadı</value>
  </data>
  <data name="PassportDelivery" xml:space="preserve">
    <value>Pasaport teslim</value>
  </data>
  <data name="PassportPhotocopy" xml:space="preserve">
    <value>Pasaport fotokopisi</value>
  </data>
  <data name="PatientCompanion" xml:space="preserve">
    <value>Hasta refakat</value>
  </data>
  <data name="PCR" xml:space="preserve">
    <value>PCR</value>
  </data>
  <data name="PcrCanceled" xml:space="preserve">
    <value>PCR test iptal edildi</value>
  </data>
  <data name="PcrIsDone" xml:space="preserve">
    <value>PCR test yapıldı</value>
  </data>
  <data name="PCRTestCancelled" xml:space="preserve">
    <value>Pcr test iptal edildi</value>
  </data>
  <data name="PCRTestDone" xml:space="preserve">
    <value>Pcr test yapıldı</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>Beklemede</value>
  </data>
  <data name="PendingApproval" xml:space="preserve">
    <value>Onay bekleyen</value>
  </data>
  <data name="PersonalLoginError" xml:space="preserve">
    <value>Personel giriş hatası</value>
  </data>
  <data name="Petition" xml:space="preserve">
    <value>Dilekçe</value>
  </data>
  <data name="Pink" xml:space="preserve">
    <value>Pembe</value>
  </data>
  <data name="PKR" xml:space="preserve">
    <value>PKR</value>
  </data>
  <data name="Platinum" xml:space="preserve">
    <value>Platin</value>
  </data>
  <data name="Postponed" xml:space="preserve">
    <value>Ertelendi</value>
  </data>
  <data name="PressMedia" xml:space="preserve">
    <value>Basın / medya</value>
  </data>
  <data name="PrimeTime" xml:space="preserve">
    <value>Prime time</value>
  </data>
  <data name="PrivatePCR" xml:space="preserve">
    <value>PCR özel</value>
  </data>
  <data name="ProcessNotStarted" xml:space="preserve">
    <value>İşlem başlatılmadı</value>
  </data>
  <data name="ProfessionalSportsperson" xml:space="preserve">
    <value>Profesyonel sporcu</value>
  </data>
  <data name="Purchase" xml:space="preserve">
    <value>Alış</value>
  </data>
  <data name="Purple" xml:space="preserve">
    <value>Mor</value>
  </data>
  <data name="PutOnHold" xml:space="preserve">
    <value>Beklemeye alındı</value>
  </data>
  <data name="QAR" xml:space="preserve">
    <value>QAR</value>
  </data>
  <data name="QCAllDocumentsAreOriginal" xml:space="preserve">
    <value>Tüm evraklar orijinal görülüp görülmedi kaşesi</value>
  </data>
  <data name="QCAllowedDates" xml:space="preserve">
    <value>İzinli olduğu tarihler</value>
  </data>
  <data name="QCApplicantNameSurnameAndPassportValidity" xml:space="preserve">
    <value>Başvuran isim-soyisim ve pasaport no doğru yazılmalı</value>
  </data>
  <data name="QCArrangedForTurkeyOrWorldwide" xml:space="preserve">
    <value>Türkiye için ya da dünya geneli düzenlenmiş olmalı</value>
  </data>
  <data name="QCBackgroundWhite" xml:space="preserve">
    <value>Arka plan beyaz</value>
  </data>
  <data name="QCBankAccountStatementControl" xml:space="preserve">
    <value>Banka hesap dökümü</value>
  </data>
  <data name="QCBankBalance" xml:space="preserve">
    <value>Bakılıyor ise bakiyesi</value>
  </data>
  <data name="QCBankDocumentValidity" xml:space="preserve">
    <value>Orijinal, ıslak imza-kaşeli</value>
  </data>
  <data name="QCBusinessInvitationApplicantNameSurname" xml:space="preserve">
    <value>Başvuru sahibinin adı-sayadı tam ve net yazıyor</value>
  </data>
  <data name="QCBusinessInvitationContactInformation" xml:space="preserve">
    <value>İletişim bilgileri var ve davet mektubu okunaklı</value>
  </data>
  <data name="QCBusinessInvitationDocumentValidity" xml:space="preserve">
    <value>Davet eden şirket tarafından imzalı-kaşeli</value>
  </data>
  <data name="QCBusinessInvitationLetterControl" xml:space="preserve">
    <value>Ticari davet mektubu</value>
  </data>
  <data name="QCBusinessInvitationUpToDate" xml:space="preserve">
    <value>Davetiye güncel tarihli</value>
  </data>
  <data name="QCCompanyDocuments" xml:space="preserve">
    <value>Var ise şirket evrakları</value>
  </data>
  <data name="QCConsentLetterControl" xml:space="preserve">
    <value>Muvaffakatname</value>
  </data>
  <data name="QCConsentLetterValidity" xml:space="preserve">
    <value>Var, standartlara uygun, güncel, orijinal</value>
  </data>
  <data name="QCCustodyOrDeathCertificate" xml:space="preserve">
    <value>Yok, velayet veya ölüm belgesi var</value>
  </data>
  <data name="QCDataControlAtoZ" xml:space="preserve">
    <value>Data kontrol A-Z</value>
  </data>
  <data name="QCDemographicInformation" xml:space="preserve">
    <value>Demografik bilgiler</value>
  </data>
  <data name="QCDepartureDate" xml:space="preserve">
    <value>Gidiş tarihi</value>
  </data>
  <data name="QCDestinationCityInTurkey" xml:space="preserve">
    <value>Türkiye'deki bir şehir</value>
  </data>
  <data name="QCEmployerLetterControl" xml:space="preserve">
    <value>İşveren yazısı</value>
  </data>
  <data name="QCEmployerLetterValidty" xml:space="preserve">
    <value>Orijinal antetli kağıda, ıslak imza-kaşeli, güncel tarihli</value>
  </data>
  <data name="QCExistingTurkeyVisaStatus" xml:space="preserve">
    <value>Var ise eski vize kontrolü, geçerli Türkiye vizesi olmaması ya da belli bir sürenin altında olması</value>
  </data>
  <data name="QCFlightApplicantNameSurname" xml:space="preserve">
    <value>Başvuran isim-soyisim</value>
  </data>
  <data name="QCFlightMainDirection" xml:space="preserve">
    <value>Transit veya başvurusu ise asıl seyahat edeceği ülke için uçak bileti ve vizesi</value>
  </data>
  <data name="QCFlightRoundTripReservation" xml:space="preserve">
    <value>Gidiş-dönüş rezervasyon</value>
  </data>
  <data name="QCFlightTicketReservationControl" xml:space="preserve">
    <value>Uçak bilet rezervasyonu</value>
  </data>
  <data name="QCFuneralIncluded" xml:space="preserve">
    <value>Cenaze masraflarını karşılamalı</value>
  </data>
  <data name="QCHospitalInvitationApplicantNameSurname" xml:space="preserve">
    <value>Başvuru sahibinin adı-sayadı (refakatçilerin adı soyadı) tam ve net yazıyor</value>
  </data>
  <data name="QCHospitalInvitationContactInformation" xml:space="preserve">
    <value>İletişim bilgileri var ve davet mektubu okunaklı</value>
  </data>
  <data name="QCHospitalInvitationDocumentValidity" xml:space="preserve">
    <value>Hastane tarafından imzalı-kaşeli</value>
  </data>
  <data name="QCHospitalInvitationLetterControl" xml:space="preserve">
    <value>Hastane davet mektubu</value>
  </data>
  <data name="QCHospitalInvitationUpToDate" xml:space="preserve">
    <value>Davetiye güncel tarihli</value>
  </data>
  <data name="QCHotelApplicantNameSurname" xml:space="preserve">
    <value>Başvuran isim-soyisim</value>
  </data>
  <data name="QCHotelReservationControl" xml:space="preserve">
    <value>Otel rezervasyonu</value>
  </data>
  <data name="QCHotelReservationInTurkey" xml:space="preserve">
    <value>Türkiye'deki bir şehir</value>
  </data>
  <data name="QCHotelValidityForRoundTripFlight" xml:space="preserve">
    <value>Gidiş-dönüş uçak bileti kapsayacak şekilde olmalı</value>
  </data>
  <data name="QCIdCheck" xml:space="preserve">
    <value>Kimlik kontrolü</value>
  </data>
  <data name="QCIllnessReports" xml:space="preserve">
    <value>Hastalığına dair tüm raporlar</value>
  </data>
  <data name="QCInstitutionAddress" xml:space="preserve">
    <value>Hitap edilen kurum</value>
  </data>
  <data name="QCInsuranceMinumGuarantee" xml:space="preserve">
    <value>En az 30.000 € teminatı olmalı</value>
  </data>
  <data name="QCInsuranceValidityForRoundTripFlight" xml:space="preserve">
    <value>Gidiş-dönüş uçak bileti kapsayacak şekilde olmalı</value>
  </data>
  <data name="QCLastSixMonths" xml:space="preserve">
    <value>Son 6 alık</value>
  </data>
  <data name="QCOccupationAndSalaryInformation" xml:space="preserve">
    <value>Meslek ve maaş bilgisi</value>
  </data>
  <data name="QCPassportControl" xml:space="preserve">
    <value>Pasaport kontrol</value>
  </data>
  <data name="QCPassportHasBlankPages" xml:space="preserve">
    <value>En az 2 adet boş sayfasının olması</value>
  </data>
  <data name="QCPassportNotDamaged" xml:space="preserve">
    <value>Yıpranmamış, zarar görmemiş olması</value>
  </data>
  <data name="QCPassportStampValidity" xml:space="preserve">
    <value>Pasaporttaki kaşelerin kontrolü</value>
  </data>
  <data name="QCPasswordExpireDateValidadtion" xml:space="preserve">
    <value>Türkiye'ye gidiş tarihinden itibaren en az 6 ay geçerli</value>
  </data>
  <data name="QCPermissiveParentSigniture" xml:space="preserve">
    <value>İzin veren ebeveyn imzası</value>
  </data>
  <data name="QCPhotoCheck" xml:space="preserve">
    <value>Fotoğraf kontrol</value>
  </data>
  <data name="QCPhotoCurrentDated" xml:space="preserve">
    <value>Güncel tarihli</value>
  </data>
  <data name="QCResidenceCheck" xml:space="preserve">
    <value>İkamet kontrolü</value>
  </data>
  <data name="QCResidenceDurationValidity" xml:space="preserve">
    <value>Türkiye'ye gidiş tarihinden itibaren en az 6 ay geçerli</value>
  </data>
  <data name="QCSchoolAcceptanceApplicantNameSurname" xml:space="preserve">
    <value>Başvuru sahibinin adı-sayadı tam ve net yazıyor</value>
  </data>
  <data name="QCSchoolAcceptanceContactInformation" xml:space="preserve">
    <value>İletişim bilgileri var ve davet mektubu okunaklı</value>
  </data>
  <data name="QCSchoolAcceptanceDocumentValidity" xml:space="preserve">
    <value>Okul yetkilisi tarafından imzalı-kaşeli</value>
  </data>
  <data name="QCSchoolAcceptanceLetterControl" xml:space="preserve">
    <value>Okul kabul mektubu</value>
  </data>
  <data name="QCSchoolAcceptanceLetterForCurrentDate" xml:space="preserve">
    <value>Kabul mektubu güncel tarihli</value>
  </data>
  <data name="QCSchoolAcceptancepaymentOrScolarship" xml:space="preserve">
    <value>Ödeme yapıldığına dair dekont ya da bursluluk belgesi</value>
  </data>
  <data name="QCSchoolAcceptanceTranscript" xml:space="preserve">
    <value>Bir önceki mezuniyet belgesi ve transcript</value>
  </data>
  <data name="QCStandardValidity" xml:space="preserve">
    <value>Standartlara uygun</value>
  </data>
  <data name="QCSuitableForPhotoSize" xml:space="preserve">
    <value>Fotoğraf ölçüsüne uygun</value>
  </data>
  <data name="QCTravelDate" xml:space="preserve">
    <value>Seyahat tarihi</value>
  </data>
  <data name="QCTravelHealthInsuranceControl" xml:space="preserve">
    <value>Seyahat sağlık sigortası (dışarıdan kabul edilme durumunda)</value>
  </data>
  <data name="QCTravelWithParent" xml:space="preserve">
    <value>Birlikte seyahat; ebeveyn geçerli vize ve uçak bileti var</value>
  </data>
  <data name="QCVisaCategory" xml:space="preserve">
    <value>Vize türü</value>
  </data>
  <data name="QCVisaType" xml:space="preserve">
    <value>Vize tipi</value>
  </data>
  <data name="QueueMatic" xml:space="preserve">
    <value>Sıra yönetimi</value>
  </data>
  <data name="ReBiometry" xml:space="preserve">
    <value>Rebiyometri</value>
  </data>
  <data name="ReceivedAtEmbassy" xml:space="preserve">
    <value>Elçilikte teslim alındı</value>
  </data>
  <data name="ReceivedAtVAC" xml:space="preserve">
    <value>Vac'ta teslim alındı</value>
  </data>
  <data name="ReceivedAtVisaCenter" xml:space="preserve">
    <value>Vize Merkezinde Teslim Alındı</value>
  </data>
  <data name="RecievedAtVacForKuwait" xml:space="preserve">
    <value>Vac'ta Teslim Alındı.</value>
  </data>
  <data name="RecievedAtVisaCenter" xml:space="preserve">
    <value>Vize merkezinde teslim alındı</value>
  </data>
  <data name="RecordNotFound" xml:space="preserve">
    <value>Kayıt bulunamadı</value>
  </data>
  <data name="Red" xml:space="preserve">
    <value>Kırmızı</value>
  </data>
  <data name="RefugeePassport" xml:space="preserve">
    <value>Mülteci pasaport</value>
  </data>
  <data name="Refund" xml:space="preserve">
    <value>İade edildi</value>
  </data>
  <data name="Refunded" xml:space="preserve">
    <value>Kısmi iade edilen</value>
  </data>
  <data name="ReimbursementTypeHimselfHerself" xml:space="preserve">
    <value>Kendisi</value>
  </data>
  <data name="ReimbursementTypeNoFinancialDocument" xml:space="preserve">
    <value>Mali evrak yok</value>
  </data>
  <data name="ReimbursementTypeSponsor" xml:space="preserve">
    <value>Sponsor</value>
  </data>
  <data name="Rejected" xml:space="preserve">
    <value>Reddedildi</value>
  </data>
  <data name="RejectedPassportDeliveredToCourier" xml:space="preserve">
    <value>Redli pasaport korgoya teslim edildi</value>
  </data>
  <data name="Rejection" xml:space="preserve">
    <value>Vize reddedildi</value>
  </data>
  <data name="RejectionRefundDone" xml:space="preserve">
    <value>Vize ret iadesi yapıldı</value>
  </data>
  <data name="RejectionWithCountryEntryBanned" xml:space="preserve">
    <value>Yurda giriş yasağı ile ret</value>
  </data>
  <data name="ReligiousFunctionary" xml:space="preserve">
    <value>Din görevlisi</value>
  </data>
  <data name="Repairing" xml:space="preserve">
    <value>Bakımda</value>
  </data>
  <data name="ReportAllBranchesDailyInsurance" xml:space="preserve">
    <value>Şube bazlı sigorta kasa raporu</value>
  </data>
  <data name="ReportAllBranchesInsurance" xml:space="preserve">
    <value>Tüm şubeler aylık sigorta raporu</value>
  </data>
  <data name="ReportCancelCompletedApplications" xml:space="preserve">
    <value>Tamamlanmış başvuruların iptal raporu</value>
  </data>
  <data name="ReportCargo" xml:space="preserve">
    <value>Kargo raporu</value>
  </data>
  <data name="ReportCargoCompanyPayment" xml:space="preserve">
    <value>Kargo firma ödeme raporu</value>
  </data>
  <data name="ReportCashReport_1" xml:space="preserve">
    <value>Kasa raporu 1</value>
  </data>
  <data name="ReportCashReport_2" xml:space="preserve">
    <value>Kasa raporu 2</value>
  </data>
  <data name="ReportConsular" xml:space="preserve">
    <value>Konsolosluk raporu</value>
  </data>
  <data name="ReportDeliveredToCargo" xml:space="preserve">
    <value>Kargoya teslim edildi raporu</value>
  </data>
  <data name="ReportExtraFees" xml:space="preserve">
    <value>Ekstra ücretler raporu</value>
  </data>
  <data name="ReportInsuranceCancellation" xml:space="preserve">
    <value>Sigorta iptal raporu</value>
  </data>
  <data name="ReportInsuranceCancellationForRefund" xml:space="preserve">
    <value>Sigorta ile ücret iade raporu</value>
  </data>
  <data name="ReportInsuranceDetail" xml:space="preserve">
    <value>Sigorta poliçe detay raporu</value>
  </data>
  <data name="ReportNonApplicationInsurance" xml:space="preserve">
    <value>Başvuru dışı sigorta raporu</value>
  </data>
  <data name="ReportOldCashReport_1" xml:space="preserve">
    <value>Eski kasa raporu-1(13.08.2022 öncesi)</value>
  </data>
  <data name="ReportOldCashReport_2" xml:space="preserve">
    <value>Eski kasa raporu-2(13.08.2022 öncesi)</value>
  </data>
  <data name="ReportPCRCancellation" xml:space="preserve">
    <value>PCR iptal raporu</value>
  </data>
  <data name="ReportPCRCompanyPayment" xml:space="preserve">
    <value>PCR firma ödeme raporu</value>
  </data>
  <data name="ReportPCRGeneral" xml:space="preserve">
    <value>PCR genel raporu</value>
  </data>
  <data name="ReportPCRPayment" xml:space="preserve">
    <value>PCR ödeme raporu</value>
  </data>
  <data name="ReportQMS" xml:space="preserve">
    <value>QMS raporu</value>
  </data>
  <data name="ReportQMSPersonal" xml:space="preserve">
    <value>QMS personel raporu</value>
  </data>
  <data name="ReportQMSTimeline" xml:space="preserve">
    <value>QMS zaman raporu</value>
  </data>
  <data name="ReportRejectionStatus" xml:space="preserve">
    <value>Ret iade raporu</value>
  </data>
  <data name="ReportTypeAllApplications" xml:space="preserve">
    <value>Başvuru raporu</value>
  </data>
  <data name="ReportTypeAllStaffApplicationsByBranch" xml:space="preserve">
    <value>Bütün personel başvuru raporu</value>
  </data>
  <data name="ReportTypeCancelledInsurance" xml:space="preserve">
    <value>Sigorta iptal raporu</value>
  </data>
  <data name="ReportTypeDailyBalance" xml:space="preserve">
    <value>Günlük pasaport raporu</value>
  </data>
  <data name="ReportTypeDeletedApplications" xml:space="preserve">
    <value>Silinen başvuru raporu</value>
  </data>
  <data name="ReportTypeDetail" xml:space="preserve">
    <value>Detay raporu</value>
  </data>
  <data name="ReportTypeFreeApplications" xml:space="preserve">
    <value>Ücretsiz başvuru raporu</value>
  </data>
  <data name="ReportTypeInsurance" xml:space="preserve">
    <value>Sistemde geçerli sigorta raporu</value>
  </data>
  <data name="ReportTypeInsuranceApplications" xml:space="preserve">
    <value>Sigorta yapılan başvuru raporu</value>
  </data>
  <data name="ReportTypePartiallyRefundedApplications" xml:space="preserve">
    <value>Kısmi iade raporu</value>
  </data>
  <data name="ReportTypePcrDaily" xml:space="preserve">
    <value>Günlük PCR test raporu</value>
  </data>
  <data name="ReportTypePcrStatus" xml:space="preserve">
    <value>PCR durum raporu</value>
  </data>
  <data name="ReportTypePhotobooth" xml:space="preserve">
    <value>Photobooth raporu</value>
  </data>
  <data name="ReportTypeSafe" xml:space="preserve">
    <value>Kasa raporu</value>
  </data>
  <data name="ReportTypeScanCycle" xml:space="preserve">
    <value>Scan cycle raporu</value>
  </data>
  <data name="ReportTypeStaffExtraFeeSales" xml:space="preserve">
    <value>Personel bazlı başvuru raporu + VAS</value>
  </data>
  <data name="ReportVisaRejection" xml:space="preserve">
    <value>Vize ret raporu</value>
  </data>
  <data name="RepublicOfTurkeyDirectorateGeneralOfMigrationManagement" xml:space="preserve">
    <value>Türkiye cumhuriyeti göç idaresi genel müdürlüğü</value>
  </data>
  <data name="RepublicTurkeyDirectorateGeneralMigrationManagement" xml:space="preserve">
    <value>Türkiye cumhuriyeti göç idaresi genel müdürlüğü</value>
  </data>
  <data name="RepublicTurkeyMinistryCustomsTrade" xml:space="preserve">
    <value>Türkiye Cumhuriyeti Gümrük ve Ticaret Bakanlığı</value>
  </data>
  <data name="RepublicTurkeyMinistryLaborSocialSecurity" xml:space="preserve">
    <value>Türkiye cumhuriyeti çalışma ve sosyal güvenlik bakanlığı</value>
  </data>
  <data name="RepublicTurkeyMinistryMinistryForeignAfffairs" xml:space="preserve">
    <value>Türkiye cumhuriyeti dışişleri bakanlığı</value>
  </data>
  <data name="ResearcherScientist" xml:space="preserve">
    <value>Araştırmacı bilim adamı</value>
  </data>
  <data name="ResidencePermit" xml:space="preserve">
    <value>Oturma izni</value>
  </data>
  <data name="Retired" xml:space="preserve">
    <value>Emekli</value>
  </data>
  <data name="RetrospectiveCancellation" xml:space="preserve">
    <value>Geçmişe dönük iptal</value>
  </data>
  <data name="Sale" xml:space="preserve">
    <value>Satış</value>
  </data>
  <data name="SameDayCancellation" xml:space="preserve">
    <value>Aynı gün iptal</value>
  </data>
  <data name="SAR" xml:space="preserve">
    <value>SAR</value>
  </data>
  <data name="Saturday" xml:space="preserve">
    <value>Cumartesi</value>
  </data>
  <data name="Scanner" xml:space="preserve">
    <value>Tarayıcı</value>
  </data>
  <data name="Sea" xml:space="preserve">
    <value>Deniz</value>
  </data>
  <data name="Seafarer" xml:space="preserve">
    <value>Denizci</value>
  </data>
  <data name="SeafarerVisa" xml:space="preserve">
    <value>Denizci vizesi</value>
  </data>
  <data name="SecurityBreach" xml:space="preserve">
    <value>Güvenlik ihlali</value>
  </data>
  <data name="SecurityReason" xml:space="preserve">
    <value>Güvenlik sebebi</value>
  </data>
  <data name="SelfEmployed" xml:space="preserve">
    <value>Serbest meslek</value>
  </data>
  <data name="SendToEmbassyForFinalApproval" xml:space="preserve">
    <value>Son karar için elçiliğe gönderildi</value>
  </data>
  <data name="Sent" xml:space="preserve">
    <value>Gönderildi</value>
  </data>
  <data name="SentCenterDueToMissingDocuments" xml:space="preserve">
    <value>Eksik evrak nedeniyle başvuru merkezine gönderildi</value>
  </data>
  <data name="September" xml:space="preserve">
    <value>Eylül</value>
  </data>
  <data name="Servant" xml:space="preserve">
    <value>Hizmetçi</value>
  </data>
  <data name="ServerError" xml:space="preserve">
    <value>Servis hatası</value>
  </data>
  <data name="ServiceFee" xml:space="preserve">
    <value>Servis ücreti</value>
  </data>
  <data name="ServicePassport" xml:space="preserve">
    <value>Hizmet pasaport</value>
  </data>
  <data name="ServiceSector" xml:space="preserve">
    <value>Hizmet sektörü</value>
  </data>
  <data name="SeventyFive_Eighty" xml:space="preserve">
    <value>76-80 yaş</value>
  </data>
  <data name="Seventy_SeventyFive" xml:space="preserve">
    <value>71-75 yaş</value>
  </data>
  <data name="SignaturePad" xml:space="preserve">
    <value>İmza pedi</value>
  </data>
  <data name="Single" xml:space="preserve">
    <value>Tek</value>
  </data>
  <data name="SingleVisit" xml:space="preserve">
    <value>Tek ziyaret</value>
  </data>
  <data name="SixtySix_Seventy" xml:space="preserve">
    <value>66-70 yaş</value>
  </data>
  <data name="SpecialPassport" xml:space="preserve">
    <value>Özel pasaport</value>
  </data>
  <data name="Sportive" xml:space="preserve">
    <value>Sportif</value>
  </data>
  <data name="SportiveActivity" xml:space="preserve">
    <value>Sportif aktivite</value>
  </data>
  <data name="StampVisa" xml:space="preserve">
    <value>Damga vizesi</value>
  </data>
  <data name="StickerVisa" xml:space="preserve">
    <value>Etiket vizesi</value>
  </data>
  <data name="Student" xml:space="preserve">
    <value>Öğrenci</value>
  </data>
  <data name="StudentCompanion" xml:space="preserve">
    <value>Öğrenci refakat</value>
  </data>
  <data name="StudentEducationVisa" xml:space="preserve">
    <value>Öğrenci / eğitim vize</value>
  </data>
  <data name="StudentTrainee" xml:space="preserve">
    <value>Stajyer öğrenci</value>
  </data>
  <data name="Sunday" xml:space="preserve">
    <value>Pazar</value>
  </data>
  <data name="SystemError" xml:space="preserve">
    <value>Sistem hatası</value>
  </data>
  <data name="Thursday" xml:space="preserve">
    <value>Perşembe</value>
  </data>
  <data name="TMT" xml:space="preserve">
    <value>TMT</value>
  </data>
  <data name="TokenNotCreated" xml:space="preserve">
    <value>Token oluşturulmadı</value>
  </data>
  <data name="Tourism" xml:space="preserve">
    <value>Turizm</value>
  </data>
  <data name="TouristBusinessperson" xml:space="preserve">
    <value>Turistik / İş</value>
  </data>
  <data name="Touristic" xml:space="preserve">
    <value>Turistik</value>
  </data>
  <data name="TouristicVisit" xml:space="preserve">
    <value>Turistik ziyaret</value>
  </data>
  <data name="TourOperatorRepresentative" xml:space="preserve">
    <value>Tur operatör temsilcisi</value>
  </data>
  <data name="Train" xml:space="preserve">
    <value>Tren</value>
  </data>
  <data name="Transit" xml:space="preserve">
    <value>Transit</value>
  </data>
  <data name="TRY" xml:space="preserve">
    <value>TRY</value>
  </data>
  <data name="Tuesday" xml:space="preserve">
    <value>Salı</value>
  </data>
  <data name="Turkish" xml:space="preserve">
    <value>Türkçe</value>
  </data>
  <data name="TurkishBorderGates" xml:space="preserve">
    <value>Türkiye sınır kapıları</value>
  </data>
  <data name="TurkishLanguageCoursePurpose" xml:space="preserve">
    <value>Türkçe dil kurs amaçlı</value>
  </data>
  <data name="TurkishMissions" xml:space="preserve">
    <value>Türk görevleri</value>
  </data>
  <data name="TurkishNationalPolice" xml:space="preserve">
    <value>Türk ulusal polisi</value>
  </data>
  <data name="TwentyFive_Fifty" xml:space="preserve">
    <value>25-50 yaş arası</value>
  </data>
  <data name="TypeChanged" xml:space="preserve">
    <value>Başvuru tipi değişen</value>
  </data>
  <data name="Unemployed" xml:space="preserve">
    <value>İşsiz</value>
  </data>
  <data name="Unico" xml:space="preserve">
    <value>Unico</value>
  </data>
  <data name="UnInsuredApplications" xml:space="preserve">
    <value>Sigortasız başvuru raporu</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Bilinmeyen</value>
  </data>
  <data name="Unspecified" xml:space="preserve">
    <value>Belirtilmemiş</value>
  </data>
  <data name="USD" xml:space="preserve">
    <value>USD</value>
  </data>
  <data name="Used" xml:space="preserve">
    <value>Kullanıldı</value>
  </data>
  <data name="ViolationInternationalProtection" xml:space="preserve">
    <value>6458 sayılı yabancılar ve uluslararası koruma kanununun ihlali</value>
  </data>
  <data name="VIP" xml:space="preserve">
    <value>VIP</value>
  </data>
  <data name="Visa" xml:space="preserve">
    <value>Vize</value>
  </data>
  <data name="VisaDecisionReport" xml:space="preserve">
    <value>Vize Karar Raporu</value>
  </data>
  <data name="VisaEntryTypeMultiple" xml:space="preserve">
    <value>Çoklu</value>
  </data>
  <data name="VisaEntryTypeSingle" xml:space="preserve">
    <value>Tek</value>
  </data>
  <data name="VisaTransfer" xml:space="preserve">
    <value>Vize transfer</value>
  </data>
  <data name="VisitToTurkishRepublicOfNorthernCyprus" xml:space="preserve">
    <value>Kuzey Kıbrıs Türk Cumhuriyeti ziyareti</value>
  </data>
  <data name="WaitingApproval" xml:space="preserve">
    <value>Teyit Bekleniyor</value>
  </data>
  <data name="WaitingForAdditionalDocuments" xml:space="preserve">
    <value>Eksik evraklar bekleniyor</value>
  </data>
  <data name="WaitingForDelivery" xml:space="preserve">
    <value>Teslim edilmeyi bekleyen</value>
  </data>
  <data name="WaittingForQualityCheck" xml:space="preserve">
    <value>Kalite kontrolü bekleniyor</value>
  </data>
  <data name="WaittingToBeCompletedData" xml:space="preserve">
    <value>Verilerin tamamlanması bekleniyor</value>
  </data>
  <data name="WaittingToBeSentToQualityCheck" xml:space="preserve">
    <value>Kalite kontrole gönderilmeyi bekliyor</value>
  </data>
  <data name="WalkIn" xml:space="preserve">
    <value>Randevusuz</value>
  </data>
  <data name="Wednesday" xml:space="preserve">
    <value>Çarşamba</value>
  </data>
  <data name="White" xml:space="preserve">
    <value>Beyaz</value>
  </data>
  <data name="Wife" xml:space="preserve">
    <value>Eş</value>
  </data>
  <data name="WithdrawalByApplicant" xml:space="preserve">
    <value>Başvuru sahibi tarafından geri çekme</value>
  </data>
  <data name="WithoutReference" xml:space="preserve">
    <value>Başvurusuz</value>
  </data>
  <data name="WithReference" xml:space="preserve">
    <value>Başvurulu</value>
  </data>
  <data name="WorkPermit" xml:space="preserve">
    <value>Çalışma izni</value>
  </data>
  <data name="WorkPermitCompanion" xml:space="preserve">
    <value>Çalışma izni refakat</value>
  </data>
  <data name="WorkVisa" xml:space="preserve">
    <value>Çalışma vize</value>
  </data>
  <data name="WrongBranchNotification" xml:space="preserve">
    <value>Başvuru numarası bu şubeye ait değildir</value>
  </data>
  <data name="Yellow" xml:space="preserve">
    <value>Sarı</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Evet</value>
  </data>
  <data name="Zero_Fifteen" xml:space="preserve">
    <value>0-15 yaş arası</value>
  </data>
  <data name="Zero_SixtyFive" xml:space="preserve">
    <value>65 yaş ve altı</value>
  </data>
</root>