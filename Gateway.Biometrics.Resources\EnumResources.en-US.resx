﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Accompaniment" xml:space="preserve">
    <value>Accompaniment</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="AdditionalDocumentSentToEmbassy" xml:space="preserve">
    <value>Outscan to embassy with addition document</value>
  </data>
  <data name="AdditionalDocumentsReceivedAtEmbassy" xml:space="preserve">
    <value>Addition document received at embassy</value>
  </data>
  <data name="AED" xml:space="preserve">
    <value>AED</value>
  </data>
  <data name="Agency" xml:space="preserve">
    <value>Agency</value>
  </data>
  <data name="AgencyCategoryCorporate" xml:space="preserve">
    <value>Corporate</value>
  </data>
  <data name="AgencyCategoryIndividual" xml:space="preserve">
    <value>Individual</value>
  </data>
  <data name="AgencyStatusActive" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="AgencyStatusOnHold" xml:space="preserve">
    <value>On Hold</value>
  </data>
  <data name="AgencyStatusPassive" xml:space="preserve">
    <value>Passive</value>
  </data>
  <data name="AgencyTypeFileTypeCertificate" xml:space="preserve">
    <value>Certificate</value>
  </data>
  <data name="AgencyTypeFileTypeOther" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="Agriculture" xml:space="preserve">
    <value>Agriculture</value>
  </data>
  <data name="Air" xml:space="preserve">
    <value>Air</value>
  </data>
  <data name="AliensPassport" xml:space="preserve">
    <value>Aliens pasaport</value>
  </data>
  <data name="ApplicantRequest" xml:space="preserve">
    <value>Applicant request</value>
  </data>
  <data name="ApplicantTypeAgency" xml:space="preserve">
    <value>Agency</value>
  </data>
  <data name="ApplicantTypeFamily" xml:space="preserve">
    <value>Family application</value>
  </data>
  <data name="ApplicantTypeGroup" xml:space="preserve">
    <value>Group application</value>
  </data>
  <data name="ApplicantTypeIndividual" xml:space="preserve">
    <value>Individual application</value>
  </data>
  <data name="ApplicantTypeRepresentative" xml:space="preserve">
    <value>Representative</value>
  </data>
  <data name="Application" xml:space="preserve">
    <value>Application</value>
  </data>
  <data name="ApplicationCancellationTypeCancellation" xml:space="preserve">
    <value>Cancellation</value>
  </data>
  <data name="ApplicationCancellationTypePartialRefund" xml:space="preserve">
    <value>Partial refund</value>
  </data>
  <data name="ApplicationDone" xml:space="preserve">
    <value>Application done</value>
  </data>
  <data name="ApplicationFileTypeBiometricPhoto" xml:space="preserve">
    <value>Biometric photo</value>
  </data>
  <data name="ApplicationFileTypeDamageEntryStamp" xml:space="preserve">
    <value>(Damage) Entry stamp</value>
  </data>
  <data name="ApplicationFileTypeDamageExpenseBill" xml:space="preserve">
    <value>(Damage) Expense bill</value>
  </data>
  <data name="ApplicationFileTypeDamageMedicalReport" xml:space="preserve">
    <value>(Damage) Medical report</value>
  </data>
  <data name="ApplicationFileTypeDamageStatement" xml:space="preserve">
    <value>(Damage) Statement</value>
  </data>
  <data name="ApplicationFileTypeFlightBooking" xml:space="preserve">
    <value>Flight booking</value>
  </data>
  <data name="ApplicationFileTypeHealthInsurance" xml:space="preserve">
    <value>Health insurance</value>
  </data>
  <data name="ApplicationFileTypeHotelReservation" xml:space="preserve">
    <value>Hotel reservation</value>
  </data>
  <data name="ApplicationFileTypeIncome" xml:space="preserve">
    <value>Income</value>
  </data>
  <data name="ApplicationFileTypeOther" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="ApplicationFileTypePassport" xml:space="preserve">
    <value>Passport</value>
  </data>
  <data name="ApplicationFileTypeRejectionDataPage" xml:space="preserve">
    <value>(Rejection) Data page</value>
  </data>
  <data name="ApplicationFileTypeRejectionPassport" xml:space="preserve">
    <value>(Rejection) Passport</value>
  </data>
  <data name="ApplicationFileTypeRejectionReturnStatement" xml:space="preserve">
    <value>(Rejection) Return statement</value>
  </data>
  <data name="ApplicationFileTypeSupportDocument" xml:space="preserve">
    <value>Other documents</value>
  </data>
  <data name="ApplicationFormElementAccomodationDetail" xml:space="preserve">
    <value>Accomodation detail</value>
  </data>
  <data name="ApplicationFormElementApplicantsMartialStatus" xml:space="preserve">
    <value>Applicant's Martial Status</value>
  </data>
  <data name="ApplicationFormElementBankBalance" xml:space="preserve">
    <value>Bank balance</value>
  </data>
  <data name="ApplicationFormElementBankBalanceCurrency" xml:space="preserve">
    <value>Currency (Bank balance)</value>
  </data>
  <data name="ApplicationFormElementCityName" xml:space="preserve">
    <value>City to visit</value>
  </data>
  <data name="ApplicationFormElementCompanyName" xml:space="preserve">
    <value>Company name</value>
  </data>
  <data name="ApplicationFormElementJob" xml:space="preserve">
    <value>Job</value>
  </data>
  <data name="ApplicationFormElementMonthlySalary" xml:space="preserve">
    <value>Monthly salary</value>
  </data>
  <data name="ApplicationFormElementMonthlySalaryCurrency" xml:space="preserve">
    <value>Currency (Monthly salary)</value>
  </data>
  <data name="ApplicationFormElementPassportExpiryDate" xml:space="preserve">
    <value>Passport Expiry Date</value>
  </data>
  <data name="ApplicationFormElementPersonTravelWith" xml:space="preserve">
    <value>Person traveling with</value>
  </data>
  <data name="ApplicationFormElementReimbursementSponsorDetail" xml:space="preserve">
    <value>Reimbursement sponsor detail</value>
  </data>
  <data name="ApplicationFormElementReimbursementType" xml:space="preserve">
    <value>Reimbursement type</value>
  </data>
  <data name="ApplicationFormElementRelativeLocation" xml:space="preserve">
    <value>Relative's location</value>
  </data>
  <data name="ApplicationFormElementTotalYearInCompany" xml:space="preserve">
    <value>How many years does he/she work at the company?</value>
  </data>
  <data name="ApplicationFormElementTotalYearInCountry" xml:space="preserve">
    <value>How many years does he/she live in the country?</value>
  </data>
  <data name="ApplicationNotReceived" xml:space="preserve">
    <value>Application not received</value>
  </data>
  <data name="ApplicationReportOfRejectedPassports" xml:space="preserve">
    <value>Application Report Of Rejected Passports</value>
  </data>
  <data name="ApplicationService" xml:space="preserve">
    <value>Application service</value>
  </data>
  <data name="ApplicationStatusActive" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="ApplicationStatusCancelled" xml:space="preserve">
    <value>Cancelled</value>
  </data>
  <data name="ApplicationStatusCompleted" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="ApplicationStatusPartiallyRefunded" xml:space="preserve">
    <value>Partially refunded</value>
  </data>
  <data name="ApplicationStatusPassportsDelivered" xml:space="preserve">
    <value>Passport delivered</value>
  </data>
  <data name="ApplicationStatusPassportsWaitingToBeDelivered" xml:space="preserve">
    <value>Passport waiting to be delivered</value>
  </data>
  <data name="ApplicationStatusPcrDelivered" xml:space="preserve">
    <value>Pcr result delivered</value>
  </data>
  <data name="ApplicationStatusPcrWaiting" xml:space="preserve">
    <value>Pcr result waitting</value>
  </data>
  <data name="ApplicationTaken" xml:space="preserve">
    <value>Application Taken</value>
  </data>
  <data name="ApplicationTypeFree" xml:space="preserve">
    <value>Free application</value>
  </data>
  <data name="ApplicationTypeNonApplicationInsurance" xml:space="preserve">
    <value>Non-application insurance</value>
  </data>
  <data name="ApplicationTypeNonApplicationPcr" xml:space="preserve">
    <value>Non-application PCR</value>
  </data>
  <data name="ApplicationTypeNonApplicationPhotocopy" xml:space="preserve">
    <value>Non-application Photocopy</value>
  </data>
  <data name="ApplicationTypeNonApplicationPhotograph" xml:space="preserve">
    <value>Non-application Photograph</value>
  </data>
  <data name="ApplicationTypeNonApplicationPrintOut" xml:space="preserve">
    <value>Non-application Printout</value>
  </data>
  <data name="ApplicationTypeNormal" xml:space="preserve">
    <value>Normal application</value>
  </data>
  <data name="ApplicationTypeTurquois" xml:space="preserve">
    <value>Turquois application</value>
  </data>
  <data name="ApplicationTypeTurquoisGratis" xml:space="preserve">
    <value>Turquois gratis application</value>
  </data>
  <data name="ApplicationTypeTurquoisPremium" xml:space="preserve">
    <value>Turquois premium application</value>
  </data>
  <data name="ApplicationUnderEvaluation" xml:space="preserve">
    <value>Application is Under Process</value>
  </data>
  <data name="ApplicationWithoutPassport" xml:space="preserve">
    <value>Application without passport</value>
  </data>
  <data name="ApplicationWithPassport" xml:space="preserve">
    <value>Application with passport</value>
  </data>
  <data name="Appointment" xml:space="preserve">
    <value>Appointment</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="ApprovedWorkPermit" xml:space="preserve">
    <value>Approved work permit</value>
  </data>
  <data name="April" xml:space="preserve">
    <value>April</value>
  </data>
  <data name="Arabic" xml:space="preserve">
    <value>Arabic</value>
  </data>
  <data name="ArchaelogicalExcavation" xml:space="preserve">
    <value>Archaelogical excavation</value>
  </data>
  <data name="ArmedSecurityForce" xml:space="preserve">
    <value>Armed security force</value>
  </data>
  <data name="ArtistPerformer" xml:space="preserve">
    <value>Artist / performer</value>
  </data>
  <data name="Asseco" xml:space="preserve">
    <value>Asseco</value>
  </data>
  <data name="AssignedArtists" xml:space="preserve">
    <value>Assigned artists</value>
  </data>
  <data name="AssignedForDuty" xml:space="preserve">
    <value>Assigned for duty</value>
  </data>
  <data name="AssignedFreeZoneWorkers" xml:space="preserve">
    <value>Assigned free zone workers</value>
  </data>
  <data name="AssignedJournalist" xml:space="preserve">
    <value>Assigned journalist</value>
  </data>
  <data name="AssignedLecturersAcademics" xml:space="preserve">
    <value>Assigned lecturers academics</value>
  </data>
  <data name="AssignedSportsperson" xml:space="preserve">
    <value>Assigned sports person</value>
  </data>
  <data name="August" xml:space="preserve">
    <value>August</value>
  </data>
  <data name="AuthorizationDocument" xml:space="preserve">
    <value>Authorization document</value>
  </data>
  <data name="B2B" xml:space="preserve">
    <value>B2B</value>
  </data>
  <data name="B2C" xml:space="preserve">
    <value>B2C</value>
  </data>
  <data name="Biometrics" xml:space="preserve">
    <value>Biometrics</value>
  </data>
  <data name="BiomeTRLightCameraMotion" xml:space="preserve">
    <value>BiomeTR light-camera motion</value>
  </data>
  <data name="BirthDate" xml:space="preserve">
    <value>Birth date</value>
  </data>
  <data name="Black" xml:space="preserve">
    <value>Black</value>
  </data>
  <data name="Blue" xml:space="preserve">
    <value>Blue</value>
  </data>
  <data name="BranchApplicationCountryFileTypeDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="BranchApplicationCountryFileTypeInformation" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="BranchApplicationCountryFileTypeRequirements" xml:space="preserve">
    <value>Required files</value>
  </data>
  <data name="BreakingTravelDocuments" xml:space="preserve">
    <value>Breaking the conditions of travel documents</value>
  </data>
  <data name="Business" xml:space="preserve">
    <value>Business</value>
  </data>
  <data name="BusinessMeetingCommerce" xml:space="preserve">
    <value>Business meeting / commerce</value>
  </data>
  <data name="ByAppointment" xml:space="preserve">
    <value>By appointment</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>Camera</value>
  </data>
  <data name="Cancelled" xml:space="preserve">
    <value>Cancelled</value>
  </data>
  <data name="CaregiverAndBabysitter" xml:space="preserve">
    <value>Caregiver and babysitter</value>
  </data>
  <data name="Cargo" xml:space="preserve">
    <value>Cargo</value>
  </data>
  <data name="Child" xml:space="preserve">
    <value>Child</value>
  </data>
  <data name="CommissionOfCrime" xml:space="preserve">
    <value>Commission of a crime</value>
  </data>
  <data name="Companion" xml:space="preserve">
    <value>Companion</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="ConferenceFair" xml:space="preserve">
    <value>Conference - fair</value>
  </data>
  <data name="ConferenceSeminarMeeting" xml:space="preserve">
    <value>Conference seminar meeting</value>
  </data>
  <data name="Consenting" xml:space="preserve">
    <value>Consenting</value>
  </data>
  <data name="Construction" xml:space="preserve">
    <value>Construction</value>
  </data>
  <data name="ConsulateCanceledRefund" xml:space="preserve">
    <value>Consulate Canceled Refund</value>
  </data>
  <data name="ConsulateDecision" xml:space="preserve">
    <value>Consulate Decision</value>
  </data>
  <data name="Courier" xml:space="preserve">
    <value>Courier</value>
  </data>
  <data name="CoursePurpose" xml:space="preserve">
    <value>Course purpose</value>
  </data>
  <data name="Created" xml:space="preserve">
    <value>Created</value>
  </data>
  <data name="CriminalRecords" xml:space="preserve">
    <value>Criminal records</value>
  </data>
  <data name="CulinaryCookery" xml:space="preserve">
    <value>Culinary cookery</value>
  </data>
  <data name="CulturalArtisticActivity" xml:space="preserve">
    <value>Cultural artistic activity</value>
  </data>
  <data name="CulturalSportive" xml:space="preserve">
    <value>Cultural/sportive</value>
  </data>
  <data name="DataTravelHistoryQuestion1" xml:space="preserve">
    <value>If you have an official residence permit in a country other than your country of origin  (your nationality), do you have permission to return to that country?</value>
  </data>
  <data name="DataTravelHistoryQuestion2" xml:space="preserve">
    <value>If you are traveling to a different final destination and transiting Turkey, do you have an entry permit for your final destination country?</value>
  </data>
  <data name="DataTravelHistoryQuestion3" xml:space="preserve">
    <value>Have you ever been issued a Turkish visa or a residence/work permit?</value>
  </data>
  <data name="DataTravelHistoryQuestion4" xml:space="preserve">
    <value>Have you ever been denied for a Turkish visa or a residence/work permit?</value>
  </data>
  <data name="DataTravelHistoryQuestion5" xml:space="preserve">
    <value>Has your admission to Turkey ever been refused at the point of entry?</value>
  </data>
  <data name="DataTravelHistoryQuestion6" xml:space="preserve">
    <value>Have you ever been deported from or required to leave Turkey?</value>
  </data>
  <data name="DataTravelHistoryQuestion7" xml:space="preserve">
    <value>Have you ever overstayed your visa or residence/work permit in Turkey?</value>
  </data>
  <data name="DataTravelHistoryQuestion8" xml:space="preserve">
    <value>Have you ever committed any serious crime in Turkey?</value>
  </data>
  <data name="DataTravelHistoryQuestion9" xml:space="preserve">
    <value>Has your visa or residence/work permit ever been cancelled or revoked by Turkish authorities?</value>
  </data>
  <data name="December" xml:space="preserve">
    <value>December</value>
  </data>
  <data name="Declaration" xml:space="preserve">
    <value>Declaration</value>
  </data>
  <data name="Declined" xml:space="preserve">
    <value>Declined</value>
  </data>
  <data name="Deleted" xml:space="preserve">
    <value>Deleted</value>
  </data>
  <data name="DeliveredToApplicant" xml:space="preserve">
    <value>Delivered to applicant</value>
  </data>
  <data name="DeliveredToCargo" xml:space="preserve">
    <value>Delivered To Cargo</value>
  </data>
  <data name="DeliveredToPost" xml:space="preserve">
    <value>Delivered to post</value>
  </data>
  <data name="DiplomaticPassport" xml:space="preserve">
    <value>Diplomatic pasaport</value>
  </data>
  <data name="DocumentEditing" xml:space="preserve">
    <value>Document Editing</value>
  </data>
  <data name="DocumentEditingService" xml:space="preserve">
    <value>Document Preparation Services</value>
  </data>
  <data name="DocumentManagement" xml:space="preserve">
    <value>Document management</value>
  </data>
  <data name="DoneQualityCheck" xml:space="preserve">
    <value>Done quality check</value>
  </data>
  <data name="Donor" xml:space="preserve">
    <value>Donor</value>
  </data>
  <data name="DoubleTransit" xml:space="preserve">
    <value>Double transit</value>
  </data>
  <data name="Driver" xml:space="preserve">
    <value>Driver</value>
  </data>
  <data name="DriverLorry" xml:space="preserve">
    <value>Driver lorry</value>
  </data>
  <data name="DZD" xml:space="preserve">
    <value>DZD</value>
  </data>
  <data name="EducationAndTraining" xml:space="preserve">
    <value>Education and training</value>
  </data>
  <data name="EducationInTurkishRepublicOfNorthernCyprus" xml:space="preserve">
    <value>Education in Turkish Republic of Northern Cyprus</value>
  </data>
  <data name="EducationPurpose" xml:space="preserve">
    <value>Education purpose</value>
  </data>
  <data name="Emaa" xml:space="preserve">
    <value>Emaa</value>
  </data>
  <data name="EmployementPurposeSpecialEmploymentPurpose" xml:space="preserve">
    <value>Employement purpose special employment purpose</value>
  </data>
  <data name="Engineer" xml:space="preserve">
    <value>Engineer</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="EntryBanned" xml:space="preserve">
    <value>Entry banned</value>
  </data>
  <data name="ErrorOccurred" xml:space="preserve">
    <value>Error occurred</value>
  </data>
  <data name="EUR" xml:space="preserve">
    <value>EUR</value>
  </data>
  <data name="Evisa" xml:space="preserve">
    <value>E-Visa</value>
  </data>
  <data name="ExistingData" xml:space="preserve">
    <value>Existing data</value>
  </data>
  <data name="Expired" xml:space="preserve">
    <value>Expired</value>
  </data>
  <data name="ExtraFile" xml:space="preserve">
    <value>Extra file</value>
  </data>
  <data name="FaceAnalysisService" xml:space="preserve">
    <value>Face analysis service</value>
  </data>
  <data name="FailedQualityCheck" xml:space="preserve">
    <value>Failed quality check</value>
  </data>
  <data name="FamilyAndFriendVisit" xml:space="preserve">
    <value>Family and friend visit</value>
  </data>
  <data name="FamilyUnificationPurpose" xml:space="preserve">
    <value>Family unification purpose</value>
  </data>
  <data name="FamilyUnion" xml:space="preserve">
    <value>Family union</value>
  </data>
  <data name="FatherName" xml:space="preserve">
    <value>Father name</value>
  </data>
  <data name="Faulty" xml:space="preserve">
    <value>Faulty</value>
  </data>
  <data name="FaultyDocumentation" xml:space="preserve">
    <value>Faulty documentation</value>
  </data>
  <data name="Febraury" xml:space="preserve">
    <value>Febraury</value>
  </data>
  <data name="Female" xml:space="preserve">
    <value>Female</value>
  </data>
  <data name="FestivalFairExhibition" xml:space="preserve">
    <value>Festival fair exhibition</value>
  </data>
  <data name="Fifteen_TwentyFive" xml:space="preserve">
    <value>15-25 years old</value>
  </data>
  <data name="FileCouldNotBeUploaded" xml:space="preserve">
    <value>File could not be uploaded</value>
  </data>
  <data name="FileWithdrewAccordingtoCustomerRequest" xml:space="preserve">
    <value>File Withdrew According To Customer Request</value>
  </data>
  <data name="FilmingDocumentaryPurpose" xml:space="preserve">
    <value>Filming documentary purpose</value>
  </data>
  <data name="FinanceAndBanking" xml:space="preserve">
    <value>Finance and banking</value>
  </data>
  <data name="FingerprintReader" xml:space="preserve">
    <value>Fingerprint reader</value>
  </data>
  <data name="FlexibleAppointment" xml:space="preserve">
    <value>Flexible Appointment</value>
  </data>
  <data name="FlightTicket" xml:space="preserve">
    <value>Flight ticket</value>
  </data>
  <data name="FreeGWServiceFee" xml:space="preserve">
    <value>No visa service fee</value>
  </data>
  <data name="FreeVisaFee" xml:space="preserve">
    <value>No visa fee</value>
  </data>
  <data name="FreightVisa" xml:space="preserve">
    <value>Freight visa</value>
  </data>
  <data name="Friday" xml:space="preserve">
    <value>Friday</value>
  </data>
  <data name="Garanti" xml:space="preserve">
    <value>Garanti</value>
  </data>
  <data name="Gender" xml:space="preserve">
    <value>Gender</value>
  </data>
  <data name="Government" xml:space="preserve">
    <value>Government</value>
  </data>
  <data name="Green" xml:space="preserve">
    <value>Green</value>
  </data>
  <data name="HandDeliveredToApplicantAtEmbassy" xml:space="preserve">
    <value>Handed over to applicant from embassy</value>
  </data>
  <data name="HandDeliveredToTheApplicant" xml:space="preserve">
    <value>Hand Delivered To The Applicant</value>
  </data>
  <data name="HavingAndInadmissibleRelative" xml:space="preserve">
    <value>Having and inadmissible relative</value>
  </data>
  <data name="Health" xml:space="preserve">
    <value>Health</value>
  </data>
  <data name="HealthGround" xml:space="preserve">
    <value>Health ground</value>
  </data>
  <data name="HealthInsurance" xml:space="preserve">
    <value>Health Insurance</value>
  </data>
  <data name="HealthMedical" xml:space="preserve">
    <value>Health / medical</value>
  </data>
  <data name="HealthPublicSafety" xml:space="preserve">
    <value>Health and public safety</value>
  </data>
  <data name="HimselfHerself" xml:space="preserve">
    <value>Himself/Herself</value>
  </data>
  <data name="HostComapny" xml:space="preserve">
    <value>Host comapny</value>
  </data>
  <data name="HostPerson" xml:space="preserve">
    <value>Host person</value>
  </data>
  <data name="HotelReservation" xml:space="preserve">
    <value>Hotel reservation</value>
  </data>
  <data name="HusbandOrWife" xml:space="preserve">
    <value>Husband/Wife</value>
  </data>
  <data name="IdentificationNumber" xml:space="preserve">
    <value>Identification number</value>
  </data>
  <data name="IllegalEntryCounrty" xml:space="preserve">
    <value>Illegal entry to the counrty</value>
  </data>
  <data name="IncompletedApplication" xml:space="preserve">
    <value>Incomplete application during the day</value>
  </data>
  <data name="IncorrectApplicationStatusReport" xml:space="preserve">
    <value>Incorrect Application Status Correction Report</value>
  </data>
  <data name="IncorrectEntryIcr" xml:space="preserve">
    <value>Incorrect entry to the ICR</value>
  </data>
  <data name="Individual" xml:space="preserve">
    <value>Individual</value>
  </data>
  <data name="InformationTechnologies" xml:space="preserve">
    <value>Information technologies</value>
  </data>
  <data name="InProgress" xml:space="preserve">
    <value>In progress</value>
  </data>
  <data name="InQualityCheck" xml:space="preserve">
    <value>In quality check</value>
  </data>
  <data name="INR" xml:space="preserve">
    <value>INR</value>
  </data>
  <data name="Insurance" xml:space="preserve">
    <value>Insurance</value>
  </data>
  <data name="InsuranceError" xml:space="preserve">
    <value>Insurance error</value>
  </data>
  <data name="InternshipAisec" xml:space="preserve">
    <value>Internship aisec</value>
  </data>
  <data name="IntershipErasmus" xml:space="preserve">
    <value>Intership erasmus</value>
  </data>
  <data name="IntershipIaeste" xml:space="preserve">
    <value>Intership iaeste</value>
  </data>
  <data name="IntershipVisa" xml:space="preserve">
    <value>Intership visa</value>
  </data>
  <data name="InvalidOperation" xml:space="preserve">
    <value>Invalid operation</value>
  </data>
  <data name="InventoryType1" xml:space="preserve">
    <value>Inventory type 1</value>
  </data>
  <data name="InventoryType2" xml:space="preserve">
    <value>Inventory type 2</value>
  </data>
  <data name="InventoryType3" xml:space="preserve">
    <value>Inventory type 3</value>
  </data>
  <data name="Invitation" xml:space="preserve">
    <value>Invitation</value>
  </data>
  <data name="IQD" xml:space="preserve">
    <value>IQD</value>
  </data>
  <data name="IraqCitizen" xml:space="preserve">
    <value>Iraqi citizen</value>
  </data>
  <data name="Istizan" xml:space="preserve">
    <value>Istizan</value>
  </data>
  <data name="IstizanDelvieredToApplicant" xml:space="preserve">
    <value>Istizan passport delivered to applicant</value>
  </data>
  <data name="IstizanOutscanToEmbassy" xml:space="preserve">
    <value>Istizan outscan to embassy</value>
  </data>
  <data name="IstizanOutscanToOffice" xml:space="preserve">
    <value>Submitted istizan passport to office</value>
  </data>
  <data name="IstizanRejection" xml:space="preserve">
    <value>Istızan rejectıon</value>
  </data>
  <data name="January" xml:space="preserve">
    <value>January</value>
  </data>
  <data name="July" xml:space="preserve">
    <value>July</value>
  </data>
  <data name="June" xml:space="preserve">
    <value>June</value>
  </data>
  <data name="KWD" xml:space="preserve">
    <value>KWD</value>
  </data>
  <data name="LackTravelHealthInsurance" xml:space="preserve">
    <value>Lack of travel and / or health insurance</value>
  </data>
  <data name="Land" xml:space="preserve">
    <value>Land</value>
  </data>
  <data name="LegalProfessional" xml:space="preserve">
    <value>Legal professional</value>
  </data>
  <data name="LetterOfAcceptance" xml:space="preserve">
    <value>Letter of acceptance</value>
  </data>
  <data name="LosingLegalStatus" xml:space="preserve">
    <value>Losing legal status</value>
  </data>
  <data name="LYD" xml:space="preserve">
    <value>LYD</value>
  </data>
  <data name="MaidenName" xml:space="preserve">
    <value>Maiden name</value>
  </data>
  <data name="Male" xml:space="preserve">
    <value>Male</value>
  </data>
  <data name="March" xml:space="preserve">
    <value>March</value>
  </data>
  <data name="MaritalStatusDivorced" xml:space="preserve">
    <value>Divorced</value>
  </data>
  <data name="MaritalStatusMarried" xml:space="preserve">
    <value>Married</value>
  </data>
  <data name="MaritalStatusSingle" xml:space="preserve">
    <value>Single</value>
  </data>
  <data name="MaritalStatusWidowWidower" xml:space="preserve">
    <value>Widow/widower</value>
  </data>
  <data name="May" xml:space="preserve">
    <value>May</value>
  </data>
  <data name="MBS" xml:space="preserve">
    <value>MBS</value>
  </data>
  <data name="MedicalTreatmentPurposes" xml:space="preserve">
    <value>Medical treatment purposes</value>
  </data>
  <data name="MinistryOfHealthApplication" xml:space="preserve">
    <value>Ministry of Health Application</value>
  </data>
  <data name="MisrepresentationFactInformation" xml:space="preserve">
    <value>Misrepresentation of fact / infornmation</value>
  </data>
  <data name="MisrepresentationFalseInformation" xml:space="preserve">
    <value>Misrepresentation / false information</value>
  </data>
  <data name="MissingDocuments" xml:space="preserve">
    <value>Missing documents</value>
  </data>
  <data name="MissingOrInvalidData" xml:space="preserve">
    <value>Missing or invalid data</value>
  </data>
  <data name="Monday" xml:space="preserve">
    <value>Monday</value>
  </data>
  <data name="Montage" xml:space="preserve">
    <value>Montage</value>
  </data>
  <data name="MontageAndRepairmentPurposes" xml:space="preserve">
    <value>Montage and repairment purposes</value>
  </data>
  <data name="MoreThanEighty" xml:space="preserve">
    <value>Older than 80</value>
  </data>
  <data name="MoreThanFifty" xml:space="preserve">
    <value>50 years and more</value>
  </data>
  <data name="MotherName" xml:space="preserve">
    <value>Mother name</value>
  </data>
  <data name="Mr" xml:space="preserve">
    <value>Mr</value>
  </data>
  <data name="Mrs" xml:space="preserve">
    <value>Mrs</value>
  </data>
  <data name="Ms" xml:space="preserve">
    <value>Ms</value>
  </data>
  <data name="Multiple" xml:space="preserve">
    <value>Multiple</value>
  </data>
  <data name="Myself" xml:space="preserve">
    <value>Myself</value>
  </data>
  <data name="NansenPassport" xml:space="preserve">
    <value>Nansen pasaport</value>
  </data>
  <data name="Nationality" xml:space="preserve">
    <value>Nationality</value>
  </data>
  <data name="NavyBlue" xml:space="preserve">
    <value>Navy blue</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="NonApplicationInsurance" xml:space="preserve">
    <value>Non-application insurance</value>
  </data>
  <data name="NonApplicationPCR" xml:space="preserve">
    <value>Non application PCR</value>
  </data>
  <data name="Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="NormalApplications" xml:space="preserve">
    <value>Normal Applications</value>
  </data>
  <data name="NormalPCR" xml:space="preserve">
    <value>PCR normal</value>
  </data>
  <data name="NotOrdered" xml:space="preserve">
    <value>Not ordered</value>
  </data>
  <data name="November" xml:space="preserve">
    <value>November</value>
  </data>
  <data name="NPR" xml:space="preserve">
    <value>NPR</value>
  </data>
  <data name="October" xml:space="preserve">
    <value>October</value>
  </data>
  <data name="OfficeWaitingForMissingDocuments" xml:space="preserve">
    <value>Office Waiting For Missing Documents</value>
  </data>
  <data name="OfficialVisa" xml:space="preserve">
    <value>Official visa</value>
  </data>
  <data name="OfficialVisit" xml:space="preserve">
    <value>Official visit</value>
  </data>
  <data name="OnHold" xml:space="preserve">
    <value>On hold</value>
  </data>
  <data name="OperationIsSuccessful" xml:space="preserve">
    <value>Operation is successful</value>
  </data>
  <data name="Orange" xml:space="preserve">
    <value>Orange</value>
  </data>
  <data name="Ordered" xml:space="preserve">
    <value>Ordered</value>
  </data>
  <data name="OrdinaryPassport" xml:space="preserve">
    <value>Ordinary pasaport</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="OutscanToCourrier" xml:space="preserve">
    <value>Outscan to courrier</value>
  </data>
  <data name="OverstayedVisit" xml:space="preserve">
    <value>Overstayed visit</value>
  </data>
  <data name="Passive" xml:space="preserve">
    <value>Passive</value>
  </data>
  <data name="PassportCouldNotBeRead" xml:space="preserve">
    <value>Passport could not be read</value>
  </data>
  <data name="PassportDelivery" xml:space="preserve">
    <value>Passport delivery</value>
  </data>
  <data name="PassportPhotocopy" xml:space="preserve">
    <value>Photocopy of the passaport</value>
  </data>
  <data name="PatientCompanion" xml:space="preserve">
    <value>Patient companion</value>
  </data>
  <data name="PCR" xml:space="preserve">
    <value>PCR</value>
  </data>
  <data name="PcrCanceled" xml:space="preserve">
    <value>PCR test is canceled</value>
  </data>
  <data name="PcrIsDone" xml:space="preserve">
    <value>PCR test is done</value>
  </data>
  <data name="PCRTestCancelled" xml:space="preserve">
    <value>PCR test is cancelled</value>
  </data>
  <data name="PCRTestDone" xml:space="preserve">
    <value>Pcr test is done</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>Pending</value>
  </data>
  <data name="PendingApproval" xml:space="preserve">
    <value>Pending approval</value>
  </data>
  <data name="PersonalLoginError" xml:space="preserve">
    <value>Personal login error</value>
  </data>
  <data name="Petition" xml:space="preserve">
    <value>Petition</value>
  </data>
  <data name="Pink" xml:space="preserve">
    <value>Pink</value>
  </data>
  <data name="PKR" xml:space="preserve">
    <value>PKR</value>
  </data>
  <data name="Platinum" xml:space="preserve">
    <value>Platin</value>
  </data>
  <data name="Postponed" xml:space="preserve">
    <value>Postponed</value>
  </data>
  <data name="PressMedia" xml:space="preserve">
    <value>Press media</value>
  </data>
  <data name="PrimeTime" xml:space="preserve">
    <value>Prime time</value>
  </data>
  <data name="PrivatePCR" xml:space="preserve">
    <value>PCR private</value>
  </data>
  <data name="ProcessNotStarted" xml:space="preserve">
    <value>Process not started</value>
  </data>
  <data name="ProfessionalSportsperson" xml:space="preserve">
    <value>Professional / sportsperson</value>
  </data>
  <data name="Purchase" xml:space="preserve">
    <value>Purchase</value>
  </data>
  <data name="Purple" xml:space="preserve">
    <value>Purple</value>
  </data>
  <data name="PutOnHold" xml:space="preserve">
    <value>Put on hold</value>
  </data>
  <data name="QAR" xml:space="preserve">
    <value>QAR</value>
  </data>
  <data name="QCAllDocumentsAreOriginal" xml:space="preserve">
    <value>All documents are original seen or not seen stamp</value>
  </data>
  <data name="QCAllowedDates" xml:space="preserve">
    <value>Allowed dates</value>
  </data>
  <data name="QCApplicantNameSurnameAndPassportValidity" xml:space="preserve">
    <value>The applicant's name, surname and passport number must be written correctly</value>
  </data>
  <data name="QCArrangedForTurkeyOrWorldwide" xml:space="preserve">
    <value>It must be arranged for Turkey or worldwide</value>
  </data>
  <data name="QCBackgroundWhite" xml:space="preserve">
    <value>Background white</value>
  </data>
  <data name="QCBankAccountStatementControl" xml:space="preserve">
    <value>Bank account statement</value>
  </data>
  <data name="QCBankBalance" xml:space="preserve">
    <value>Balance if viewed</value>
  </data>
  <data name="QCBankDocumentValidity" xml:space="preserve">
    <value>Original, wet signature-stamped</value>
  </data>
  <data name="QCBusinessInvitationApplicantNameSurname" xml:space="preserve">
    <value>Applicant's name-number is written completely and clearly</value>
  </data>
  <data name="QCBusinessInvitationContactInformation" xml:space="preserve">
    <value>Has contact information and the invitation letter is legible</value>
  </data>
  <data name="QCBusinessInvitationDocumentValidity" xml:space="preserve">
    <value>Signed-stamped by the inviting company</value>
  </data>
  <data name="QCBusinessInvitationLetterControl" xml:space="preserve">
    <value>Business invitation letter</value>
  </data>
  <data name="QCBusinessInvitationUpToDate" xml:space="preserve">
    <value>Invitation up to date</value>
  </data>
  <data name="QCCompanyDocuments" xml:space="preserve">
    <value>Company documents, if any</value>
  </data>
  <data name="QCConsentLetterControl" xml:space="preserve">
    <value>Consent letter</value>
  </data>
  <data name="QCConsentLetterValidity" xml:space="preserve">
    <value>Yes, conforming to standards, up-to-date, original</value>
  </data>
  <data name="QCCustodyOrDeathCertificate" xml:space="preserve">
    <value>No custody or death certificate</value>
  </data>
  <data name="QCDataControlAtoZ" xml:space="preserve">
    <value>Data control A-Z</value>
  </data>
  <data name="QCDemographicInformation" xml:space="preserve">
    <value>Demographic information</value>
  </data>
  <data name="QCDepartureDate" xml:space="preserve">
    <value>Departure date</value>
  </data>
  <data name="QCDestinationCityInTurkey" xml:space="preserve">
    <value>A city in Turkey</value>
  </data>
  <data name="QCEmployerLetterControl" xml:space="preserve">
    <value>Employer letter</value>
  </data>
  <data name="QCEmployerLetterValidty" xml:space="preserve">
    <value>Original letterhead, wet signature-stamped, current dated</value>
  </data>
  <data name="QCExistingTurkeyVisaStatus" xml:space="preserve">
    <value>If there is an old visa check, not having a valid Turkish visa or being under a certain period of time</value>
  </data>
  <data name="QCFlightApplicantNameSurname" xml:space="preserve">
    <value>Applicant name-surname</value>
  </data>
  <data name="QCFlightMainDirection" xml:space="preserve">
    <value>Air ticket and visa for the country of primary travel if transit or application</value>
  </data>
  <data name="QCFlightRoundTripReservation" xml:space="preserve">
    <value>Round-trip reservation</value>
  </data>
  <data name="QCFlightTicketReservationControl" xml:space="preserve">
    <value>Flight ticket reservation</value>
  </data>
  <data name="QCFuneralIncluded" xml:space="preserve">
    <value>Must pay for funeral expenses</value>
  </data>
  <data name="QCHospitalInvitationApplicantNameSurname" xml:space="preserve">
    <value>Applicant's name and surname (name and surname of accompanying persons) are written in full and clearly</value>
  </data>
  <data name="QCHospitalInvitationContactInformation" xml:space="preserve">
    <value>Has contact information and the invitation letter is legible</value>
  </data>
  <data name="QCHospitalInvitationDocumentValidity" xml:space="preserve">
    <value>Signed-stamped by the hospital</value>
  </data>
  <data name="QCHospitalInvitationLetterControl" xml:space="preserve">
    <value>Hospital invitation letter</value>
  </data>
  <data name="QCHospitalInvitationUpToDate" xml:space="preserve">
    <value>Invitation up to date</value>
  </data>
  <data name="QCHotelApplicantNameSurname" xml:space="preserve">
    <value>Applicant name-surname</value>
  </data>
  <data name="QCHotelReservationControl" xml:space="preserve">
    <value>Hotel reservation</value>
  </data>
  <data name="QCHotelReservationInTurkey" xml:space="preserve">
    <value>A city in Turkey</value>
  </data>
  <data name="QCHotelValidityForRoundTripFlight" xml:space="preserve">
    <value>Round-trip airfare must be included</value>
  </data>
  <data name="QCIdCheck" xml:space="preserve">
    <value>ID check</value>
  </data>
  <data name="QCIllnessReports" xml:space="preserve">
    <value>All reports of his illness</value>
  </data>
  <data name="QCInstitutionAddress" xml:space="preserve">
    <value>Institution addressed</value>
  </data>
  <data name="QCInsuranceMinumGuarantee" xml:space="preserve">
    <value>Must have a minimum guarantee of 30.000 €</value>
  </data>
  <data name="QCInsuranceValidityForRoundTripFlight" xml:space="preserve">
    <value>Round-trip airfare must be included</value>
  </data>
  <data name="QCLastSixMonths" xml:space="preserve">
    <value>Last 6 months</value>
  </data>
  <data name="QCOccupationAndSalaryInformation" xml:space="preserve">
    <value>Occupation and salary information</value>
  </data>
  <data name="QCPassportControl" xml:space="preserve">
    <value>Passport control</value>
  </data>
  <data name="QCPassportHasBlankPages" xml:space="preserve">
    <value>Have at least 2 blank pages</value>
  </data>
  <data name="QCPassportNotDamaged" xml:space="preserve">
    <value>Not worn or damaged</value>
  </data>
  <data name="QCPassportStampValidity" xml:space="preserve">
    <value>Checking stamps in passport</value>
  </data>
  <data name="QCPasswordExpireDateValidadtion" xml:space="preserve">
    <value>Valid for at least 6 months from the date of departure to Turkey</value>
  </data>
  <data name="QCPermissiveParentSigniture" xml:space="preserve">
    <value>Permissive parent signature</value>
  </data>
  <data name="QCPhotoCheck" xml:space="preserve">
    <value>Photo check</value>
  </data>
  <data name="QCPhotoCurrentDated" xml:space="preserve">
    <value>Current date</value>
  </data>
  <data name="QCResidenceCheck" xml:space="preserve">
    <value>Residence check</value>
  </data>
  <data name="QCResidenceDurationValidity" xml:space="preserve">
    <value>Valid for at least 6 months from the date of departure to Turkey</value>
  </data>
  <data name="QCSchoolAcceptanceApplicantNameSurname" xml:space="preserve">
    <value>Applicant's name-number is written completely and clearly</value>
  </data>
  <data name="QCSchoolAcceptanceContactInformation" xml:space="preserve">
    <value>Has contact information and the invitation letter is legible</value>
  </data>
  <data name="QCSchoolAcceptanceDocumentValidity" xml:space="preserve">
    <value>Signed-stamped by the school official</value>
  </data>
  <data name="QCSchoolAcceptanceLetterControl" xml:space="preserve">
    <value>School acceptance letter</value>
  </data>
  <data name="QCSchoolAcceptanceLetterForCurrentDate" xml:space="preserve">
    <value>Acceptance letter with current date</value>
  </data>
  <data name="QCSchoolAcceptancepaymentOrScolarship" xml:space="preserve">
    <value>Receipt of payment or scholarship certificate</value>
  </data>
  <data name="QCSchoolAcceptanceTranscript" xml:space="preserve">
    <value>Previous graduation certificate and transcript</value>
  </data>
  <data name="QCStandardValidity" xml:space="preserve">
    <value>Conforming to standards</value>
  </data>
  <data name="QCSuitableForPhotoSize" xml:space="preserve">
    <value>Suitable for photo size</value>
  </data>
  <data name="QCTravelDate" xml:space="preserve">
    <value>Travel date</value>
  </data>
  <data name="QCTravelHealthInsuranceControl" xml:space="preserve">
    <value>Travel health insurance (in case of external admission)</value>
  </data>
  <data name="QCTravelWithParent" xml:space="preserve">
    <value>Travel together; parent has valid visa and air ticket</value>
  </data>
  <data name="QCVisaCategory" xml:space="preserve">
    <value>Visa category</value>
  </data>
  <data name="QCVisaType" xml:space="preserve">
    <value>Visa type</value>
  </data>
  <data name="QueueMatic" xml:space="preserve">
    <value>Queue matic</value>
  </data>
  <data name="ReBiometry" xml:space="preserve">
    <value>Rebiometry</value>
  </data>
  <data name="ReceivedAtEmbassy" xml:space="preserve">
    <value>Received at embassy</value>
  </data>
  <data name="ReceivedAtVAC" xml:space="preserve">
    <value>Received at VAC</value>
  </data>
  <data name="ReceivedAtVisaCenter" xml:space="preserve">
    <value>Received at Visa Center</value>
  </data>
  <data name="RecievedAtVacForKuwait" xml:space="preserve">
    <value>Recieved at vac for kuwait</value>
  </data>
  <data name="RecievedAtVisaCenter" xml:space="preserve">
    <value>Recieved at visa center</value>
  </data>
  <data name="RecordNotFound" xml:space="preserve">
    <value>Record not found</value>
  </data>
  <data name="Red" xml:space="preserve">
    <value>Red</value>
  </data>
  <data name="RefugeePassport" xml:space="preserve">
    <value>Refugee pasaport</value>
  </data>
  <data name="Refund" xml:space="preserve">
    <value>Refund</value>
  </data>
  <data name="Refunded" xml:space="preserve">
    <value>Refunded</value>
  </data>
  <data name="RegistrationPlaceCity" xml:space="preserve">
    <value>Registration place (city)</value>
  </data>
  <data name="RegistrationPlaceCountry" xml:space="preserve">
    <value>Registration place (country)</value>
  </data>
  <data name="ReimbursementTypeHimselfHerself" xml:space="preserve">
    <value>Himself/herself</value>
  </data>
  <data name="ReimbursementTypeNoFinancialDocument" xml:space="preserve">
    <value>No financial document</value>
  </data>
  <data name="ReimbursementTypeSponsor" xml:space="preserve">
    <value>Sponsor</value>
  </data>
  <data name="Rejected" xml:space="preserve">
    <value>Rejected</value>
  </data>
  <data name="RejectedPassportDeliveredToCourier" xml:space="preserve">
    <value>Outscan passport with rejection to courier</value>
  </data>
  <data name="Rejection" xml:space="preserve">
    <value>Rejection</value>
  </data>
  <data name="RejectionRefundDone" xml:space="preserve">
    <value>Refund is done</value>
  </data>
  <data name="RejectionWithCountryEntryBanned" xml:space="preserve">
    <value>Rejection to enter the country</value>
  </data>
  <data name="ReligiousFunctionary" xml:space="preserve">
    <value>Religious functionary</value>
  </data>
  <data name="Repairing" xml:space="preserve">
    <value>Repairing</value>
  </data>
  <data name="ReportAllBranchesDailyInsurance" xml:space="preserve">
    <value>Branch based insurance cash report</value>
  </data>
  <data name="ReportAllBranchesInsurance" xml:space="preserve">
    <value>All branches monthly insurance report</value>
  </data>
  <data name="ReportCancelCompletedApplications" xml:space="preserve">
    <value>Cancellation of completed applications report</value>
  </data>
  <data name="ReportCargo" xml:space="preserve">
    <value>Cargo report</value>
  </data>
  <data name="ReportCargoCompanyPayment" xml:space="preserve">
    <value>Cargo company payment report</value>
  </data>
  <data name="ReportCashReport_1" xml:space="preserve">
    <value>Cash report 1</value>
  </data>
  <data name="ReportCashReport_2" xml:space="preserve">
    <value>Cash report 2</value>
  </data>
  <data name="ReportConsular" xml:space="preserve">
    <value>Consular report</value>
  </data>
  <data name="ReportDeliveredToCargo" xml:space="preserve">
    <value>Delivered to cargo report</value>
  </data>
  <data name="ReportExtraFees" xml:space="preserve">
    <value>Extra fees report</value>
  </data>
  <data name="ReportInsuranceCancellation" xml:space="preserve">
    <value>Insurance cancellation report</value>
  </data>
  <data name="ReportInsuranceCancellationForRefund" xml:space="preserve">
    <value>Refund with insurance report</value>
  </data>
  <data name="ReportInsuranceDetail" xml:space="preserve">
    <value>Insurance detail report</value>
  </data>
  <data name="ReportNonApplicationInsurance" xml:space="preserve">
    <value>Non-appilication insurance report</value>
  </data>
  <data name="ReportOldCashReport_1" xml:space="preserve">
    <value>Old cash report-1 (before 13.08.2022)</value>
  </data>
  <data name="ReportOldCashReport_2" xml:space="preserve">
    <value>Old cash report-2 (before 13.08.2022)</value>
  </data>
  <data name="ReportPCRCancellation" xml:space="preserve">
    <value>PCR cancellation report</value>
  </data>
  <data name="ReportPCRCompanyPayment" xml:space="preserve">
    <value>PCR company payment report</value>
  </data>
  <data name="ReportPCRGeneral" xml:space="preserve">
    <value>PCR general report</value>
  </data>
  <data name="ReportPCRPayment" xml:space="preserve">
    <value>PCR payment report</value>
  </data>
  <data name="ReportQMS" xml:space="preserve">
    <value>QMS report</value>
  </data>
  <data name="ReportQMSPersonal" xml:space="preserve">
    <value>Personal QMS report</value>
  </data>
  <data name="ReportQMSTimeline" xml:space="preserve">
    <value>QMS timeline report</value>
  </data>
  <data name="ReportRejectionStatus" xml:space="preserve">
    <value>Rejection return report</value>
  </data>
  <data name="ReportTypeAllApplications" xml:space="preserve">
    <value>All applications report</value>
  </data>
  <data name="ReportTypeAllStaffApplicationsByBranch" xml:space="preserve">
    <value>All staff applications report</value>
  </data>
  <data name="ReportTypeCancelledInsurance" xml:space="preserve">
    <value>Insurance cancellation report</value>
  </data>
  <data name="ReportTypeDailyBalance" xml:space="preserve">
    <value>Daily balance report</value>
  </data>
  <data name="ReportTypeDeletedApplications" xml:space="preserve">
    <value>Deleted applications report</value>
  </data>
  <data name="ReportTypeDetail" xml:space="preserve">
    <value>Detail report</value>
  </data>
  <data name="ReportTypeFreeApplications" xml:space="preserve">
    <value>Free applications report</value>
  </data>
  <data name="ReportTypeInsurance" xml:space="preserve">
    <value>Insurance report</value>
  </data>
  <data name="ReportTypeInsuranceApplications" xml:space="preserve">
    <value>Insurance application report</value>
  </data>
  <data name="ReportTypePartiallyRefundedApplications" xml:space="preserve">
    <value>Partially refunded applications report</value>
  </data>
  <data name="ReportTypePcrDaily" xml:space="preserve">
    <value>Daily PCR test report</value>
  </data>
  <data name="ReportTypePcrStatus" xml:space="preserve">
    <value>PCR status report</value>
  </data>
  <data name="ReportTypePhotobooth" xml:space="preserve">
    <value>Photobooth report</value>
  </data>
  <data name="ReportTypeSafe" xml:space="preserve">
    <value>Safe report</value>
  </data>
  <data name="ReportTypeScanCycle" xml:space="preserve">
    <value>Scan cycle report</value>
  </data>
  <data name="ReportTypeStaffExtraFeeSales" xml:space="preserve">
    <value>Staff based applications report + VAS</value>
  </data>
  <data name="ReportVisaRejection" xml:space="preserve">
    <value>Visa rejection report</value>
  </data>
  <data name="RepublicOfTurkeyDirectorateGeneralOfMigrationManagement" xml:space="preserve">
    <value>Republic of turkey directorate general of migration management</value>
  </data>
  <data name="RepublicTurkeyDirectorateGeneralMigrationManagement" xml:space="preserve">
    <value>Republic of turkey directorate general of migration management</value>
  </data>
  <data name="RepublicTurkeyMinistryCustomsTrade" xml:space="preserve">
    <value>Republic of Turkey Ministry of Customs and Trade</value>
  </data>
  <data name="RepublicTurkeyMinistryLaborSocialSecurity" xml:space="preserve">
    <value>Republic of turkey ministry of labor and social security</value>
  </data>
  <data name="RepublicTurkeyMinistryMinistryForeignAfffairs" xml:space="preserve">
    <value>Republic of turkey ministry of ministry of foreign afffairs</value>
  </data>
  <data name="ResearcherScientist" xml:space="preserve">
    <value>Researcher scientist</value>
  </data>
  <data name="ResidencePermit" xml:space="preserve">
    <value>Residence permit</value>
  </data>
  <data name="Retired" xml:space="preserve">
    <value>Retired</value>
  </data>
  <data name="RetrospectiveCancellation" xml:space="preserve">
    <value>Retrospective cancellation</value>
  </data>
  <data name="Sale" xml:space="preserve">
    <value>Sale</value>
  </data>
  <data name="SameDayCancellation" xml:space="preserve">
    <value>Same day cancellation</value>
  </data>
  <data name="SAR" xml:space="preserve">
    <value>SAR</value>
  </data>
  <data name="Saturday" xml:space="preserve">
    <value>Saturday</value>
  </data>
  <data name="Scanner" xml:space="preserve">
    <value>Scanner</value>
  </data>
  <data name="Sea" xml:space="preserve">
    <value>Sea</value>
  </data>
  <data name="Seafarer" xml:space="preserve">
    <value>Seafarer</value>
  </data>
  <data name="SeafarerVisa" xml:space="preserve">
    <value>Seafarer visa</value>
  </data>
  <data name="SecurityBreach" xml:space="preserve">
    <value>Security breach</value>
  </data>
  <data name="SecurityReason" xml:space="preserve">
    <value>Security reason</value>
  </data>
  <data name="SelfEmployed" xml:space="preserve">
    <value>Self-Employed</value>
  </data>
  <data name="SendToEmbassyForFinalApproval" xml:space="preserve">
    <value>Outscan istizan passport to embassy</value>
  </data>
  <data name="Sent" xml:space="preserve">
    <value>Sent</value>
  </data>
  <data name="SentCenterDueToMissingDocuments" xml:space="preserve">
    <value>Sent to application center due to missing documents</value>
  </data>
  <data name="September" xml:space="preserve">
    <value>September</value>
  </data>
  <data name="Servant" xml:space="preserve">
    <value>Servant</value>
  </data>
  <data name="ServerError" xml:space="preserve">
    <value>Server error</value>
  </data>
  <data name="ServiceFee" xml:space="preserve">
    <value>Service  fee</value>
  </data>
  <data name="ServicePassport" xml:space="preserve">
    <value>Service pasaport</value>
  </data>
  <data name="ServiceSector" xml:space="preserve">
    <value>Service sector</value>
  </data>
  <data name="SeventyFive_Eighty" xml:space="preserve">
    <value>76-80 years old</value>
  </data>
  <data name="Seventy_SeventyFive" xml:space="preserve">
    <value>71-75 years old</value>
  </data>
  <data name="SignaturePad" xml:space="preserve">
    <value>Signature pad</value>
  </data>
  <data name="Single" xml:space="preserve">
    <value>Single</value>
  </data>
  <data name="SingleVisit" xml:space="preserve">
    <value>Single visit</value>
  </data>
  <data name="SixtySix_Seventy" xml:space="preserve">
    <value>66-70 years old</value>
  </data>
  <data name="SpecialPassport" xml:space="preserve">
    <value>Special pasaport</value>
  </data>
  <data name="Sportive" xml:space="preserve">
    <value>Sportive</value>
  </data>
  <data name="SportiveActivity" xml:space="preserve">
    <value>Sportive activity</value>
  </data>
  <data name="StampVisa" xml:space="preserve">
    <value>Stamp visa</value>
  </data>
  <data name="StickerVisa" xml:space="preserve">
    <value>Sticker visa</value>
  </data>
  <data name="Student" xml:space="preserve">
    <value>Student</value>
  </data>
  <data name="StudentCompanion" xml:space="preserve">
    <value>Student companion</value>
  </data>
  <data name="StudentEducationVisa" xml:space="preserve">
    <value>Student / education visa</value>
  </data>
  <data name="StudentTrainee" xml:space="preserve">
    <value>Student trainee</value>
  </data>
  <data name="Sunday" xml:space="preserve">
    <value>Sunday</value>
  </data>
  <data name="SystemError" xml:space="preserve">
    <value>System error</value>
  </data>
  <data name="Thursday" xml:space="preserve">
    <value>Thursday</value>
  </data>
  <data name="TMT" xml:space="preserve">
    <value>TMT</value>
  </data>
  <data name="TokenNotCreated" xml:space="preserve">
    <value>Token not created</value>
  </data>
  <data name="Tourism" xml:space="preserve">
    <value>Tourism</value>
  </data>
  <data name="TouristBusinessperson" xml:space="preserve">
    <value>Tourist / businessperson</value>
  </data>
  <data name="Touristic" xml:space="preserve">
    <value>Touristic</value>
  </data>
  <data name="TouristicVisit" xml:space="preserve">
    <value>Touristic visit</value>
  </data>
  <data name="TourOperatorRepresentative" xml:space="preserve">
    <value>Tour operator representative</value>
  </data>
  <data name="Train" xml:space="preserve">
    <value>Train</value>
  </data>
  <data name="Transit" xml:space="preserve">
    <value>Transit</value>
  </data>
  <data name="TRY" xml:space="preserve">
    <value>TRY</value>
  </data>
  <data name="Tuesday" xml:space="preserve">
    <value>Tuesday</value>
  </data>
  <data name="Turkish" xml:space="preserve">
    <value>Turkish</value>
  </data>
  <data name="TurkishBorderGates" xml:space="preserve">
    <value>Turkish border gates</value>
  </data>
  <data name="TurkishLanguageCoursePurpose" xml:space="preserve">
    <value>Turkish language course purpose</value>
  </data>
  <data name="TurkishMissions" xml:space="preserve">
    <value>Turkish missions</value>
  </data>
  <data name="TurkishNationalPolice" xml:space="preserve">
    <value>Turkish national police</value>
  </data>
  <data name="TwentyFive_Fifty" xml:space="preserve">
    <value>25-50 years old</value>
  </data>
  <data name="TypeChanged" xml:space="preserve">
    <value>Application type changed</value>
  </data>
  <data name="Unemployed" xml:space="preserve">
    <value>Unemployed</value>
  </data>
  <data name="Unico" xml:space="preserve">
    <value>Unico</value>
  </data>
  <data name="UnInsuredApplications" xml:space="preserve">
    <value>Uninsured applications report</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="Unspecified" xml:space="preserve">
    <value>Unspecified</value>
  </data>
  <data name="USD" xml:space="preserve">
    <value>USD</value>
  </data>
  <data name="Used" xml:space="preserve">
    <value>Used</value>
  </data>
  <data name="ViolationInternationalProtection" xml:space="preserve">
    <value>Violation of law on foreigners and international protection no 6458</value>
  </data>
  <data name="VIP" xml:space="preserve">
    <value>VIP</value>
  </data>
  <data name="Visa" xml:space="preserve">
    <value>Visa</value>
  </data>
  <data name="VisaDecisionReport" xml:space="preserve">
    <value>Visa Decision Report</value>
  </data>
  <data name="VisaEntryTypeMultiple" xml:space="preserve">
    <value>Multiple</value>
  </data>
  <data name="VisaEntryTypeSingle" xml:space="preserve">
    <value>Single</value>
  </data>
  <data name="VisaTransfer" xml:space="preserve">
    <value>Visa transfer</value>
  </data>
  <data name="VisitToTurkishRepublicOfNorthernCyprus" xml:space="preserve">
    <value>Visit to Turkish Republic of Northern Cyprus</value>
  </data>
  <data name="WaitingApproval" xml:space="preserve">
    <value>Waiting Approval</value>
  </data>
  <data name="WaitingForAdditionalDocuments" xml:space="preserve">
    <value>Waiting for additional documents</value>
  </data>
  <data name="WaitingForDelivery" xml:space="preserve">
    <value>Waiting for delivery</value>
  </data>
  <data name="WaittingForQualityCheck" xml:space="preserve">
    <value>Waitting for quality check</value>
  </data>
  <data name="WaittingToBeCompletedData" xml:space="preserve">
    <value>Waitting to be completed data</value>
  </data>
  <data name="WaittingToBeSentToQualityCheck" xml:space="preserve">
    <value>Waitting to be sent to qualityCheck</value>
  </data>
  <data name="WalkIn" xml:space="preserve">
    <value>Walk in</value>
  </data>
  <data name="Wednesday" xml:space="preserve">
    <value>Wednesday</value>
  </data>
  <data name="White" xml:space="preserve">
    <value>White</value>
  </data>
  <data name="Wife" xml:space="preserve">
    <value>Wife</value>
  </data>
  <data name="WithdrawalByApplicant" xml:space="preserve">
    <value>Withdrawal by applicant</value>
  </data>
  <data name="WithoutReference" xml:space="preserve">
    <value>Without reference</value>
  </data>
  <data name="WithReference" xml:space="preserve">
    <value>With reference</value>
  </data>
  <data name="WorkPermit" xml:space="preserve">
    <value>Work permit</value>
  </data>
  <data name="WorkPermitCompanion" xml:space="preserve">
    <value>Work permit companion</value>
  </data>
  <data name="WorkVisa" xml:space="preserve">
    <value>Work visa</value>
  </data>
  <data name="WrongBranchNotification" xml:space="preserve">
    <value>Application is not taken from this branch</value>
  </data>
  <data name="Yellow" xml:space="preserve">
    <value>Yellow</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="Zero_Fifteen" xml:space="preserve">
    <value>0-15 years old</value>
  </data>
  <data name="Zero_SixtyFive" xml:space="preserve">
    <value>0-65 yeras old</value>
  </data>
</root>