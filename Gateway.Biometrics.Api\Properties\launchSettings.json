{"profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "weatherforecast", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Gateway.Biometrics.Api-Development": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "https://localhost:5011;http://localhost:5010"}, "Gateway.Biometrics.Api-Staging": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Staging"}, "applicationUrl": "https://localhost:5011;http://localhost:5010"}, "Gateway.Biometrics.Api-Production": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production"}, "applicationUrl": "https://localhost:5011;http://localhost:5010"}, "WSL": {"commandName": "WSL2", "launchBrowser": true, "launchUrl": "https://localhost:5011/weatherforecast", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:5011;http://localhost:5010"}, "distributionName": ""}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:3137", "sslPort": 44303}}}