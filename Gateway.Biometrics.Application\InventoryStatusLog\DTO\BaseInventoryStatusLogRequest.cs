﻿using Gateway.Biometrics.Entity.Enum;
using System.ComponentModel.DataAnnotations;
using Gateway.Biometrics.Application.Inventory.DTO;

namespace Gateway.Biometrics.Application.InventoryStatusLog.DTO
{
    public class BaseInventoryStatusLogRequest : BaseServiceRequest
    {
        public int Id { get; set; }
        public string HostName { get; set; }
        public int InventoryId { get; set; }
        public int CabinId { get; set; }
        public int OfficeId { get; set; }
        public int CountryId { get; set; }
        public int ProvinceId { get; set; }
        public string ErrorMessage { get; set; }
        public string AppFileVersion { get; set; }
        public InventoryStatusEnum Status { get; set; }
        public BaseInventoryStatusLogRequest()
        {

        }
    }


}

