﻿using System.Collections.Generic;
using System;
using Gateway.Core.Pagination;
using System.ComponentModel.DataAnnotations;
using Gateway.Biometrics.Application;
using Gateway.Biometrics.Entity.Enum;
using Gateway.Biometrics.Api.Models.Cabin;

namespace Gateway.Biometrics.Api.Models.InventoryStatusLog
{
    public class BaseInventoryStatusLogRequestModel : BaseServiceRequest
    {
        public int Id { get; set; }
        public string HostName { get; set; }
        public int InventoryId { get; set; }
        public int CabinId { get; set; }
        public int OfficeId { get; set; }
        public int CountryId { get; set; }
        public int ProvinceId { get; set; }
        public string ErrorMessage { get; set; }
        public string AppFileVersion { get; set; }
        public InventoryStatusEnum Status { get; set; }
        public BaseInventoryStatusLogRequestModel()
        {

        }
    }

    public class GetInventoryStatusLogRequestModel : BaseServiceRequest
    {
        [Required]
        public int ResourceId { get; set; }
    }

    public class CreateInventoryStatusLogRequestModel : BaseInventoryStatusLogRequestModel
    {
    }

    public class GetPaginatedInventoryStatusLogsRequestModel
    {
        public int? FilterInventoryId { get; set; }
        public PaginationRequest Pagination { get; set; }
    }
}