﻿namespace Gateway.Cargo.LastMile.Dto.Result
{
    public class LastMileGetAreaResult : LastMileBaseServiceListResult<BaseLastMileStatus, LastMileGetAreaResult.GetAreaResult>
    {
        public class GetAreaResult
        {
            public string Id { get; set; }
            public List<AreaTitleItems> Title { get; set; }
            public string Description { get; set; }
            public string GovernorateId { get; set; }
            public bool? IsActive { get; set; }

            public class AreaTitleItems
            {
                public string Title { get; set; }
                public string LanguageCode { get; set; }
            }
        }
        
    }
}
