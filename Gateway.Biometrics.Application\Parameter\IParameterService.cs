﻿
using System.Threading.Tasks;
using Gateway.Biometrics.Application.Office.DTO;
using Gateway.Biometrics.Application.Parameter.DTO;

namespace Gateway.Biometrics.Application.Parameter
{
    public interface IParameterService
    {


        Task<ApiResponse<OfficesDto>> GetOfficesAsync(OfficesRequestDto request);
        Task<ApiResponse<CabinsDto>> GetCabinsAsync(CabinsRequestDto request);
        Task<ApiResponse<InventoryTypesDto>> GetInventoryTypesAsync(InventoryTypesRequestDto request);
        Task<ApiResponse<InventoryDefinitionsDto>> GetInventoryDefinitionsAsync(InventoryDefinitionsRequestDto request);
        Task<ApiResponse<InventoryValueSetsEmptyDto>> GetInventoryValueSetsEmptyAsync();
        Task<ApiResponse<ClientConfigurationInventoriesUnAssignedDto>> GetClientConfigurationInventoriesUnAssignedAsync();
        Task<ApiResponse<CabinInventoriesUnAssignedDto>> GetCabinInventoriesUnAssignedAsync();
        Task<ApiResponse<NeurotecLicensesDto>> GetNeurotecLicensesSelectAsync(NeurotecLicensesRequestDto request);



    }
}
