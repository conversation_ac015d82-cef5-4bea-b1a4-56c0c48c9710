﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gateway.Cargo.Entity.Entities.Branch
{
    public class BranchTranslation
    {
        public BranchTranslation()
        {
            IsActive = true;
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(200)]
        public string Name { get; set; }

        [Required]
        public int BranchId { get; set; }

        [Required]
        public int LanguageId { get; set; }

        public Branch Branch { get; set; }

        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }
    }
}
