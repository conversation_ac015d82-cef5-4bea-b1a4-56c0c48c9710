﻿using Gateway.Biometrics.Application.Country.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.Biometrics.Api.Models.Country
{
    public static class CountryResponseFactory
    {
        public static ObjectResult CountriesResponse(CountriesResult result)
        {
            switch (result.Status)
            {
                case GetCountriesStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.Countries
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case GetCountriesStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.INPUT_ERROR,
                            Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                            Message = result.Message,
                            ValidationMessages = result.ValidationMessages
                        })
                        { StatusCode = HttpStatusCodes.InvalidInput };
                case GetCountriesStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                            Message = result.Message,
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                case GetCountriesStatus.BranchNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.BRANCH_NOT_FOUND),
                            Message = result.Message,
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult CountryResponse(CountryResult result)
        {
            switch (result.Status)
            {
                case GetCountryStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.Country
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case GetCountryStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message,
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }
    }
}
