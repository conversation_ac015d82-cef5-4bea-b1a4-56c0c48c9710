﻿using Innovatrics.IFace;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Gateway.Biometrics.Application.Innovatrics.DTO
{
    public class ICAOVerification
    {
        public static Lazy<List<FaceAttributeId>> FaceAttributeIds = new Lazy<List<FaceAttributeId>>(() =>
        {
            var list = Enum.GetValues(typeof(FaceAttributeId)).Cast<FaceAttributeId>().ToList();

            list.Remove(FaceAttributeId.Age);
            list.Remove(FaceAttributeId.Gender);
            list.Remove(FaceAttributeId.LastItem);
            list.Remove(FaceAttributeId.SegmentationMask);
            list.Remove(FaceAttributeId.Crop);
            list.Remove(FaceAttributeId.Template);
            list.Remove(FaceAttributeId.FaceVerificationConfidence);
            list.Remove(FaceAttributeId.FaceRelativeArea);
            list.Remove(FaceAttributeId.FaceRelativeAreaInImage);
            list.Remove(FaceAttributeId.EyeDistance);

            return list;
        });

        public Dictionary<FaceAttributeId, ICAOResult> VerificationResult { get; set; }

        public ICAOVerification()
        {
            VerificationResult = new Dictionary<FaceAttributeId, ICAOResult>();
        }

        public void Clear()
        {
            VerificationResult?.Clear();
        }
    }
}
