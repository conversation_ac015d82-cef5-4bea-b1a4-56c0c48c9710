﻿using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.InventoryDefinition.DTO;
using System.Collections.Generic;

namespace Gateway.Biometrics.Application.Inventory.DTO
{
    public class InventoryDto
    {
        public int Id { get; set; }
        public int InventoryDefinitionId { get; set; }

        public string SerialNumber { get; set; }

        public string Description { get; set; }

        public int Status { get; set; }

        public virtual List<InventoryIpCameraDto> IpCameras { get; set; }
        public virtual InventoryDefinitionDto InventoryDefinition { get; set; }
        //public virtual CabinDto Cabin { get; set; }
    }
}
