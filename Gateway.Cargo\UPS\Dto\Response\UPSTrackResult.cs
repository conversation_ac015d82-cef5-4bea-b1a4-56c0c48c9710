﻿using Gateway.Core.Responses;

namespace Gateway.Cargo.UPS.Dto.Response
{
    public class UPSTrackingResult : BaseServiceResult<GetStatus>
    {
        public Response Response { get; set; }
        public TrackResponse TrackResponse { get; set; }
    }

    public class TrackResponse
    {
        public Response Response { get; set; }
        public Shipment[] Shipment { get; set; }
    }

    public class Shipment
    {
        public string InquiryNumber { get; set; }

        public List<Package> Package { get; set; }
    }

    public class Package
    {
        public string TrackingNumber { get; set; }
        public DeliveryDate[] DeliveryDate { get; set; }
        public DeliveryTime DeliveryTime { get; set; }

        public List<Activity> Activity { get; set; }
    }

    public class DeliveryDate
    {
        public string Type { get; set; }
        public string Date { get; set; }
    }

    public class DeliveryTime
    {
        public string Type { get; set; }
        public string EndDate { get; set; }
    }

    public class Activity
    {
        public Location Location { get; set; }
        public Status Status { get; set; }
        public string Date { get; set; }
        public string Time { get; set; }
    }

    public class Location
    {
        public Address Address { get; set; }
    }

    public class Address
    {
        public string City { get; set; }
        public string StateProvince { get; set; }
        public string PostalCode { get; set; }
        public string Country { get; set; }
    }

    public class Status
    {
        public string Type { get; set; }
        public string Description { get; set; }
        public string Code { get; set; }
        public string StatusCode { get; set; }
    }
}
