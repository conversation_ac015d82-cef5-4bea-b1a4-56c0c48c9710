﻿using Gateway.Biometrics.Application;
using Gateway.Biometrics.Application.Neurotec.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.Biometrics.Api.Models.Neurotec
{
    public static class NeurotecLicenseResponseFactory
    {
        public static ObjectResult GetNeurotecLicenseResponse(GetNeurotecLicenseResult result)
        {
            switch (result.Status)
            {
                case GetNeurotecLicenseStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.NeurotecLicense
                    })
                    { StatusCode = HttpStatusCodes.Created };
                case GetNeurotecLicenseStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case GetNeurotecLicenseStatus.NotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.NEUROTEC_LICENSE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.NEUROTEC_LICENSE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }
  
        public static ObjectResult UpdateNeurotecLicenseResponse(UpdateNeurotecLicenseResult result)
        {
            switch (result.Status)
            {
                case UpdateNeurotecLicenseStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_UPDATED),
                        Message = result.Message,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case UpdateNeurotecLicenseStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case UpdateNeurotecLicenseStatus.NeurotecLicenseNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.NEUROTEC_LICENSE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.NEUROTEC_LICENSE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateNeurotecLicenseStatus.HostNameNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.HOST_NAME_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.HOST_NAME_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateNeurotecLicenseStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }
   
        public static ObjectResult GetPaginatedNeurotecLicensesResponse(GetPaginatedNeurotecLicensesResult result)
        {
            switch (result.Status)
            {
                case GetPaginatedNeurotecLicensesStatus.Successful:
                    return new ObjectResult(new BasePaginationApiResponse()
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.NeurotecLicenses,
                        TotalNumberOfPages = result.TotalNumberOfPages,
                        TotalNumberOfRecords = result.TotalNumberOfRecords
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case GetPaginatedNeurotecLicensesStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case GetPaginatedNeurotecLicensesStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }
    }
}