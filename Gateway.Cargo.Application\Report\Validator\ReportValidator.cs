﻿using System.Linq;
using FluentValidation;
using Gateway.Cargo.Application.Report.Dto.Request;
using Gateway.Cargo.Resources;
using Gateway.Extensions;


namespace Gateway.Cargo.Application.Report.Validator
{
    internal class BaseReportValidatorByBranches : AbstractValidator<ReportRequestByBranches>
    {
        public BaseReportValidatorByBranches()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.BranchIds.Any())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.BranchIds)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.StartDate.IsDatetimeTrueFormat())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.StartDate)));
            });


            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.EndDate.IsDatetimeTrueFormat())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.EndDate)));
            });
        }
    }
}
