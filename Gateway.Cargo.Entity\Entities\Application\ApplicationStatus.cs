﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gateway.Cargo.Entity.Entities.Application
{
    public class ApplicationStatus
    {
        public ApplicationStatus()
        {
            IsActive = true;
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        public bool IsActive { get; set; }


        public ICollection<Application> Applications { get; set; }
        public ICollection<ApplicationStatusHistory> ApplicationStatusHistory { get; set; }
    }
}
