﻿using AutoMapper;
using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;
using Gateway.Biometrics.Api.Models.Inventory;
using Gateway.Biometrics.Application.Inventory;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Extensions;
using Gateway.Biometrics.Api.Models.Cabin;
using Gateway.Biometrics.Api.Models.InventoryStatusLog;
using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.InventoryStatusLog;
using Gateway.Biometrics.Application.InventoryStatusLog.DTO;

namespace Gateway.Biometrics.Api.Controllers
{
    //[Authorize]
    [Route("api")]
    [ApiController]
    public class InventoryStatusLogController : Controller
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private readonly IInventoryStatusLogService _inventoryStatusLogService;

        #region ctor

        public InventoryStatusLogController(IContext context, IMapper mapper, IInventoryStatusLogService inventoryStatusLogService)
        {
            _context = context;
            _mapper = mapper;
            _inventoryStatusLogService = inventoryStatusLogService;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Get Inventory Status Log by id
        /// </summary>
        /// <param name="resourceId"></param>
        [HttpGet]
        [Route("InventoryStatusLogs/{resourceId?}")]
        public async Task<IActionResult> GetInventoryStatusLog(int resourceId)
        {
            var serviceRequest = new GetInventoryStatusLogRequest()
            {
                Context = _context,
                ResourceId = resourceId
            };

            var result = await _inventoryStatusLogService.GetInventoryStatusLog(serviceRequest);

            return InventoryStatusLogResponseFactory.GetInventoryStatusLogResponse(result);
        }
        


        /// <summary>
        /// Get paginated list of inventory status logs
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Get paginated list of inventory status logs", 
            Description = "Get paginated list of inventory status logs")]
        [HttpPost]
        [Route("InventoryStatusLogs/search")]
        public async Task<IActionResult> GetPaginatedInventoryStatusLogs(GetPaginatedInventoryStatusLogsRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            // SignalR projesinde invettory status loglar kaydedilecek
            // Cabin filtresine göre inventoru statuslar listelenecek
            var serviceRequest = _mapper.Map<GetPaginatedInventoryStatusLogsRequest>(request);
            serviceRequest.Context = _context;

            var result = await _inventoryStatusLogService.GetPaginatedInventoryStatusLogs(serviceRequest);

            return InventoryStatusLogResponseFactory.GetPaginatedInventoryStatusLogsResponse(result);
        }

        /// <summary>
        /// Get paginated list of inventory status logs
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Get last inventory status logs for each inventories of cabin",
            Description = "Get last inventory status logs for each inventories of cabin")]
        [HttpGet]
        [Route("InventoryStatusLogs/lastInventoryStatusLogsForEachInventoriesOfCabin/{cabinId?}")]
        public async Task<IActionResult> GetLastInventoryStatusLogsForEachInventoriesOfCabin(int? cabinId)
        {
            if (cabinId == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));


            var serviceRequest = new GetLastInventoryStatusLogsForEachInventoriesOfCabinRequest()
            {
                CabinId = cabinId.Value
            };
            serviceRequest.Context = _context;

            var result = await _inventoryStatusLogService.GetLastInventoryStatusLogsForEachInventoriesOfCabin(serviceRequest);

            var response = InventoryStatusLogResponseFactory.GetLastInventoryStatusLogsForEachInventoriesOfCabinResponse(result);
            return response;
        }

        #endregion
    }
}