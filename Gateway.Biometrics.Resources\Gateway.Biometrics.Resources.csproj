﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <Compile Update="EnumResources.Designer.cs">
      <DependentUpon>EnumResources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Update="ServiceResources.Designer.cs">
      <DependentUpon>ServiceResources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="EnumResources.en-US.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="EnumResources.resx">
      <LastGenOutput>EnumResources.Designer.cs</LastGenOutput>
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="ServiceResources.resx">
      <LastGenOutput>ServiceResources.Designer.cs</LastGenOutput>
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="ServiceResources.tr-TR.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
  </ItemGroup>
</Project>