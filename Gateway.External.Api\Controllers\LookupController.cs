﻿using AutoMapper;
using Gateway.External.Api.Models.Lookup;
using Gateway.External.Application.Lookup;
using Gateway.External.Application.Lookup.Dto;
using Gateway.External.Core.Context;
using Gateway.External.Resources;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;

namespace Gateway.External.Api.Controllers
{
    [Authorize]
    [Route("api")]
    [ApiController]
    public class LookupController : Controller
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private readonly ILookupService _lookupService;

        #region ctor

        public LookupController(IContext context, IMapper mapper, ILookupService lookupService)
        {
            _context = context;
            _mapper = mapper;
            _lookupService = lookupService;
        }

        #endregion

        #region Public Methods

        /// <summary>
        ///  Retrieves a list of lookup values by the given parameter
        /// </summary>
        /// <param name="request"></param>      
        [SwaggerOperation(Summary = "Retrieves a list of lookup values by the given parameter", 
            Description = "Retrieves a list of lookup values by the given parameter")]
        [HttpPost]
        [Route("lookup")]
        public async Task<IActionResult> GetLookups(GetLookupRequestModel request)
        {
            if (request == null)
	            return LookupResponseFactory.GetLookupsResponse(new GetLookupResult()
	            {
                    Status = GetLookupStatus.BadRequest,
                    Message = ServiceResources.INVALID_REQUEST
	            });

			var serviceRequest = _mapper.Map<GetLookupRequest>(request);
            serviceRequest.Context = _context;

            var result = await _lookupService.GetLookupValue(serviceRequest);

            return LookupResponseFactory.GetLookupsResponse(result);
        }

        #endregion
    }
}
