﻿using AutoMapper;
using Gateway.Biometrics.Application.ClientConfiguration.DTO;
using Gateway.Biometrics.Application.InventoryAttribute.DTO;
using Gateway.Biometrics.Application.InventoryDefinition.DTO;
using Gateway.Biometrics.Application.Parameter.DTO;
using Gateway.Biometrics.Entity.Entities.ClientConfiguration;
using Gateway.Biometrics.Persistence;
using Gateway.Biometrics.Resources;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Parameter
{
    public class ParameterService : IParameterService
    {
        private readonly BiometricsDbContext _biometricsDbContext;
        private readonly ExternalDbContext _externalDbContext;
        private readonly IMapper _mapper;

        public ParameterService(
            BiometricsDbContext biometricsDbContext,
            ExternalDbContext externalDbContext, IMapper mapper)
        {

            _biometricsDbContext = biometricsDbContext;
            _externalDbContext = externalDbContext;
            _mapper = mapper;
        }

        public async Task<ApiResponse<OfficesDto>> GetOfficesAsync(OfficesRequestDto request)
        {
            try
            {
                var offices = new OfficesDto() { Offices = new List<OfficeSelectDto>() };

                var queryOffice = _biometricsDbContext.Office
                    .Where(p => !p.IsDeleted);

                if (request.CountryId.HasValue)
                    queryOffice = queryOffice.Where(p => p.CountryId == request.CountryId.Value);

                var officeList = await queryOffice
                    .OrderBy(o => o.Id)
                    .ToListAsync();

                foreach (var item in officeList)
                {
                    var newOffice = new OfficeSelectDto()
                    {
                        Id = item.Id,
                        Name = item.Name,
                    };

                    offices.Offices.Add(newOffice);
                }

                return new ApiResponse<OfficesDto>()
                {
                    Data = offices
                };

            }
            catch (Exception e)
            {
                return new ApiResponse<OfficesDto>()
                {
                    Message = e.Message
                };

            }

        }

        public async Task<ApiResponse<CabinsDto>> GetCabinsAsync(CabinsRequestDto request)
        {
            try
            {
                var Cabins = new CabinsDto() { Cabins = new List<CabinSelectDto>() };

                var queryCabin = _biometricsDbContext.Cabin
                    .Where(p => !p.IsDeleted);

                if (request.OfficeId.HasValue)
                    queryCabin = queryCabin.Where(p => p.OfficeId == request.OfficeId.Value);

                var cabinList = await queryCabin
                    .OrderBy(o => o.Id)
                    .ToListAsync();

                foreach (var item in cabinList)
                {
                    var newCabin = new CabinSelectDto()
                    {
                        Id = item.Id,
                        Name = item.Name,
                    };

                    Cabins.Cabins.Add(newCabin);
                }

                return new ApiResponse<CabinsDto>()
                {
                    Data = Cabins
                };

            }
            catch (Exception e)
            {
                return new ApiResponse<CabinsDto>()
                {
                    Message = e.Message
                };

            }

        }




        public async Task<ApiResponse<InventoryTypesDto>> GetInventoryTypesAsync(InventoryTypesRequestDto request)
        {
            try
            {
                var inventoryTypes = new InventoryTypesDto() { InventoryTypes = new List<InventoryTypeSelectDto>() };

                var queryInventoryType = _biometricsDbContext.InventoryType
                    .Where(p => !p.IsDeleted && p.Id > 0);

                var inventoryTypeList = await queryInventoryType
                    .OrderBy(o => o.Id)
                    .ToListAsync();

                foreach (var item in inventoryTypeList)
                {
                    var newInventoryType = new InventoryTypeSelectDto()
                    {
                        Id = item.Id,
                        Name = item.Name,
                    };

                    inventoryTypes.InventoryTypes.Add(newInventoryType);
                }

                return new ApiResponse<InventoryTypesDto>()
                {
                    Data = inventoryTypes
                };

            }
            catch (Exception e)
            {
                return new ApiResponse<InventoryTypesDto>()
                {
                    Message = e.Message
                };

            }

        }

        public async Task<ApiResponse<InventoryDefinitionsDto>> GetInventoryDefinitionsAsync(InventoryDefinitionsRequestDto request)
        {
            try
            {
                var InventoryDefinitions = new InventoryDefinitionsDto() { InventoryDefinitions = new List<InventoryDefinitionSelectDto>() };

                var queryInventoryDefinition = _biometricsDbContext.InventoryDefinition
                    .Where(p => !p.IsDeleted);

                var InventoryDefinitionList = await queryInventoryDefinition
                    .OrderBy(o => o.Id)
                    .ToListAsync();

                foreach (var item in InventoryDefinitionList)
                {
                    var newInventoryDefinition = new InventoryDefinitionSelectDto()
                    {
                        Id = item.Id,
                        InventoryDefinition = _mapper.Map<InventoryDefinitionDto>(item),
                    };

                    InventoryDefinitions.InventoryDefinitions.Add(newInventoryDefinition);
                }

                return new ApiResponse<InventoryDefinitionsDto>()
                {
                    Data = InventoryDefinitions
                };

            }
            catch (Exception e)
            {
                return new ApiResponse<InventoryDefinitionsDto>()
                {
                    Message = e.Message
                };

            }

        }


        public async Task<ApiResponse<InventoryValueSetsEmptyDto>> GetInventoryValueSetsEmptyAsync()
        {
            try
            {
                var inventoryValueSets = new InventoryValueSetsEmptyDto() { InventoryValueSets = new List<InventoryValueSetDto>() };

                var queryInventoryAttribute = _biometricsDbContext.InventoryAttribute
                    .Where(p => !p.IsDeleted);

                var inventoryAttributeList = await queryInventoryAttribute
                    .OrderBy(o => o.Id)
                    .ToListAsync();

                foreach (var inventoryAttribute in inventoryAttributeList)
                {
                    var inventoryValueSet = new InventoryValueSetDto()
                    {
                        InventoryAttributeId = inventoryAttribute.Id,
                        InventoryDefinitionId = 0,
                        InventoryAttribute = _mapper.Map<InventoryAttributeDto>(inventoryAttribute)
                    };

                    inventoryValueSets.InventoryValueSets.Add(inventoryValueSet);
                }

                return new ApiResponse<InventoryValueSetsEmptyDto>()
                {
                    Data = inventoryValueSets
                };

            }
            catch (Exception e)
            {
                return new ApiResponse<InventoryValueSetsEmptyDto>()
                {
                    Message = e.Message
                };

            }
        }

        public async Task<ApiResponse<ClientConfigurationInventoriesUnAssignedDto>> GetClientConfigurationInventoriesUnAssignedAsync()
        {
            try
            {
                var assignedInventoryIds = _biometricsDbContext.ClientConfigurationInventory
                    .Where(n => !n.IsDeleted).Select(n => n.InventoryId);

                var unAssignedInventories = _biometricsDbContext.Inventory.Where(n => !assignedInventoryIds.Contains(n.Id));
                var unAssignedClientConfigurationInventories = await unAssignedInventories
                    .Select(n => new ClientConfigurationInventory()
                    {
                        Inventory = n,
                        InventoryId = n.Id
                    }).ToListAsync();

                var clientConfigurationInventories = new ClientConfigurationInventoriesUnAssignedDto()
                {
                    ClientConfigurationInventories = _mapper.Map<List<ClientConfigurationInventoryDto>>(unAssignedClientConfigurationInventories)
                };

                return new ApiResponse<ClientConfigurationInventoriesUnAssignedDto>()
                {
                    Data = clientConfigurationInventories
                };

            }
            catch (Exception e)
            {
                return new ApiResponse<ClientConfigurationInventoriesUnAssignedDto>()
                {
                    Message = e.Message
                };

            }
        }

        public async Task<ApiResponse<CabinInventoriesUnAssignedDto>> GetCabinInventoriesUnAssignedAsync()
        {
            try
            {

                //var unAssignedInventories = _biometricsDbContext.Inventory.Where(n => !n.IsDeleted && n.CabinId == 0);
                //var unAssignedCabinInventories = await unAssignedInventories
                //    .Select(n => new Inventory.DTO.InventoryDto()
                //    {                        
                //        Id = n.Id,
                //        InventoryDefinitionId = n.InventoryDefinitionId,
                //        Description = n.Description,
                //        InventoryDefinition = _mapper.Map<InventoryDefinitionDto>(n.InventoryDefinition)
                //    }).ToListAsync();

                var unAssignedInventories = await _biometricsDbContext.Inventory.Where(n => !n.IsDeleted && n.CabinId == 0).ToListAsync();

                var cabinInventories = new CabinInventoriesUnAssignedDto()
                {
                    CabinInventories = _mapper.Map<List<Inventory.DTO.InventoryDto>>(unAssignedInventories)
                };

                return new ApiResponse<CabinInventoriesUnAssignedDto>()
                {
                    Data = cabinInventories
                };

            }
            catch (Exception e)
            {
                return new ApiResponse<CabinInventoriesUnAssignedDto>()
                {
                    Message = e.Message
                };

            }
        }

       public async Task<ApiResponse<NeurotecLicensesDto>> GetNeurotecLicensesSelectAsync(NeurotecLicensesRequestDto request)
        {
            try
            {

                //Entity.Entities.ClientConfiguration.ClientConfiguration clientConfiguration = null;
                string hostName = null;
                if (request.ClientConfigurationId.HasValue && request.ClientConfigurationId.Value > 0)
                {
                    var clientConfiguration = _biometricsDbContext.ClientConfiguration.FirstOrDefault(n => n.Id == request.ClientConfigurationId.Value);

                    if (clientConfiguration == null)
                    {
                        return new ApiResponse<NeurotecLicensesDto>()
                        {
                            Message = ServiceResources.CLIENT_CONFIGURATION_NOT_FOUND
                        };                        
                    }
                    else
                    {
                        hostName = clientConfiguration.HostName;
                    }
                }


                var neurotecLicensesDb = await _biometricsDbContext.NeurotecLicense.Where(n => !n.IsDeleted && n.LicenseTypeId == 1 &&
                (n.HostName == null || n.HostName == "" || n.HostName == hostName)).ToListAsync();

                var neurotecLicenses = new NeurotecLicensesDto()
                {
                    Licenses = _mapper.Map<List<NeurotecLicenseSelectDto>>(neurotecLicensesDb)
                };

                return new ApiResponse<NeurotecLicensesDto>()
                {
                    Data = neurotecLicenses
                };

            }
            catch (Exception e)
            {
                return new ApiResponse<NeurotecLicensesDto>()
                {
                    Message = e.Message
                };

            }
        }


    }
}
