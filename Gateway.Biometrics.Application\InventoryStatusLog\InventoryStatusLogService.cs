﻿using AutoMapper;
using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Application.InventoryStatusLog.DTO;
using Gateway.Biometrics.Application.InventoryStatusLog.Validator;
using Gateway.Biometrics.Application.Lookup;
using Gateway.Biometrics.Entity.Enum;
using Gateway.Biometrics.Persistence;
using Gateway.Biometrics.Resources;
using Gateway.Core.Pagination;
using Gateway.Extensions;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace Gateway.Biometrics.Application.InventoryStatusLog
{
    public class InventoryStatusLogService : IInventoryStatusLogService
    {
        private readonly BiometricsDbContext _dbContext;
        private readonly IValidationService _validationService;
        private IMapper _mapper;

        public InventoryStatusLogService(IValidationService validationService, BiometricsDbContext dbContext, IMapper mapper)
        {
            _validationService = validationService;
            _dbContext = dbContext;
            _mapper = mapper;
        }

        public async Task<GetInventoryStatusLogResult> GetInventoryStatusLog(GetInventoryStatusLogRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetInventoryStatusLogValidator), request);

            if (!validationResult.IsValid)
                return new GetInventoryStatusLogResult
                {
                    Status = GetInventoryStatusLogStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var inventoryStatusLogExisting = await _dbContext.InventoryStatusLog.Where(p => p.Id == request.ResourceId && !p.IsDeleted)
                .FirstOrDefaultAsync();

            if (inventoryStatusLogExisting == null)
                return new GetInventoryStatusLogResult
                {
                    Status = GetInventoryStatusLogStatus.NotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };


            var office = await _dbContext.Office
                .Where(n =>
                    n.Id == inventoryStatusLogExisting.OfficeId && !n.IsDeleted)
                .FirstOrDefaultAsync();


            if (office == null)
                return new GetInventoryStatusLogResult
                {
                    Status = GetInventoryStatusLogStatus.OfficeNotFound,
                    Message = ServiceResources.OFFICE_NOT_FOUND
                };

            var inventoryStatusLogGet = _mapper.Map<InventoryStatusLogDto>(inventoryStatusLogExisting);

            return new GetInventoryStatusLogResult
            {
                Status = GetInventoryStatusLogStatus.Successful,
                Message = ServiceResources.RESOURCE_FOUND,
                InventoryStatusLog = inventoryStatusLogGet
            };
        }

        public async Task<CreateInventoryStatusLogResult> CreateInventoryStatusLog(CreateInventoryStatusLogRequest request)
        {
            var validationResult = _validationService.Validate(typeof(CreateInventoryStatusLogValidator), request);

            if (!validationResult.IsValid)
                return new CreateInventoryStatusLogResult
                {
                    Status = CreateInventoryStatusLogStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };


            var inventory = _dbContext.Inventory.FirstOrDefault(t => t.Id == request.InventoryId);

            if (inventory == null)
            {
                return new CreateInventoryStatusLogResult
                {
                    Status = CreateInventoryStatusLogStatus.InventoryNotFound,
                    Message = ServiceResources.INVENTORY_NOT_FOUND
                };
            }

            var newInventoryStatusLog = new Entity.Entities.Inventory.InventoryStatusLog()
            {
                HostName = request.HostName,
                InventoryId = request.InventoryId,
                CabinId = request.CabinId,
                OfficeId = request.OfficeId,
                CountryId = request.CountryId,
                ProvinceId = request.ProvinceId,
                ErrorMessage = request.ErrorMessage,
                AppFileVersion = request.AppFileVersion,
                Status = request.Status,
            };

            inventory.Status = (int)request.Status;
            inventory.UpdatedAt = DateTime.UtcNow;
            _dbContext.Inventory.Update(inventory);

            await _dbContext.InventoryStatusLog.AddAsync(newInventoryStatusLog);

            await _dbContext.SaveChangesAsync();

            return new CreateInventoryStatusLogResult
            {
                Status = CreateInventoryStatusLogStatus.Successful,
                Message = ServiceResources.RESOURCE_CREATED,
                Id = newInventoryStatusLog.Id
            };
        }



        public async Task<GetPaginatedInventoryStatusLogsResult> GetPaginatedInventoryStatusLogs(GetPaginatedInventoryStatusLogsRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetPaginatedInventoryStatusLogsValidator), request);

            if (!validationResult.IsValid)
                return new GetPaginatedInventoryStatusLogsResult
                {
                    Status = GetPaginatedInventoryStatusLogsStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var queryInventoryStatusLogs = _dbContext.InventoryStatusLog
                .Where(p => !p.IsDeleted && p.InventoryId == request.FilterInventoryId.Value);


            var paginationResult = PagedResultsFactory.CreatePagedResult(
                queryInventoryStatusLogs, request.Pagination.PageNumber, request.Pagination.PageSize,
                "Id", false);
  

            return paginationResult == null
                ? new GetPaginatedInventoryStatusLogsResult
                {
                    InventoryStatusLogs = null,
                    Status = GetPaginatedInventoryStatusLogsStatus.ResourceNotFound,
                    Message = ServiceResources.INVALID_INPUT_ERROR
                }
                : new GetPaginatedInventoryStatusLogsResult
                {
                    InventoryStatusLogs = paginationResult.Results.Select(p => _mapper.Map<InventoryStatusLogDto>(p)).ToList(),
                    TotalNumberOfPages = paginationResult.TotalNumberOfPages,
                    TotalNumberOfRecords = paginationResult.TotalNumberOfRecords,
                    Status = GetPaginatedInventoryStatusLogsStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED
                };
        }

        public async Task<GetLastInventoryStatusLogsForEachInventoriesOfCabinResult> GetLastInventoryStatusLogsForEachInventoriesOfCabin(GetLastInventoryStatusLogsForEachInventoriesOfCabinRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetLastInventoryStatusLogsForEachInventoriesOfCabinValidator), request);

            if (!validationResult.IsValid)
                return new GetLastInventoryStatusLogsForEachInventoriesOfCabinResult
                {
                    Status = GetLastInventoryStatusLogsForEachInventoriesOfCabinStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var cabin = await _dbContext.Cabin.FirstOrDefaultAsync(n => !n.IsDeleted && n.Id == request.CabinId);

            if (cabin == null)
            {
                return new GetLastInventoryStatusLogsForEachInventoriesOfCabinResult()
                {
                    Status = GetLastInventoryStatusLogsForEachInventoriesOfCabinStatus.NotFound,
                    Message = ServiceResources.CABIN_NOT_FOUND
                };
            }

            var result = new LastInventoryStatusLogsOfCabin();
            result.Cabin = _mapper.Map<CabinDto>(cabin);

            foreach (var inventory in cabin.Inventories)
            {
                var lastLog = _dbContext.InventoryStatusLog.Where(n => !n.IsDeleted && n.InventoryId == inventory.Id)
                    .OrderByDescending(n => n.Id).FirstOrDefault();

                //result.LastLogsOfEachInventories.Add(new InventoryStatusLogDto()
                //{
                //    Inventory = _mapper.Map<InventoryDto>(inventory),
                //    InventoryStatusLog =  lastLog== null ? null : _mapper.Map<InventoryStatusLogDto>(lastLog),
                //})

                if (lastLog != null)
                {
                    result.LastLogsOfEachInventories.Add(_mapper.Map<InventoryStatusLogDto>(lastLog));
                }
            }

            return new GetLastInventoryStatusLogsForEachInventoriesOfCabinResult()
            {
                LastInventoryStatusLogsOfCabin = result,
                Status = GetLastInventoryStatusLogsForEachInventoriesOfCabinStatus.Successful,
                Message = ServiceResources.RESOURCE_RETRIEVED
            };
        }



        private static LookupValue GetLookupValue(int enumType, int? value)
        {
            return EnumExtensions.GetEnumAsDictionary(LookupTypeFactory.GetInstance(enumType))
                .Select(x => new LookupValue { Id = x.Key.ToString(), DisplayValue = x.Value })
                .FirstOrDefault(p => p.Id == value.ToString());
        }
    }
}