﻿using FluentValidation;
using Gateway.Biometrics.Application.Branch.DTO;
using Gateway.Biometrics.Application.Country.DTO;
using Gateway.Biometrics.Resources;

namespace Gateway.Biometrics.Application.Branch.Validator
{
    public static class BranchValidator
    {
        internal class GetBranchesValidator : AbstractValidator<GetBranchesRequest>
        {
            public GetBranchesValidator()
            {
                RuleFor(p => p).Custom((item, context) =>
                {
                   
                });
            }
        }
    }
}
