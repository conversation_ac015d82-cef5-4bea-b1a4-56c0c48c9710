﻿using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.ClientConfiguration.DTO
{
    public class UpdateClientConfigurationResult : BaseServiceResult<UpdateClientConfigurationStatus>
    {
        public int Id { get; set; }
    }

    public enum UpdateClientConfigurationStatus
    {
        Successful,
        ResourceExists,
        InvalidInput,
        ClientConfigurationNotFound,
        OfficeNotFound,
        CabinNotFound,
        CountryNotFound,
        HostNameAllreadyRegistered,
        NeurotecLicenseNotFound,
    }
}
