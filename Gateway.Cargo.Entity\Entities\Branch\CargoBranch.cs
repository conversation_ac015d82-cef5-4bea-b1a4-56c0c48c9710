﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gateway.Cargo.Entity.Entities.Branch
{
    public class CargoBranch
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public int BranchId { get; set; }

        [Required]
        public byte CargoProviderId { get; set; }

        public string UserName { get; set; }

        public string Password { get; set; }

        public string ApiKey { get; set; }

        public string ShipperNumber { get; set; }

        public string BaseAddress { get; set; }

        public string BasePublicAddress { get; set; }

		public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }

        public Branch Branch { get; set; }
    }
}
