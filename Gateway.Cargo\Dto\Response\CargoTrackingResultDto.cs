﻿using Gateway.Cargo.Resources;
using Gateway.Core.CustomAttributes;
using Gateway.Core.Responses;
using Gateway.Core.Responses.Models;

namespace Gateway.Cargo.Dto.Response
{
    public class CargoTrackingResultDto : BaseServiceResult<GetTrackingStatus>
    {
        public CargoTrackingResult CargoTrackingResult { get; set; }
    }

    public class CargoTrackingResult
    {
        public string InquiryNumber { get; set; }
        public string TrackingNumber { get; set; }

        public TrackingStatus LastStatus { get; set; }

        public List<TrackingStatus> Status { get; set; }
    }

    public class TrackingStatus
    {
        public string Date { get; set; }
        public string Time { get; set; }
        public string Description { get; set; }
        //public string Type { get; set; }
        //public string Code { get; set; }
        public byte? StatusCode { get; set; }
    }

    public enum GetTrackingStatus
    {
        [CustomHttpStatus(Code = "SUCCESS", Resources = typeof(ServiceResources), Status = "SUCCESS", StatusCode = HttpStatusCodes.Ok)]
        Successful,

        [CustomHttpStatus(Code = "INVALID_INPUT_ERROR", Resources = typeof(ServiceResources), Status = "INVALID_INPUT_ERROR", StatusCode = HttpStatusCodes.InvalidInput)]
        InvalidInput,

        [CustomHttpStatus(Code = "BAD_REQUEST", Resources = typeof(ServiceResources), Status = "BAD_REQUEST", StatusCode = HttpStatusCodes.BadRequest)]
        BadRequest,

        [CustomHttpStatus(Code = "RESOURCE_NOT_FOUND", Resources = typeof(ServiceResources), Status = "RESOURCE_NOT_FOUND", StatusCode = HttpStatusCodes.ResourceNotFound)]
        NotFound,

        [CustomHttpStatus(Code = "INTERNAL_SERVICE_ERROR", Resources = typeof(ServiceResources), Status = "INTERNAL_SERVICE_ERROR", StatusCode = HttpStatusCodes.InternalServerError)]
        InternalServerError,

        [CustomHttpStatus(Code = "EXTERNAL_SERVICE_ERROR", Resources = typeof(ServiceResources), Status = "EXTERNAL_SERVICE_ERROR", StatusCode = HttpStatusCodes.ServiceUnavailable)]
        ExternalServiceError
    }
}
