﻿using Gateway.Biometrics.Application.DemographicInformation.DTO;
using Gateway.Biometrics.Application.Supervisor.DTO;
using Gateway.Biometrics.Application.User.DTO;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.User
{
    public interface IUserService
    {
        Task<PortalUserLoginResult> PortalUserLogin(PortalUserLoginRequest request);
        Task<PortalUserLoginResult> LdapUserLogin(PortalUserLoginRequest request);
        
    }
}
