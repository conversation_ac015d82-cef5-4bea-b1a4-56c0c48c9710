<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <EnvironmentName>__ASPNETCORE_ENVIRONMENT__</EnvironmentName>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.4" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <ProjectReference Include="..\Gateway.Logger.Core\Gateway.Logger.Core.csproj" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="8.1.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="8.1.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="8.1.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Gateway.Cargo.Application\Gateway.Cargo.Application.csproj" />
    <ProjectReference Include="..\Gateway.Cargo.Resources\Gateway.Cargo.Resources.csproj" />
    <ProjectReference Include="..\Gateway.Db.Repository\Gateway.Db.Repository.csproj" />
    <ProjectReference Include="..\Gateway.Redis\Gateway.Redis.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Content Update="web.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
</Project>