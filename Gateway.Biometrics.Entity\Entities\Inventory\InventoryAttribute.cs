﻿using Gateway.Biometrics.Entity.Entities.Category;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace Gateway.Biometrics.Entity.Entities.Inventory
{
    public class InventoryAttribute : BaseEntity
    {
        public string Name { get; set; }
        public string FieldType { get; set; }
        public string DataType { get; set; }
        public string DefaultValue { get; set; }
        public int MaxLength { get; set; }
        public int SortOrder { get; set; }
        public int Status { get; set; }
        public bool IsRequired { get; set; }
        public bool IsSingleValue { get; set; }

    }
}
