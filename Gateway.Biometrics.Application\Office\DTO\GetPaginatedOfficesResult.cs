﻿using System;
using System.Collections.Generic;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.Office.DTO
{
    public class GetPaginatedOfficesResult : BasePaginationServiceListResult<GetPaginatedOfficesStatus>
    {
        public IEnumerable<OfficeDto> Offices { get; set; }
    }
    public enum GetPaginatedOfficesStatus
    {
        Successful,
        InvalidInput,
        ResourceNotFound
    }
}
