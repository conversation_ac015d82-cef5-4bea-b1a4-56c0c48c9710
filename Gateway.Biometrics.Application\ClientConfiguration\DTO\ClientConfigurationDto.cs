﻿using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Application.Plugin.DTO;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Castle.Components.DictionaryAdapter;
using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.Office.DTO;

namespace Gateway.Biometrics.Application.ClientConfiguration.DTO
{
    public class ClientConfigurationDto
    {
        public ClientConfigurationDto()
        {
            
        }

        public int Id { get; set; }
        public string HostName { get; set; }
        public string Description { get; set; }
        public int CabinId { get; set; }
        public int OfficeId { get; set; }
        public int Status { get; set; }
        public int CountryId { get; set; }
        public int LicenseId { get; set; }
        public string NeurotecLicenseNumber { get; set; }

        public virtual OfficeDto Office { get; set; }
        public virtual CabinDto Cabin { get; set; }

        public virtual List<ClientConfigurationInventoryDto> ClientConfigurationInventories { get; set; } =
            new List<ClientConfigurationInventoryDto>();

        public virtual List<ClientConfigurationInventoryDto> UnAssignedClientConfigurationInventories { get; set; } =
            new List<ClientConfigurationInventoryDto>();
    }
}
