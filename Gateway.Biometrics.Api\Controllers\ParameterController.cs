﻿using System.Threading.Tasks;
using AutoMapper;
using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Application;
using Gateway.Biometrics.Application.Office;
using Gateway.Biometrics.Application.Parameter;
using Gateway.Biometrics.Application.Parameter.DTO;
using Gateway.Biometrics.Core.Context;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Swashbuckle.AspNetCore.Annotations;

namespace Gateway.Biometrics.Api.Controllers
{
    //[Authorize]
    [Route("api")]
    [ApiController]
    public class ParameterController : Controller
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private readonly IParameterService _parameterService;


        public ParameterController(IContext context, IMapper mapper, IParameterService parameterService
        )
        {
            _context = context;
            _mapper = mapper;
            _parameterService = parameterService;
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of active Offices",
            Description = "Get collection of Offices by using filtering parameters such as country options")]
        [HttpPost]
        [Route("Parameters/GetOfficesSelectList")]
        public Task<ApiResponse<OfficesDto>> GetOffices([FromBody] OfficesRequestDto request)
        {
            return _parameterService.GetOfficesAsync(request);
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of active Cabins",
            Description = "Get collection of Cabins by using filtering parameters such as country options")]
        [HttpPost]
        [Route("Parameters/GetCabinsSelectList")]
        public Task<ApiResponse<CabinsDto>> GetCabins([FromBody] CabinsRequestDto request)
        {
            return _parameterService.GetCabinsAsync(request);
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of active Inventory Type",
            Description = "Get collection of Inventory Types by using filtering parameters such as country options")]
        [HttpPost]
        [Route("Parameters/GetInventoryTypesSelectList")]
        public Task<ApiResponse<InventoryTypesDto>> GetInventoryTypes([FromBody] InventoryTypesRequestDto request)
        {
            return _parameterService.GetInventoryTypesAsync(request);
        }


        [SwaggerOperation(
            Summary = "Provides clients to get collection of active Inventory Definition",
            Description = "Get collection of Inventory Definitions by using filtering parameters such as country options")]
        [HttpPost]
        [Route("Parameters/GetInventoryDefinitionsSelectList")]
        public Task<ApiResponse<InventoryDefinitionsDto>> GetInventoryDefinitions([FromBody] InventoryDefinitionsRequestDto request)
        {
            return _parameterService.GetInventoryDefinitionsAsync(request);
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of empty Inventory Value Sets",
            Description = "Get collection of Inventory Value Sets")]
        [HttpGet]
        [Route("Parameters/GetInventoryValueSetsEmpty")]
        public Task<ApiResponse<InventoryValueSetsEmptyDto>> GetInventoryValueSetsEmpty()
        {
            return _parameterService.GetInventoryValueSetsEmptyAsync();
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of unassigned ClientConfigurationInventories",
            Description = "Get collection of ClientConfigurationInventories")]
        [HttpGet]
        [Route("Parameters/GetClientConfigurationInventoriesUnAssigned")]
        public Task<ApiResponse<ClientConfigurationInventoriesUnAssignedDto>> GetClientConfigurationInventoriesUnAssigned()
        {
            return _parameterService.GetClientConfigurationInventoriesUnAssignedAsync();
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of unassigned CabinInventories",
            Description = "Get collection of CabinInventories")]
        [HttpGet]
        [Route("Parameters/GetCabinInventoriesUnAssigned")]
        public Task<ApiResponse<CabinInventoriesUnAssignedDto>> GetCabinInventoriesUnAssigned()
        {
            return _parameterService.GetCabinInventoriesUnAssignedAsync();
        }

        [SwaggerOperation(
    Summary = "Provides Neurotechnology License numbers list can use by ClientConfiguration",
    Description = "Provides Neurotechnology License numbers list can use by ClientConfiguration")]
        [HttpPost]
        [Route("Parameters/GetNeurotecLicensesSelectList")]
        public Task<ApiResponse<NeurotecLicensesDto>> GetNeurotecLicensesSelectList([FromBody] NeurotecLicensesRequestDto request)
        {
            //return null;
            return _parameterService.GetNeurotecLicensesSelectAsync(request);
        }

    }
}
