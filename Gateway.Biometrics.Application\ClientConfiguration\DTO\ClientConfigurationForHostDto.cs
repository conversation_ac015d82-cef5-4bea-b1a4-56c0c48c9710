﻿using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Application.Plugin.DTO;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Gateway.Biometrics.Application.ClientConfiguration.DTO
{
    public class ClientConfigurationForHostDto
    {
        public ClientConfigurationForHostDto()
        {
            Inventories = new List<InventoryDto>();
            Cabin = new CabinDto();
            Plugins = new List<PluginDto>();
            ClientConfigurationInventories = new List<ClientConfigurationInventoryDto>();
        }

        public int Id { get; set; }
        public string HostName { get; set; }
        public string Description { get; set; }
        public int CabinId { get; set; }

        public int OfficeId { get; set; }
        public int Status { get; set; }

        public int CountryId { get; set; }
        public string DeviceName { get; set; }
        public int LicenseId { get; set; }

        public DateTime ConfigurationDate { get; set; }
         
        public List<PluginDto> Plugins { get; set; }

        /// <summary>
        /// Eskimiştir. Bunun yerine Cabin.Inventories kullanılacaktır
        /// </summary>
        public List<InventoryDto> Inventories { get; set; }
        public CabinDto Cabin { get; set; }

        [JsonIgnore]
        public List<ClientConfigurationInventoryDto> ClientConfigurationInventories { get; set; }
    }
}
