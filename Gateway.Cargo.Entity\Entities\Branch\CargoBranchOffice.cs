﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Gateway.Cargo.Entity.Entities.Branch
{
    public class CargoBranchOffice
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public int BranchId { get; set; }

        [Required]
        public string OfficeId { get; set; }

        [Required]
        public string ClientId { get; set; }

        public string TitleEn { get; set; }
        public string TitleAr { get; set; }
        public string TitleKu { get; set; }
        public string Phone { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }

        public Branch Branch { get; set; }
    }
}
