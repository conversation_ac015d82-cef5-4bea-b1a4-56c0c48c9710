﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="APPEAL_METADATA_NOT_FOUND" xml:space="preserve">
    <value>Appeal Meta data not found</value>
  </data>
  <data name="APPEAL_NOT_FOUND" xml:space="preserve">
    <value>Başvuru bulunamadı</value>
  </data>
  <data name="BRANCHES_DO_NOT_MATCH" xml:space="preserve">
    <value>Portal ve Biyometri Branch idleri uyumsuz</value>
  </data>
  <data name="BRANCH_NOT_FOUND" xml:space="preserve">
    <value>Şube bulunamadı</value>
  </data>
  <data name="CABIN_NOT_FOUND" xml:space="preserve">
    <value>Kabin bulunamadı</value>
  </data>
  <data name="Category_NOT_FOUND" xml:space="preserve">
    <value>Kategori bulunamadı</value>
  </data>
  <data name="CITY_NOT_FOUND" xml:space="preserve">
    <value>Şehir bulunamadı</value>
  </data>
  <data name="CLIENT_CONFIGURATION_NOT_FOUND" xml:space="preserve">
    <value>İstemci konfigürasyonu bulunamadı</value>
  </data>
  <data name="COUNTRY_NOT_FOUND" xml:space="preserve">
    <value>Ülke bulunamadı</value>
  </data>
  <data name="CREATE_FACE_ERROR" xml:space="preserve">
    <value>Create Face hatası</value>
  </data>
  <data name="FAILED" xml:space="preserve">
    <value>FAİLED</value>
  </data>
  <data name="HOST_NAME_NOT_FOUND" xml:space="preserve">
    <value>Hostname bulunamadı</value>
  </data>
  <data name="INPUT_ERROR" xml:space="preserve">
    <value>INPUT_ERROR</value>
  </data>
  <data name="INTERNAL_SERVER_ERROR" xml:space="preserve">
    <value>INTERNAL_SERVER_ERROR</value>
  </data>
  <data name="INVALID_INPUT_ERROR" xml:space="preserve">
    <value>Geçersiz istek parametresi</value>
  </data>
  <data name="INVALID_REQUEST" xml:space="preserve">
    <value>Geçersiz istek</value>
  </data>
  <data name="INVENTORY_ATTRIBUTE_NOT_FOUND" xml:space="preserve">
    <value>Envanter öğesi bulunamadı</value>
  </data>
  <data name="INVENTORY_DEFINITION_NOT_FOUND" xml:space="preserve">
    <value>Envanter tanımı bulunamadı</value>
  </data>
  <data name="INVENTORY_NOT_FOUND" xml:space="preserve">
    <value>Envanter bulunamadı</value>
  </data>
  <data name="INVENTORY_STATUS_LOG_NOT_FOUND" xml:space="preserve">
    <value>Envanter durum logu bulunamadı</value>
  </data>
  <data name="INVENTORY_TYPE_NOT_FOUND" xml:space="preserve">
    <value>Envanter tipi bulunamadı</value>
  </data>
  <data name="LDAP_USER_ACCOUNT_EXPIRED" xml:space="preserve">
    <value>Activ directory hesabı expire olmuş</value>
  </data>
  <data name="LDAP_USER_ACCOUNT_LOCKED" xml:space="preserve">
    <value>Activ directory hesabı kilitli</value>
  </data>
  <data name="LDAP_USER_ACCOUNT_NOT_FOUND" xml:space="preserve">
    <value>Activ directory hesabı bulunamadı</value>
  </data>
  <data name="LDAP_USER_INVALID_CREDENTIALS" xml:space="preserve">
    <value>Active directory kullanıcısı kullanıcı adı ve şifre hatalı</value>
  </data>
  <data name="METHOD_REQUIREMENT_ERROR" xml:space="preserve">
    <value>Bu yöntem, bu parametre kombinasyonları için geçerli değildir</value>
  </data>
  <data name="NEUROTEC_LICENSE_NOT_FOUND" xml:space="preserve">
    <value>Neurotec lisansı bulunamadı</value>
  </data>
  <data name="OFFICE_NOT_FOUND" xml:space="preserve">
    <value>Ofis bulunamadı</value>
  </data>
  <data name="PROPERTY_MAX_LENGTH_ERROR" xml:space="preserve">
    <value>{0} özelliği {1} karakterden fazla olamaz</value>
  </data>
  <data name="PROPERTY_REQUIRED" xml:space="preserve">
    <value>{0} gerekli</value>
  </data>
  <data name="RESOURCE_ALREADY_REGISTERED" xml:space="preserve">
    <value>Önceden kaydedilmiş</value>
  </data>
  <data name="RESOURCE_CREATED" xml:space="preserve">
    <value>Kayıt oluşturuldu</value>
  </data>
  <data name="RESOURCE_DELETED" xml:space="preserve">
    <value>Kayıt silindi</value>
  </data>
  <data name="RESOURCE_FOUND" xml:space="preserve">
    <value>Kayıt bulundu</value>
  </data>
  <data name="RESOURCE_NOT_FOUND" xml:space="preserve">
    <value>Kayıt bulunamadı</value>
  </data>
  <data name="RESOURCE_RETRIEVED" xml:space="preserve">
    <value>Kayıt getirildi</value>
  </data>
  <data name="RESOURCE_UPDATED" xml:space="preserve">
    <value>Kayıt güncellendi</value>
  </data>
  <data name="SUCCESS" xml:space="preserve">
    <value>SUCCESS</value>
  </data>
  <data name="USER_HAVE_NOT_BRANCH" xml:space="preserve">
    <value>Kullanıcıya şube tanımlanmamış</value>
  </data>
  <data name="USER_NOT_AUTHORIZED" xml:space="preserve">
    <value>Kullanıcı yetkili değil</value>
  </data>
  <data name="USER_NOT_FOUND" xml:space="preserve">
    <value>Kullanıcı bulunamadı</value>
  </data>
</root>