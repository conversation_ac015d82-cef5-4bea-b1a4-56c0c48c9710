﻿using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Application.ClientConfiguration.DTO;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Application.InventoryDefinition.DTO;

namespace Gateway.Biometrics.Application.Parameter.DTO
{
    public class CabinInventoriesUnAssignedDto
    {
        public IList<InventoryDto> CabinInventories { get; set; }
    }
}
