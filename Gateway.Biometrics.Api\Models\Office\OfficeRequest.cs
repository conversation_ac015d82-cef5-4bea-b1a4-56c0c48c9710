﻿using System.Collections.Generic;
using System;
using Gateway.Core.Pagination;
using System.ComponentModel.DataAnnotations;
using Gateway.Biometrics.Application;

namespace Gateway.Biometrics.Api.Models.Office
{
    public class BaseOfficeRequestModel
    {
        public string Name { get; set; }
        public string OfficeCode { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public int Status { get; set; }
        public int CountryId { get; set; }
        public int BranchId { get; set; }

        public BaseOfficeRequestModel()
        {
        }
    }

    public class GetOfficeRequestModel : BaseServiceRequest
    {
        [Required]
        public int ResourceId { get; set; }
    }

    public class CreateOfficeRequestModel : BaseOfficeRequestModel
    {
    }

    public class UpdateOfficeRequestModel : BaseOfficeRequestModel
    {
    }

    public class DeleteOfficeRequestModel
    {
        public int OfficeId { get; set; }
    }
    
    public class GetPaginatedOfficesRequestModel
    {
        public int? FilterCountryId { get; set; }
        public PaginationRequest Pagination { get; set; }
    }
}