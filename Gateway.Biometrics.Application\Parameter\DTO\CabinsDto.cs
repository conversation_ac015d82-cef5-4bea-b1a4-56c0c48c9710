﻿using System.Collections.Generic;
using Gateway.Biometrics.Application.Cabin.DTO;

namespace Gateway.Biometrics.Application.Parameter.DTO
{
    public class CabinsDto
    {
        public IList<CabinSelectDto> Cabins { get; set; }
    }
    public class CabinSelectDto
    {
        public int Id { get; set; }
        public string Name { get; set; }


    }

    public class CabinsRequestDto
    {
        public int? OfficeId { get; set; }
    }

    public class CabinsResult : BaseServiceDataResult<CabinsDto>
    {
        public int Id { get; set; }
    }
}
