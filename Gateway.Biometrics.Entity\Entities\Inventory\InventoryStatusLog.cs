﻿using Gateway.Biometrics.Entity.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace Gateway.Biometrics.Entity.Entities.Inventory
{
    public class InventoryStatusLog : BaseEntity
    {
        public string HostName { get; set; }
        public int InventoryId { get; set; }
        public int CabinId { get; set; }
        public int OfficeId { get; set; }
        public int CountryId { get; set; }
        public int ProvinceId { get; set; }
        public string ErrorMessage { get; set; }
        public string AppFileVersion { get; set; }
        public InventoryStatusEnum Status { get; set; }
        public virtual Inventory Inventory { get; set; }

    }
}
