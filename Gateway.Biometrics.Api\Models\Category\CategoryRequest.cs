﻿using Gateway.Core.Pagination;

namespace Gateway.Biometrics.Api.Models.Category
{
    public class BaseCategoryRequestModel
    {
        public int? ParentId { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public BaseCategoryRequestModel()
        {
        }
    }
    
    public class CreateCategoryRequestModel : BaseCategoryRequestModel
    {
    }

    public class UpdateCategoryRequestModel : BaseCategoryRequestModel
    {
    }

    public class DeleteCategoryRequestModel
    {
        public int CategoryId { get; set; }
    }

    public class DeleteCategoryApplicantRequestModel
    {
        public int CategoryId { get; set; }
    }

    public class GetPaginatedCategoriesRequestModel
    {
        public PaginationRequest Pagination { get; set; }
    }
}