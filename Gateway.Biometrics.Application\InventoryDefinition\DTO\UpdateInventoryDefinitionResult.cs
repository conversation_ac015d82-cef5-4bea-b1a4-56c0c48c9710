﻿using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.InventoryDefinition.DTO
{
    public class UpdateInventoryDefinitionResult : BaseServiceResult<UpdateInventoryDefinitionStatus>
    {
        public int Id { get; set; }
    }

    public enum UpdateInventoryDefinitionStatus
    {
        Successful,
        ResourceExists,
        InvalidInput,
        InventoryDefinitionNotFound,
        InventoryTypeNotFound,
    }
}
