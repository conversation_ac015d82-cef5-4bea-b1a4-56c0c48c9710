﻿using System.Collections.Generic;

namespace Gateway.Cargo.Api.Models.UPS
{
    public class UPSTrackResult
    {
        public Trackresponse TrackResponse { get; set; }
    }

    public class Trackresponse
    {
        public Shipment[] Shipment { get; set; }
    }

    public class Shipment
    {
        public List<Package> Package { get; set; }
    }

    public class Package
    {
        public string TrackingNumber { get; set; }
        public List<Activity> Activity { get; set; }
    }

    public class Activity
    {
        public Location Location { get; set; }
        public Status Status { get; set; }
        public string Sate { get; set; }
        public string Time { get; set; }
    }

    public class Location
    {
        public Address Address { get; set; }
    }

    public class Address
    {
        public string City { get; set; }
        public string StateProvince { get; set; }
        public string PostalCode { get; set; }
        public string Country { get; set; }
    }

    public class Status
    {
        public string Type { get; set; }
        public string Description { get; set; }
        public string Code { get; set; }
    }
}
