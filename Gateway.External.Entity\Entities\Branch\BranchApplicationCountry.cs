﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using Gateway.External.Entity.Entities.PreApplications;
using Gateway.External.Entity.Entities.B2B.AppointmentDemand;
using Gateway.External.Entity.Entities.B2B.SlotDemand;

namespace Gateway.External.Entity.Entities.Branch
{
    public class BranchApplicationCountry
    {
        public BranchApplicationCountry()
        {
            IsActive = true;
        }

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Branch id from Branch entity
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// Branch authorized country id from Country entity
        /// </summary>
        public int CountryId { get; set; }

        [Column(TypeName = "citext"), MaxLength(1000)]
        public string Note { get; set; }

        public Branch Branch { get; set; }

        public Country.Country Country { get; set; }

        public ICollection<Application.Application> Applications { get; set; }

        public ICollection<Slot.Slot> Slots { get; set; }

        public ICollection<PreApplication> PreApplications { get; set; }

        public ICollection<SlotDemand> SlotDemands { get; set; }

		public ICollection<AppointmentDemand> AppointmentDemands { get; set; }

        public ICollection<BranchHolidaysBranchShiftHoliday> BranchHolidaysBranchShiftHolidays { get; set; }

        public ICollection<DayOfWeekBranchShiftHoliday> DayOfWeekBranchShiftHolidays { get; set; }
        public  ICollection<BranchApplicationCountryExtraFee> BranchApplicationCountryExtraFees { get; set; }

        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }

    }
}
