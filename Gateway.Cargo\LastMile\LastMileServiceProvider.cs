﻿using Gateway.Cargo.Dto.Request;
using Gateway.Cargo.Dto.Response;
using Gateway.Cargo.LastMile.Dto;
using Gateway.Cargo.LastMile.Dto.Request;
using Gateway.Cargo.LastMile.Dto.Result;
using Gateway.Http;
using System.Net;

namespace Gateway.Cargo.LastMile
{
    public class LastMileServiceProvider : ILastMileServiceProvider
    {
        private LastMileServiceConfig _config;
        private WebHeaderCollection _headers;
        private string _baseAddress;
        private string _basePublicAddress;
        private LastMileEndpointRouting _routing;

        public void Initialize<TConfig>(TConfig config)
        {
            _config = config as LastMileServiceConfig;

            if (_config == null)
                throw new Exception();

            _headers = new WebHeaderCollection
            {
                { "Authorization", $"Bearer {_config.Token}" }
            };

            _baseAddress = _config.BaseAddress;

            _basePublicAddress = _config.BasePublicAddress;

            _routing = new LastMileEndpointRouting();
        }

        public async Task<CargoTrackingResultDto> Tracking(string inquiryNumber, string packageId)
        {
            _headers.Add("x-app-type", "customer");

            if(packageId != null)
            {
                var getPackageResponse = await RestHttpClient.Create().Get<LastMileGetPackageResult>(
                    $"{_baseAddress}{_routing.GetPackage}{packageId}", _headers);

                if (getPackageResponse.Status != BaseLastMileStatus.Successful || getPackageResponse.Data is null || !getPackageResponse.Data.Any())
                {
                    return new CargoTrackingResultDto
                    {
                        Status = GetTrackingStatus.NotFound,
                        Message = getPackageResponse.Message
                    };
                }

                var lastPackageStatus = getPackageResponse.Data?.FirstOrDefault().PackageTracking.LastOrDefault(i => i.IsLatest);

                return new CargoTrackingResultDto
                {
                    Status = GetTrackingStatus.Successful,
                    CargoTrackingResult = new CargoTrackingResult
                    {
                        InquiryNumber = getPackageResponse.Data?.FirstOrDefault().Id,
                        TrackingNumber = getPackageResponse.Data?.FirstOrDefault().TrackNumber,
                        Status = getPackageResponse.Data?.FirstOrDefault().PackageTracking.Select(p => new TrackingStatus
                        {
                            Date = p.Date,
                            Time = $"{Convert.ToDateTime(p.Date):hh}{Convert.ToDateTime(p.Date):mm}",
                            Description = p.Status.Internal,
                        }).ToList(),
                        LastStatus = new TrackingStatus()
                        {
                            Date = lastPackageStatus?.Date,
                            Time = $"{Convert.ToDateTime(lastPackageStatus?.Date):hh}{Convert.ToDateTime(lastPackageStatus?.Date):mm}",
                            Description = lastPackageStatus?.Status.Internal,
                        }
                    }
                };
            }

            return new CargoTrackingResultDto
            {
                Status = GetTrackingStatus.NotFound,
                Message = "Invalid Package"
            };
        }

        public async Task<ShipmentResultDto> Shipment(ShipmentRequest request)
        {
            var shipmentRequest = new LastMileShipmentRequest
            {
                OfficeId = request.OfficeId,
                ShipmentType = "supplier"
            };

            var addShipmentResponse = await RestHttpClient.Create().Post<LastMileAddShipmentResult>(
                $"{_baseAddress}{_routing.AddShipment}",
                _headers, shipmentRequest,
                false);

            if (addShipmentResponse.Status != BaseLastMileStatus.Successful || addShipmentResponse.Data == null)
                return new ShipmentResultDto
                {
                    Status = GetShipmentStatus.BadRequest,
                    Message = addShipmentResponse.Message,
                    ShipmentResult = new ShipmentResult { ApplicationId = request.ApplicationId }
                };

            var shipmentId = addShipmentResponse.Data.Id;

            var packageRequest = new LastMilePackageRequest
            {
                Address = request.Application.Address,
                DocId = request.Application.PassportNumber,
                AreaId = request.AreaId,
                HolderName = $"{request.Application.Name} {request.Application.Surname}",
                PackageType = "Supplier",
                ShipmentId = shipmentId,
                RecipientPhoneNumber = request.Application.Phone1,
                SecondRecipientPhoneNumber = request.Application.Phone2,
            };

            var addPackageResponse = await RestHttpClient.Create().Post<LastMileAddPackageResult>(
                $"{_baseAddress}{_routing.AddPackage}",
                _headers, packageRequest,
                false);

            if (addPackageResponse.Status != BaseLastMileStatus.Successful || addPackageResponse.Data != true)
                return new ShipmentResultDto
                {
                    Status = GetShipmentStatus.BadRequest,
                    Message = addPackageResponse.Message,
                    ShipmentResult = new ShipmentResult { ApplicationId = request.ApplicationId }
                };

            var shipmentDetail = await RestHttpClient.Create().Get<LastMileShipmentDetailResult>
                ($"{_baseAddress}{_routing.GetShipments}?page=1&take=10", _headers);

			if (shipmentDetail.Status != BaseLastMileStatus.Successful || !shipmentDetail.Data.Any())
                return new ShipmentResultDto
                {
                    Status = GetShipmentStatus.NotFound,
                    Message = shipmentDetail.Message,
                    ShipmentResult = new ShipmentResult { ApplicationId = request.ApplicationId }
                };

            var getPackageDetailResponse = await RestHttpClient.Create().Get<LastMilePackageDetailResult>(
                $"{_baseAddress}{_routing.GetShipmentPackages}{shipmentId}?page=1", _headers);

            if (getPackageDetailResponse.Status != BaseLastMileStatus.Successful || !getPackageDetailResponse.Data.Any())
                return new ShipmentResultDto
                {
                    Status = GetShipmentStatus.NotFound,
                    Message = getPackageDetailResponse.Message,
                    ShipmentResult = new ShipmentResult { ApplicationId = request.ApplicationId }
                };

            return new ShipmentResultDto
            {
                Status = GetShipmentStatus.Successful,
                ShipmentResult = new ShipmentResult
                {
                    ApplicationId = request.ApplicationId,
                    Description = getPackageDetailResponse.Data.FirstOrDefault()?.Status.Internal.FirstOrDefault(r => r.LanguageCode == "en")?.Title,
                    ShipmentId = shipmentId,
                    PackageId = getPackageDetailResponse.Data.FirstOrDefault()?.Id,
					GraphicImage = getPackageDetailResponse.Data.FirstOrDefault()?.QrCodeImage,
                    TrackingNumber = shipmentDetail.Data.FirstOrDefault(r => r.Id.Equals(shipmentId)).TrackNumber,
					PackageTrackingNumber = getPackageDetailResponse.Data.FirstOrDefault()?.TrackNumber,
				}
            };
        }

        public Task<ShipmentResultDto> LabelRecovery(LabelRecoveryServiceRequest request)
        {
            throw new NotImplementedException();
        }

        public async Task<GetAreaResultDto> GetArea(string governorateId)
        {
            var getAreaResponse = await RestHttpClient.Create().Get<LastMileGetAreaResult>(
                $"{_baseAddress}{_routing.GetAreas}?governorateId={governorateId}", _headers);

            if (getAreaResponse.Status != BaseLastMileStatus.Successful || getAreaResponse.Data == null || !getAreaResponse.Data.Any())
                return new GetAreaResultDto
                {
                    Status = GetAreaStatus.NotFound,
                    Message = getAreaResponse.Message
                };

            return new GetAreaResultDto()
            {
                Status = GetAreaStatus.Successful,
                AreaList = getAreaResponse.Data.Select(r => new GetAreaResult()
                {
                    Id = r.Id,
                    Title = r.Title.FirstOrDefault()?.Title
                }).ToList()
            };
        }

        public async Task<GetGovernorateResultDto> GetGovernorate()
        {
            var getGovernorateResponse = await RestHttpClient.Create().Get<LastMileGetGovernorateResult>(
              $"{_baseAddress}{_routing.GetGovernorates}", _headers);

            if (getGovernorateResponse.Status != BaseLastMileStatus.Successful || getGovernorateResponse.Data == null || !getGovernorateResponse.Data.Any())
                return new GetGovernorateResultDto
                {
                    Status = GetGovernorateStatus.NotFound,
                    Message = getGovernorateResponse.Message
                };

            return new GetGovernorateResultDto()
            {
                Status = GetGovernorateStatus.Successful,
                AreaList = getGovernorateResponse.Data.Select(r => new GetGovernorateResult()
                {
                    Id = r.Id,
                    Title = r.Title.FirstOrDefault()?.Title
                }).ToList()
            };
        }

        public async Task<UpdateStatusDto> UpdateStatus(UpdateStatusRequest request)
        {
            var updateStatusRequest = new LastMileUpdateStatusRequest
            {
                StatusId = "b0e8cb8d-2c6b-4dcb-b67a-4bd033065571", // ready to ship
            };

            var updateShipmentStatusResponse = await RestHttpClient.Create()
                .Patch<LastMileUpdateStatusResult>($"{_baseAddress}{_routing.UpdateStatus}{request.ShipmentId}",
                    _headers, updateStatusRequest);

            if (updateShipmentStatusResponse.Status != BaseLastMileStatus.Successful || updateShipmentStatusResponse.Data?.StatusText != "Updated")
                return new UpdateStatusDto
                {
                    Status = UpdateCargoStatus.BadRequest,
                    Message = updateShipmentStatusResponse.Message
                };

            return new UpdateStatusDto
            {
                Status = UpdateCargoStatus.Successful,
            };
        }
    }
}
