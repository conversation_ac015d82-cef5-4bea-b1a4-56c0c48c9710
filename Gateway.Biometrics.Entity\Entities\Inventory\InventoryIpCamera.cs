﻿using Gateway.Biometrics.Entity.Entities.Category;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace Gateway.Biometrics.Entity.Entities.Inventory
{
    public class InventoryIpCamera : BaseEntity
    {
        public int InventoryId { get; set; }
        public string Name { get; set; }
        public Guid CameraId { get; set; }
        public string Description { get; set; }

    }
}
