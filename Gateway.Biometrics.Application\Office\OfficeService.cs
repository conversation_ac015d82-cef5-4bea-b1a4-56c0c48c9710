﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Gateway.Biometrics.Persistence;
using Gateway.Biometrics.Resources;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Gateway.Core.Pagination;
using Gateway.Extensions;
using Gateway.Biometrics.Application.Lookup;
using Gateway.Biometrics.Application.Inventory.Validator;
using Gateway.Biometrics.Application.Office.DTO;
using Gateway.Biometrics.Application.Office.Validator;
using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.Cabin.Validator;
using Gateway.Biometrics.Application.Country.DTO;

namespace Gateway.Biometrics.Application.Office
{
    public class OfficeService : IOfficeService
    {
        private readonly BiometricsDbContext _dbContext;
        private readonly ExternalDbContext _externalDbContext;
        private readonly IValidationService _validationService;
        private IMapper _mapper;

        public OfficeService(IValidationService validationService, BiometricsDbContext dbContext, ExternalDbContext externalDbContext, IMapper mapper)
        {
            _validationService = validationService;
            _dbContext = dbContext;
            _externalDbContext = externalDbContext;
            _mapper = mapper;
        }

        public async Task<GetOfficeResult> GetOffice(GetOfficeRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetOfficeValidator), request);

            if (!validationResult.IsValid)
                return new GetOfficeResult
                {
                    Status = GetOfficeStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var existingOffice = await _dbContext.Office.Where(p => p.Id == request.ResourceId && !p.IsDeleted)
                .FirstOrDefaultAsync();

            if (existingOffice == null)
                return new GetOfficeResult
                {
                    Status = GetOfficeStatus.NotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };

            var officeGet = _mapper.Map<OfficeDto>(existingOffice);

            return new GetOfficeResult
            {
                Status = GetOfficeStatus.Successful,
                Message = ServiceResources.RESOURCE_FOUND,
                Office = officeGet
            };
        }


        public async Task<CreateOfficeResult> CreateOffice(CreateOfficeRequest request)
        {
            var validationResult = _validationService.Validate(typeof(CreateOfficeValidator), request);

            if (!validationResult.IsValid)
                return new CreateOfficeResult
                {
                    Status = CreateOfficeStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };


            var countryCount = _externalDbContext.Country.Count(t => t.Id == request.CountryId);

            if (countryCount > 1)
            {
                return new CreateOfficeResult
                {
                    Status = CreateOfficeStatus.CountryNotfound,
                    Message = ServiceResources.COUNTRY_NOT_FOUND
                };
            }

            var newOffice = new Entity.Entities.Office.Office()
            {
                Name = request.Name,
                OfficeCode = request.OfficeCode,
                Phone = request.Phone,
                Email = request.Email,
                Address = request.Address,
                Status = request.Status,
                CountryId = request.CountryId,
                BranchId = request.BranchId,
            };

            await _dbContext.Office.AddAsync(newOffice);
            
            await _dbContext.SaveChangesAsync();

            return new CreateOfficeResult
            {
                Status = CreateOfficeStatus.Successful,
                Message = ServiceResources.RESOURCE_CREATED,
                Id = newOffice.Id
            };
        }

        public async Task<UpdateOfficeResult> UpdateOffice(UpdateOfficeRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateOfficeValidator), request);

            if (!validationResult.IsValid)
                return new UpdateOfficeResult
                {
                    Status = UpdateOfficeStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var officeExisting = await _dbContext.Office
                .Where(q => q.Id == request.OfficeId).FirstOrDefaultAsync();

            if (officeExisting == null)
                return new UpdateOfficeResult
                {
                    Status = UpdateOfficeStatus.OfficeNotFound,
                    Message = ServiceResources.INVENTORY_DEFINITION_NOT_FOUND,
                };


            var countryCount = _externalDbContext.Country.Count(t => t.Id == request.CountryId);

            if (countryCount > 1)
            {
                return new UpdateOfficeResult
                {
                    Status = UpdateOfficeStatus.CountryNotfound,
                    Message = ServiceResources.COUNTRY_NOT_FOUND
                };
            }

            officeExisting.Name = request.Name;
            officeExisting.OfficeCode = request.OfficeCode;
            officeExisting.Phone = request.Phone;
            officeExisting.Email = request.Email;
            officeExisting.Address = request.Address;
            officeExisting.Status = request.Status;
            officeExisting.CountryId = request.CountryId;
            officeExisting.BranchId = request.BranchId;


            officeExisting.IsDeleted = false;
            officeExisting.UpdatedAt = DateTime.Now;

            _dbContext.Office.Update(officeExisting);

            await _dbContext.SaveChangesAsync();

            return new UpdateOfficeResult
            {
                Status = UpdateOfficeStatus.Successful,
                Message = ServiceResources.RESOURCE_UPDATED,
                Id = officeExisting.Id
            };
        }
        
        public async Task<DeleteOfficeResult> DeleteOffice(DeleteOfficeRequest request)
        {
            var validationResult = _validationService.Validate(typeof(DeleteOfficeValidator), request);

            if (!validationResult.IsValid)
                return new DeleteOfficeResult
                {
                    Status = DeleteOfficeStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var officeExisting = await _dbContext.Office
                .Where(p => !p.IsDeleted && p.Id == request.OfficeId).FirstOrDefaultAsync();

            if (officeExisting == null)
                return new DeleteOfficeResult
                {
                    Status = DeleteOfficeStatus.ResourceNotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };

            officeExisting.DeletedAt = DateTime.Now;
            officeExisting.IsDeleted = true;

            _dbContext.Office.Update(officeExisting);
            await _dbContext.SaveChangesAsync();

            return new DeleteOfficeResult
            {
                Status = DeleteOfficeStatus.Successful,
                Message = ServiceResources.RESOURCE_DELETED
            };
        }

    
        public async Task<GetPaginatedOfficesResult> GetPaginatedOffices(GetPaginatedOfficesRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetPaginatedOfficesValidator), request);

            if (!validationResult.IsValid)
                return new GetPaginatedOfficesResult
                {
                    Status = GetPaginatedOfficesStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var queryOffices =  _dbContext.Office
                .Where(p => !p.IsDeleted);

            if (request.FilterCountryId.HasValue)
            {
                queryOffices = queryOffices.Where(n => n.CountryId == request.FilterCountryId.Value);
            }

            var listOffices = await queryOffices.ToListAsync();

            if (listOffices == null || listOffices.Count == 0)
                return new GetPaginatedOfficesResult
                {
                    Status = GetPaginatedOfficesStatus.ResourceNotFound,
                    Message = ServiceResources.OFFICE_NOT_FOUND
                };

            var result = new GetPaginatedOfficesResult
            {
                Offices = listOffices.Select(p => _mapper.Map<OfficeDto>(p))
            };

            var paginationResult = PagedResultsFactory.CreatePagedResult(
                result.Offices.AsQueryable(), request.Pagination.PageNumber, request.Pagination.PageSize,
                request.Pagination.OrderBy, request.Pagination.Ascending);

            return paginationResult == null
                ? new GetPaginatedOfficesResult
                {
                    Offices = null, Status = GetPaginatedOfficesStatus.ResourceNotFound,
                    Message = ServiceResources.INVALID_INPUT_ERROR
                }
                : new GetPaginatedOfficesResult
                {
                    Offices = paginationResult.Results, Status = GetPaginatedOfficesStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    TotalNumberOfPages = paginationResult.TotalNumberOfPages,
                    TotalNumberOfRecords = paginationResult.TotalNumberOfRecords,
                };
        }
        
        private static LookupValue GetLookupValue(int enumType, int? value)
        {
            return EnumExtensions.GetEnumAsDictionary(LookupTypeFactory.GetInstance(enumType))
                .Select(x => new LookupValue { Id = x.Key.ToString(), DisplayValue = x.Value })
                .FirstOrDefault(p => p.Id == value.ToString());
        }
    }
}