{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "ConnectionStrings": {
    "GatewayBiometricsDbConnection": "Server=**********; Database=GatewayBiometrics; Username=gw_portal_stg_app_usr;Password=*****************;",
    "GatewayExternalApiDbConnection": "Server=**********; Database=GatewayPortalDbStage1; Username=gw_portal_stg_app_usr;Password=*****************;"
  },
  "IdentityServer": {
    "Address": "http://***********:8000"
  },
  "Jwt": {
    "Issuer": "https://localhost:5011",
    "Audience": "https://localhost:5011",
    "Key": "MIHcAgEBBEIBR6xEV8oABUEl4nnl7YsTgHx+BKb9niG9u+rcHhRTItyxKXqwHZpfrM3xAqWtK21vU07QITDgJyyAHnVV+LdRH+igBwYFK4EEACOhgYkDgYYABAANEKvt1SAXyXha1909fyA3G0b5XM3Wkha6EwR4V9X6SLBfkrWh29C92qE/21F8gAJJAylR23gXdq1BB75UTI8REwDHF7TYX/8pWGfqm5OntWMo/Y5vzzMQJTD6fFHzVx5MVrDykNu7UhN7G9DBXl24InomIaY+VIyKmB1AvxtRBzX8gw==",
    "ExpiresIn": 2880 // minutes
  },
  "AppSettings": {
    "MinioConfiguration": {
      "EndPoint": "visacdn.gateway.com.tr:443",
      "AccessKey": "m9ZILox0eHb1HEea",
      "SecretKey": "jRcb0JDpBO8utC66iY800NBE4Yf1glI5",
      "BucketPrefix": "",
      "IsSecure": "true"
    },
    "FileEncryptDecrypt": {
      "CipherKey": "DrdI28+0ag282rrqHsmxEwxK4K/FAKo8Pl03rVWC9zw=",
      "CipherIV": "aa0OzGWMLJGfpxCrz/h5PQ=="
    },
    "Ldap": {
      "PrimaryServer": {
        "HostName": "GWDC01.gateway.com.tr",
        "Port": 389
      },
      "SecondaryServer": {
        "HostName": "GWDC02.gateway.com.tr",
        "Port": 389
      },
      "SearchBase": "DC=gateway,DC=com,DC=tr",
      "UserIdAttributeName": "sAMAccountName",
      "ServiceUserDN": "CN=Gateway Portal Service,OU=SERVICE_USERS,OU=USERS-GLOBAL,DC=gateway,DC=com,DC=tr",
      "ServiceUserPassword": "2024@Pass",
      "AllowedUnits": [
        "OU=USERS-GLOBAL,DC=gateway,DC=com,DC=tr"
      ],
      "NotAllowedUnits": [
        "OU=MYNEST,OU=USERS-GLOBAL,DC=gateway,DC=com,DC=tr",
        "OU=SERVICE_USERS,OU=USERS-GLOBAL,DC=gateway,DC=com,DC=tr",
        "OU=EXTERNALS,OU=USERS-GLOBAL,DC=gateway,DC=com,DC=tr"
      ],
      "Notification": {
        "Subject": "Kullanıcı İşlemleri Hakkında Bilgilendirme",
        "Contact": "<EMAIL>"
      }
    }
  },
  "Innovatrics": {
    "Dis": {
      "ServiceUrl": "http://localhost:8080",
      "ApiVersion": "v1",
      "Token": "aW5rX2YyYWZhYjY0ZjlhZTJjYWZlZjkyNDFiZjc2MjNhYzg3Omluc19leUp0WlhSaFpHRjBZU0k2SUhzaVkyeHBaVzUwSWpvZ2V5SnBaQ0k2SUNKaFlXSXdOak5tWXkwek5UQmpMVFF6TldRdE9URTVNQzAzTmpReU1HTXlZekV6TVRNaUxDQWlibUZ0WlNJNklDSkhiMnhrWlc0Z1IyRjBaWGRoZVNCTllXNWhaMlZ0Wlc1MEluMHNJQ0pzYVdObGJuTmxYMk4xYzNSdmJWOXdjbTl3WlhKMGFXVnpJam9nZXlJdlkyOXVkSEpoWTNRdlpHOTBMMlJwY3k5bGJtRmliR1ZrSWpvZ0luUnlkV1VpTENBaUwyTnZiblJ5WVdOMEwyUnZkQzlsZG1Gc2RXRjBhVzl1SWpvZ0luUnlkV1VpTENBaUwyTnZiblJ5WVdOMEwyUnZkQzlrYVhNdmJHbGpaVzV6WlZabGNuTnBiMjRpT2lBaU15SjlMQ0FpWTNKbFlYUnBiMjVmZEdsdFpYTjBZVzF3SWpvZ0lqQXlMekExTHpJd01qVWdNRGM2TXpZNk1qZ2dWVlJESWl3Z0luWmhiR2xrWDNSdklqb2dJakEwTHpBekx6SXdNalVnTURBNk1EQTZNREFnVlZSREluMHNJQ0p6YVdkdVlYUjFjbVVpT2lBaWJUZE1WV3RJTTIxcFJURXdZakZtZFdOQ1puWkpVWEo1UmpaVGIzRnNaRTEzWTBkT1VGQlZWRUpGZDJnMmJuVk9iV1JyU1hkT1owNVBkMDlqZGxCUmQwMUdhR2xsZUZsUmRUZFZUM2hKUVVaM1ExSmpRbEU5UFNKOQ=="
    }
  }
}
