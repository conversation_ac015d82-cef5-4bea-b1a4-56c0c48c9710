﻿using System.Collections.Generic;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Application.InventoryDefinition.DTO;

namespace Gateway.Biometrics.Application.Parameter.DTO
{
    public class InventoryDefinitionsDto
    {
        public IList<InventoryDefinitionSelectDto> InventoryDefinitions { get; set; }
    }
    public class InventoryDefinitionSelectDto
    {
        public int Id { get; set; }
        public InventoryDefinitionDto InventoryDefinition { get; set; }
    }

    public class InventoryDefinitionsRequestDto
    {
        
    }

    public class InventoryDefinitionsResult : BaseServiceDataResult<InventoryDefinitionsDto>
    {
        public int Id { get; set; }
    }
}
