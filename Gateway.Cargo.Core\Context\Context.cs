﻿using Microsoft.AspNetCore.Http;
using System;
using System.Globalization;
using System.Threading;

namespace Gateway.Cargo.Core.Context
{
    internal class Context : IContext
    {
        public string RequestId { get; } = $"{Guid.NewGuid():N}";
        public string TraceId { get; } = $"{Guid.NewGuid():N}";
        public string IpAddress { get; }
        public string UserAgent { get; }
        public string AcceptLanguage { get; }
        public int LanguageId { get; set; }
        public IIdentityContext Identity { get; }

        private Context() { }

        public Context(HttpContext context) : this(context.TraceIdentifier, new IdentityContext(context))
        {
            IpAddress = context.Connection.RemoteIpAddress.ToString();
            UserAgent = context.Request.Headers["User-Agent"].ToString();
            AcceptLanguage = context.Request.Headers["Accept-Language"].ToString();
            LanguageId = GetLanguageId();

            var cultureInfo = CultureInfo.GetCultureInfo(GetLanguage());

            Thread.CurrentThread.CurrentCulture = cultureInfo;
            Thread.CurrentThread.CurrentUICulture = cultureInfo;
        }

        private Context(string traceId, IIdentityContext identity)
        {
            TraceId = traceId;
            Identity = identity;
        }

        private string GetLanguage()
        {
            switch (AcceptLanguage)
            {
                case "en-US":
                    return "en-US";
                case null:
                    return "en-US";
                case "tr-TR":
                    return "tr-TR";
                default:
                    return "en-US";
            }
        }

        private int GetLanguageId()
        {
            switch (AcceptLanguage)
            {
                case "en-US":
                    return 2;
                case null:
                    return 2;
                case "tr-TR":
                    return 1;
                default:
                    return 2;
            }
        }

        public static IContext Empty => new Context();
    }
}
