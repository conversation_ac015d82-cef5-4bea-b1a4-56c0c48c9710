﻿using System.Collections.Generic;
using System;
using Gateway.Core.Pagination;

namespace Gateway.Biometrics.Api.Models.InventoryAttribute
{
    public class BaseInventoryAttributeRequestModel
    {
        public string Name { get; set; }
        public string FieldType { get; set; }
        public string DataType { get; set; }
        public string DefaultValue { get; set; }
        public int MaxLength { get; set; }
        public int SortOrder { get; set; }
        public int Status { get; set; }
        public bool IsRequired { get; set; }
        public bool IsSingleValue { get; set; }

        public BaseInventoryAttributeRequestModel()
        {
        }
    }
    
    public class CreateInventoryAttributeRequestModel : BaseInventoryAttributeRequestModel
    {
    }

    public class UpdateInventoryAttributeRequestModel : BaseInventoryAttributeRequestModel
    {
    }

    public class DeleteInventoryAttributeRequestModel
    {
        public int InventoryAttributeId { get; set; }
    }
    
    public class GetPaginatedInventoryAttributesRequestModel
    {
        public PaginationRequest Pagination { get; set; }
    }
}