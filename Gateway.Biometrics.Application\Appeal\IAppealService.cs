﻿using Gateway.Biometrics.Application.Appeal.DTO;
using Gateway.Biometrics.Application.DemographicInformation.DTO;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Appeal
{
    public interface IAppealService
    {
        Task<CreateAppealResult> CreateAppeal(CreateAppealRequest request);
        Task<InsertAppealWithMetaDataResult> InsertAppealWithMetaData(InsertAppealWithMetaDataRequest request);
        Task<InsertAppealWithMetaDataFromOfflineResult> InsertAppealWithMetaDataFromOffline(InsertAppealWithMetaDataFromOfflineRequest request);
        Task<InsertAppealWithMetaDataFastResult> InsertAppealWithMetaDataFast(InsertAppealWithMetaDataFastRequest request);
        Task<SaveAppealMetaDataFullResult> SaveAppealMetaDataFull(SaveAppealMetaDataFullRequest request);
        Task<InsertAppealMetaDataResult> InsertAppealMetaData(InsertAppealMetaDataRequest request);
        Task<GetAppealsResult> GetAppealsByPassportAndCountry(GetAppealsByPassportAndCountryRequest request);
        Task<GetAppealsResult> GetAppealsByXml(GetAppealsByXmlRequest request);
        Task<GetFullAppealMetaDataResult> GetFullAppealMetaDataById(GetFullAppealMetaDataRequest request);
    }
}
