﻿using Gateway.Cargo.UPS.Dto.Request;

namespace Gateway.Cargo.UPS.Dto.Response
{
    public class UPSLabelRecoveryResponse : BaseUpsResult<GetStatus>
    {
        public Response Response { get; set; }

        public LabelRecoveryResponse LabelRecoveryResponse { get; set; }    
    }

    public class LabelRecoveryResponse
    {
        public Response Response { get; set; }
        public string ShipmentIdentificationNumber { get; set; }
        public LabelResults LabelResults { get; set; }

    }

    public class LabelResults
    {
        public string TrackingNumber { get; set; }
        public LabelImage LabelImage { get; set; }
    }

    public class LabelImage
    {
        public LabelImageFormat LabelImageFormat { get; set; }
        public string GraphicImage { get; set; }
    }
}
