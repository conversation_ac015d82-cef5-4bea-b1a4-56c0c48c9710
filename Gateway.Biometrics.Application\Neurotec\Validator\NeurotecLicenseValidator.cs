﻿using System;
using System.Linq;
using FluentValidation;
using Gateway.Extensions;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Entity.Entities.Inventory;
using Gateway.Biometrics.Resources;
using Gateway.Validation;
using Gateway.Biometrics.Application.Neurotec.DTO;

namespace Gateway.Biometrics.Application.Neurotec.Validator
{
    internal class GetNeurotecLicenseValidator : AbstractValidator<GetNeurotecLicenseRequest>
    {
        public GetNeurotecLicenseValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.ResourceId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.ResourceId)));
            });
        }
    }

  
    internal class UpdateNeurotecLicenseValidator : AbstractValidator<UpdateNeurotecLicenseRequest>
    {
        public UpdateNeurotecLicenseValidator()
        {
            RuleFor(p => p).Custom((item, context) => {

            });
        }
    }

    internal class GetPaginatedNeurotecLicensesValidator : AbstractValidator<GetPaginatedNeurotecLicensesRequest>
    {
        public GetPaginatedNeurotecLicensesValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (string.IsNullOrWhiteSpace(item.Pagination.OrderBy))
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Pagination.OrderBy)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageSize.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageSize)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageNumber.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageNumber)));
            });
        }
    }
}