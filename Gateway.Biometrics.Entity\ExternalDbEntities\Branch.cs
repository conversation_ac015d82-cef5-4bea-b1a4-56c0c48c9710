﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gateway.Biometrics.Entity.ExternalDbEntities
{
    public class Branch
    {
        public Branch()
        {
            IsActive = true; 
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public int CountryId { get; set; }

        /// <summary>
        /// Branch address
        /// </summary>
        [Required]
        [Column(TypeName = "citext"), MaxLength(200)]
        public string Address { get; set; }

        /// <summary>
        /// Branch email
        /// </summary>
        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string Email { get; set; }

        /// <summary>
        /// Branch telephone
        /// </summary>
        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string Telephone { get; set; }

        /// <summary>
        /// Branch city name
        /// </summary>
        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string CityName { get; set; }

        /// <summary>
        /// Branch mission
        /// </summary>
        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string Mission { get; set; }

        /// <summary>
        /// Branch invoice number
        /// </summary>
        [Required]
        [Column(TypeName = "citext"), MaxLength(50)]
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// Branch corporate name
        /// </summary>
        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string CorporateName { get; set; }

        public int LocalTimeOffset { get; set; }

        public bool CheckRejectedStatus { get; set; }

        public int CheckRejectedStatusPeriod { get; set; }

        public bool CheckUnrealDocumentStatus { get; set; }

        public int CheckUnrealDocumentStatusPeriod { get; set; }

        public bool IsValidAMS { get; set; }

        [Column(TypeName = "citext"), MaxLength(200)]
        public string QueueMaticPlaylistId { get; set; }

        public int? MaxAppointmentDay { get; set; }

        [Required]
        public int InsuranceProviderId { get; set; }

        public Country Country { get; set; }

        public ICollection<BranchApplicationCountry> BranchApplicationCountries { get; set; }

        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
    }
}

