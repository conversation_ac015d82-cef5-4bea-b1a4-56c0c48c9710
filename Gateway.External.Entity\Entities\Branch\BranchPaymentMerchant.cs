﻿using Gateway.External.Entity.Entities.Payment;

namespace Gateway.External.Entity.Entities.Branch
{
    public class BranchPaymentMerchant
    {
        public int Id { get; set; }
        public int BranchId { get; set; }
        public int PaymentMerchantId { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public Branch Branch { get; set; }
        public PaymentMerchant PaymentMerchant { get; set; }
    }
}
