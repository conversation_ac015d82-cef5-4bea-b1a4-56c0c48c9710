﻿using FluentValidation;
using Gateway.Extensions;
using Gateway.Biometrics.Resources;
using Gateway.Biometrics.Application.ClientConfiguration.DTO;
using Gateway.Biometrics.Application.Cabin.DTO;

namespace Gateway.Biometrics.Application.ClientConfiguration.Validator
{
    internal class GetClientConfigurationValidator : AbstractValidator<GetClientConfigurationRequest>
    {
        public GetClientConfigurationValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.ResourceId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.ResourceId)));
            });
        }
    }

    internal class CreateClientConfigurationValidator : AbstractValidator<CreateClientConfigurationRequest>
    {
        public CreateClientConfigurationValidator()
        {
            RuleFor(p => p).Custom((item, context) => {

                if (item.HostName.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.HostName)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Status.IsNumeric())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Status)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.CountryId.IsNumeric())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.CountryId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.OfficeId.IsNumeric())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.OfficeId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.CabinId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.CabinId)));
            });


        }
    }

    internal class UpdateClientConfigurationValidator : AbstractValidator<UpdateClientConfigurationRequest>
    {
        public UpdateClientConfigurationValidator()
        {
            RuleFor(p => p).Custom((item, context) => {

                if (item.HostName.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.HostName)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Status.IsNumeric())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Status)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.CountryId.IsNumeric())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.CountryId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.OfficeId.IsNumeric())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.OfficeId)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.CabinId.IsNumeric())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.CabinId)));
            });
        }
    }
    internal class DeleteClientConfigurationValidator : AbstractValidator<DeleteClientConfigurationRequest>
    {
        public DeleteClientConfigurationValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.ClientConfigurationId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.ClientConfigurationId)));
            });
        }
    }

    internal class GetPaginatedClientConfigurationsValidator : AbstractValidator<GetPaginatedClientConfigurationsRequest>
    {
        public GetPaginatedClientConfigurationsValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (string.IsNullOrWhiteSpace(item.Pagination.OrderBy))
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Pagination.OrderBy)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageSize.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageSize)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageNumber.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageNumber)));
            });
        }
    }
}