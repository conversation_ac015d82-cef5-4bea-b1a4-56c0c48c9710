﻿using Gateway.Biometrics.Entity.ExternalDbEntities;
using Microsoft.EntityFrameworkCore;

namespace Gateway.Biometrics.Persistence
{
    public class ExternalDbContext : DbContext
    {
        public ExternalDbContext(DbContextOptions<ExternalDbContext> options) : base(options) { }
        public DbSet<Application> Application { get; set; }
        public DbSet<Country> Country { get; set; }
        public DbSet<BranchApplicationCountry> BranchApplicationCountry { get; set; }
        public DbSet<Branch> Branch { get; set; }
        public DbSet<City> City { get; set; }
        public DbSet<User> User { get; set; }
        public DbSet<BranchSupervisorDefinition> BranchSupervisorDefinition { get; set; }
        public DbSet<UserBranch> UserBranch { get; set; }

    }
}
