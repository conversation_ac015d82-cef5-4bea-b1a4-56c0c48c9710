﻿using Gateway.External.Entity.Entities.Application;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gateway.External.Entity.Entities.Branch
{
    public class BranchApplicationStatus
    {
        public BranchApplicationStatus()
        {
            IsActive = true;
        }

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        public int BranchId { get; set; }

        public int ApplicationStatusId { get; set; }

        public bool IsApplicationUpdateAllowed { get; set; }

        public bool IsNewApplicationWithSamePassportNumberBlocked { get; set; }

        public bool IsRestrictChangeContactInformation { get; set; }

        public bool IsEsimBeSent { get; set; }

        public bool IsApplicationStatusHidden { get; set; }

        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }

        public Branch Branch { get; set; }

        public ApplicationStatus ApplicationStatus { get; set; }
    }

    public class BranchApplicationStatusEntityConfiguration : IEntityTypeConfiguration<BranchApplicationStatus>
    {
        public void Configure(EntityTypeBuilder<BranchApplicationStatus> builder)
        {
            builder.HasOne(m => m.Branch)
                .WithMany(g => g.BranchApplicationStatus)
                .HasForeignKey(s => s.BranchId)
                .OnDelete(DeleteBehavior.Restrict)
                .IsRequired();

            builder.HasOne(m => m.ApplicationStatus)
                .WithMany(g => g.BranchApplicationStatus)
                .HasForeignKey(s => s.ApplicationStatusId)
                .OnDelete(DeleteBehavior.Restrict)
                .IsRequired();
        }
    }
}
