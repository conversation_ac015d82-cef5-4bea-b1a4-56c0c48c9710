﻿using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.Biometrics.Api.Models.Inventory
{
    public static class InventoryResponseFactory
    {

        public static ObjectResult GetInventoryResponse(GetInventoryResult result)
        {
            switch (result.Status)
            {
                case GetInventoryStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.Inventory
                    })
                    { StatusCode = HttpStatusCodes.Created };
                case GetInventoryStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case GetInventoryStatus.NotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INVENTORY_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.INVENTORY_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case GetInventoryStatus.InventoryDefinitionNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INVENTORY_DEFINITION_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.INVENTORY_DEFINITION_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }


        public static ObjectResult CreateInventoryResponse(CreateInventoryResult result)
        {
            switch (result.Status)
            {
                case CreateInventoryStatus.Successful:
                    return new ObjectResult(new BasePaginationApiResponse
                    {
                            Status = ServiceResources.SUCCESS,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_CREATED),
                            Message = result.Message,
                            Data = result
                        })
                        { StatusCode = HttpStatusCodes.Created };
                case CreateInventoryStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.INPUT_ERROR,
                            Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                            Message = result.Message,
                            ValidationMessages = result.ValidationMessages
                        })
                        { StatusCode = HttpStatusCodes.InvalidInput };
                case CreateInventoryStatus.InventoryDefinitionNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.INVENTORY_DEFINITION_NOT_FOUND,
                            Code = Resource.GetKey(ServiceResources.INVENTORY_DEFINITION_NOT_FOUND),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                
                case CreateInventoryStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.ResourceExist };
                default:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.FAILED),
                            Message = ServiceResources.FAILED
                        })
                        { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult UpdateInventoryResponse(UpdateInventoryResult result)
        {
            switch (result.Status)
            {
                case UpdateInventoryStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_UPDATED),
                        Message = result.Message,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case UpdateInventoryStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case UpdateInventoryStatus.BranchNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.BRANCH_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.BRANCH_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateInventoryStatus.InventoryNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INVENTORY_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.INVENTORY_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateInventoryStatus.InventoryDefinitionNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INVENTORY_DEFINITION_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.INVENTORY_DEFINITION_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateInventoryStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult DeleteInventoryResponse(DeleteInventoryResult result)
        {
            switch (result.Status)
            {
                case DeleteInventoryStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.SUCCESS,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_DELETED),
                            Message = result.Message,
                            Data = result
                        })
                        { StatusCode = HttpStatusCodes.Ok };
                case DeleteInventoryStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.INPUT_ERROR,
                            Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                            Message = result.Message,
                            ValidationMessages = result.ValidationMessages
                        })
                        { StatusCode = HttpStatusCodes.InvalidInput };
                case DeleteInventoryStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.FAILED),
                            Message = ServiceResources.FAILED
                        })
                        { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult GetPaginatedInventoriesResponse(GetPaginatedInventoriesResult result)
        {
            switch (result.Status)
            {
                case GetPaginatedInventoriesStatus.Successful:
                    return new ObjectResult(new BasePaginationApiResponse
                    {
                            Status = ServiceResources.SUCCESS,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                            Message = result.Message,
                            Data = result.Inventories,
                            TotalNumberOfRecords = result.TotalNumberOfRecords,
                            TotalNumberOfPages = result.TotalNumberOfPages
                        })
                        { StatusCode = HttpStatusCodes.Ok };
                case GetPaginatedInventoriesStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.INPUT_ERROR,
                            Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                            Message = result.Message,
                            ValidationMessages = result.ValidationMessages
                        })
                        { StatusCode = HttpStatusCodes.InvalidInput };
                case GetPaginatedInventoriesStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.FAILED),
                            Message = ServiceResources.FAILED
                        })
                        { StatusCode = HttpStatusCodes.InternalError };
            }
        }
    }
}