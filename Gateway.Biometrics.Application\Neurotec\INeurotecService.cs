﻿using Gateway.Biometrics.Application.Neurotec.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Neurotec
{
    public interface INeurotecService
    {
        Task<GetNeurotecLicenseResult> GetNeurotecLicense(GetNeurotecLicenseRequest request); 

        Task<UpdateNeurotecLicenseResult> UpdateNeurotecLicense(UpdateNeurotecLicenseRequest request);

        Task<GetPaginatedNeurotecLicensesResult> GetPaginatedNeurotecLicenses(GetPaginatedNeurotecLicensesRequest request);
    }
}
