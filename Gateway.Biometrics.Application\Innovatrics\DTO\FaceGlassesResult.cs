﻿using Gateway.Biometrics.Resources;
using Gateway.Core.CustomAttributes;
using Gateway.Core.Responses.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Innovatrics.DTO
{
    public class FaceGlassesResult : BaseServiceResult<FaceGlassesStatus>
    {
        public string ErrorCode { get; set; }

        public double Score { get; set; }
        public double Tinted { get; set; }
        public double HeavyFrame { get; set; }
    }

    public enum FaceGlassesStatus
    {
        [CustomHttpStatus(Code = "SUCCESS", Resources = typeof(ServiceResources), Status = "SUCCESS", StatusCode = HttpStatusCodes.Ok)]
        Successful,

        [CustomHttpStatus(Code = "INVALID_INPUT_ERROR", Resources = typeof(ServiceResources), Status = "INVALID_INPUT_ERROR", StatusCode = HttpStatusCodes.InvalidInput)]
        InvalidInput,

        [CustomHttpStatus(Code = "BAD_REQUEST", Resources = typeof(ServiceResources), Status = "BAD_REQUEST", StatusCode = HttpStatusCodes.BadRequest)]
        BadRequest,

        [CustomHttpStatus(Code = "RESOURCE_NOT_FOUND", Resources = typeof(ServiceResources), Status = "RESOURCE_NOT_FOUND", StatusCode = HttpStatusCodes.ResourceNotFound)]
        NotFound,

        [CustomHttpStatus(Code = "INTERNAL_SERVICE_ERROR", Resources = typeof(ServiceResources), Status = "INTERNAL_SERVICE_ERROR", StatusCode = HttpStatusCodes.InternalServerError)]
        InternalServerError,

        [CustomHttpStatus(Code = "EXTERNAL_SERVICE_ERROR", Resources = typeof(ServiceResources), Status = "EXTERNAL_SERVICE_ERROR", StatusCode = HttpStatusCodes.ServiceUnavailable)]
        ExternalServiceError
    }
}
