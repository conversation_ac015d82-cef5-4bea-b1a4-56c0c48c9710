﻿using Gateway.Biometrics.Application.Appeal.DTO;
using System.Threading.Tasks;
using Gateway.Biometrics.Application.Innovatrics.DTO;

namespace Gateway.Biometrics.Application.Innovatrics
{
    public interface IInnovatricsService
    {
        AnalyseFaceResult AnalyseFace(AnalyseFaceRequest request);
        AnalyseFaceResult AnalyseFace2(AnalyseFaceRequest request);
        Task<AnalyseFaceDisResult> AnalyseFaceDis(AnalyseFaceRequest request);
    }
}
