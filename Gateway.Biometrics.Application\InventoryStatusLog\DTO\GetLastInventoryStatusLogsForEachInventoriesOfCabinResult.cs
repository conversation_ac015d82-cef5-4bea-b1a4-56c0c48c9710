﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.InventoryStatusLog.DTO
{
    public class GetLastInventoryStatusLogsForEachInventoriesOfCabinResult : BaseServiceResult<GetLastInventoryStatusLogsForEachInventoriesOfCabinStatus>
    {
        public LastInventoryStatusLogsOfCabin LastInventoryStatusLogsOfCabin { get; set; }
    }

    public enum GetLastInventoryStatusLogsForEachInventoriesOfCabinStatus
    {
        Successful,
        InvalidInput,
        NotFound,        
    }
}
