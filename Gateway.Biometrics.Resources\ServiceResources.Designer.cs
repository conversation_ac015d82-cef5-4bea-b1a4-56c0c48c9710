﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Gateway.Biometrics.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ServiceResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ServiceResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Gateway.Biometrics.Resources.ServiceResources", typeof(ServiceResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başvuru meta datası bulunamadı.
        /// </summary>
        public static string APPEAL_METADATA_NOT_FOUND {
            get {
                return ResourceManager.GetString("APPEAL_METADATA_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appeal not found.
        /// </summary>
        public static string APPEAL_NOT_FOUND {
            get {
                return ResourceManager.GetString("APPEAL_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch not found.
        /// </summary>
        public static string BRANCH_NOT_FOUND {
            get {
                return ResourceManager.GetString("BRANCH_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Portal and Biometrics branch ids do not match.
        /// </summary>
        public static string BRANCHES_DO_NOT_MATCH {
            get {
                return ResourceManager.GetString("BRANCHES_DO_NOT_MATCH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cabin not found.
        /// </summary>
        public static string CABIN_NOT_FOUND {
            get {
                return ResourceManager.GetString("CABIN_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category not found.
        /// </summary>
        public static string Category_NOT_FOUND {
            get {
                return ResourceManager.GetString("Category_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to City not found.
        /// </summary>
        public static string CITY_NOT_FOUND {
            get {
                return ResourceManager.GetString("CITY_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client configuration not found.
        /// </summary>
        public static string CLIENT_CONFIGURATION_NOT_FOUND {
            get {
                return ResourceManager.GetString("CLIENT_CONFIGURATION_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country not found.
        /// </summary>
        public static string COUNTRY_NOT_FOUND {
            get {
                return ResourceManager.GetString("COUNTRY_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Face hatası.
        /// </summary>
        public static string CREATE_FACE_ERROR {
            get {
                return ResourceManager.GetString("CREATE_FACE_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FAILED.
        /// </summary>
        public static string FAILED {
            get {
                return ResourceManager.GetString("FAILED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HostName not found.
        /// </summary>
        public static string HOST_NAME_NOT_FOUND {
            get {
                return ResourceManager.GetString("HOST_NAME_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to INPUT_ERROR.
        /// </summary>
        public static string INPUT_ERROR {
            get {
                return ResourceManager.GetString("INPUT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to INTERNAL_SERVER_ERROR.
        /// </summary>
        public static string INTERNAL_SERVER_ERROR {
            get {
                return ResourceManager.GetString("INTERNAL_SERVER_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid input error.
        /// </summary>
        public static string INVALID_INPUT_ERROR {
            get {
                return ResourceManager.GetString("INVALID_INPUT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid request.
        /// </summary>
        public static string INVALID_REQUEST {
            get {
                return ResourceManager.GetString("INVALID_REQUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inventory attribute not found.
        /// </summary>
        public static string INVENTORY_ATTRIBUTE_NOT_FOUND {
            get {
                return ResourceManager.GetString("INVENTORY_ATTRIBUTE_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inventory Definition not found.
        /// </summary>
        public static string INVENTORY_DEFINITION_NOT_FOUND {
            get {
                return ResourceManager.GetString("INVENTORY_DEFINITION_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inventory not found.
        /// </summary>
        public static string INVENTORY_NOT_FOUND {
            get {
                return ResourceManager.GetString("INVENTORY_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inventory status log not found.
        /// </summary>
        public static string INVENTORY_STATUS_LOG_NOT_FOUND {
            get {
                return ResourceManager.GetString("INVENTORY_STATUS_LOG_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inventory type not found.
        /// </summary>
        public static string INVENTORY_TYPE_NOT_FOUND {
            get {
                return ResourceManager.GetString("INVENTORY_TYPE_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active directory user account expired.
        /// </summary>
        public static string LDAP_USER_ACCOUNT_EXPIRED {
            get {
                return ResourceManager.GetString("LDAP_USER_ACCOUNT_EXPIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active directory user account locked.
        /// </summary>
        public static string LDAP_USER_ACCOUNT_LOCKED {
            get {
                return ResourceManager.GetString("LDAP_USER_ACCOUNT_LOCKED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active directory user account not found.
        /// </summary>
        public static string LDAP_USER_ACCOUNT_NOT_FOUND {
            get {
                return ResourceManager.GetString("LDAP_USER_ACCOUNT_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active directory user invalid credentials.
        /// </summary>
        public static string LDAP_USER_INVALID_CREDENTIALS {
            get {
                return ResourceManager.GetString("LDAP_USER_INVALID_CREDENTIALS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This method is not valid for this parameter combinations.
        /// </summary>
        public static string METHOD_REQUIREMENT_ERROR {
            get {
                return ResourceManager.GetString("METHOD_REQUIREMENT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Neurotec license not found.
        /// </summary>
        public static string NEUROTEC_LICENSE_NOT_FOUND {
            get {
                return ResourceManager.GetString("NEUROTEC_LICENSE_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Office not found.
        /// </summary>
        public static string OFFICE_NOT_FOUND {
            get {
                return ResourceManager.GetString("OFFICE_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The property {0} can not be more than {1} characters.
        /// </summary>
        public static string PROPERTY_MAX_LENGTH_ERROR {
            get {
                return ResourceManager.GetString("PROPERTY_MAX_LENGTH_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} is required.
        /// </summary>
        public static string PROPERTY_REQUIRED {
            get {
                return ResourceManager.GetString("PROPERTY_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource is already registered.
        /// </summary>
        public static string RESOURCE_ALREADY_REGISTERED {
            get {
                return ResourceManager.GetString("RESOURCE_ALREADY_REGISTERED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource created.
        /// </summary>
        public static string RESOURCE_CREATED {
            get {
                return ResourceManager.GetString("RESOURCE_CREATED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource deleted.
        /// </summary>
        public static string RESOURCE_DELETED {
            get {
                return ResourceManager.GetString("RESOURCE_DELETED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource found.
        /// </summary>
        public static string RESOURCE_FOUND {
            get {
                return ResourceManager.GetString("RESOURCE_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource not found.
        /// </summary>
        public static string RESOURCE_NOT_FOUND {
            get {
                return ResourceManager.GetString("RESOURCE_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource retrieved.
        /// </summary>
        public static string RESOURCE_RETRIEVED {
            get {
                return ResourceManager.GetString("RESOURCE_RETRIEVED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource updated.
        /// </summary>
        public static string RESOURCE_UPDATED {
            get {
                return ResourceManager.GetString("RESOURCE_UPDATED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SUCCESS.
        /// </summary>
        public static string SUCCESS {
            get {
                return ResourceManager.GetString("SUCCESS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User have not branch.
        /// </summary>
        public static string USER_HAVE_NOT_BRANCH {
            get {
                return ResourceManager.GetString("USER_HAVE_NOT_BRANCH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User not authorized.
        /// </summary>
        public static string USER_NOT_AUTHORIZED {
            get {
                return ResourceManager.GetString("USER_NOT_AUTHORIZED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User not found.
        /// </summary>
        public static string USER_NOT_FOUND {
            get {
                return ResourceManager.GetString("USER_NOT_FOUND", resourceCulture);
            }
        }
    }
}
