﻿using Gateway.Cargo.Application.Report.Dto.Request;
using Gateway.Cargo.Application.Report.Dto.Result;
using Gateway.Cargo.Application.Report.Validator;
using Gateway.Cargo.Persistence;
using Gateway.Cargo.Resources;
using Gateway.Validation;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Enums;

using Serilog;
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;
using static Gateway.Cargo.Application.Report.Dto.Result.CourierCheckReportResult.BranchResult;

namespace Gateway.Cargo.Application.Report
{
    public class ReportService : IReportService
    {
        private readonly CargoDbContext _dbContext;
        private readonly IValidationService _validationService;
        private static readonly ILogger Logger = Log.ForContext<ReportService>();

        public ReportService(CargoDbContext dbContext, IValidationService validationService)
        {
            _dbContext = dbContext;
            _validationService = validationService;
        }

        public async Task<GetCargoTrackReportByStatusResponse> GetCargoTrackReportByStatus(BaseReportRequest<ReportRequestByBranches> request)
        {
            var validationResult = _validationService.Validate(typeof(BaseReportValidatorByBranches), request.Request);
            if (!validationResult.IsValid)
                return new GetCargoTrackReportByStatusResponse
                {
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    Status = GetCargoTrackReportStatus.InvalidInput,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var branchTranslation = await _dbContext.BranchTranslation
                        .Where(r => r.IsActive && !r.IsDeleted && request.Request.BranchIds.Contains(r.BranchId))
                    .ToListAsync();

                if (!branchTranslation.Any())
                    return new GetCargoTrackReportByStatusResponse
                    {
                        Message = ServiceResources.BRANCH_TRANSLATION_NOT_FOUND,
                        Status = GetCargoTrackReportStatus.NotFound,
                    };

                var cargoTrack = await _dbContext.Application
                    .If(request.StatusId.HasValue, filter => filter.Where(r => r.CargoTracks.Any(r => r.CargoStatus == request.StatusId)))
                    .Include(i => i.ApplicationExtraFees)
                        .ThenInclude(i => i.ExtraFee)
                    .Include(i => i.Nationality)
                    .Include(i => i.User)
                    .Include(i => i.CargoTracks)
                        .ThenInclude(i => i.User)
                    .Include(i => i.BranchApplicationCountry)
                    .Include(i => i.ApplicationStatusHistories)
                    .Include(i => i.ApplicationCancellations)
                    .Where(r => r.IsActive && !r.IsDeleted && request.Request.BranchIds.Contains(r.BranchApplicationCountry.BranchId) && (r.ApplicationCancellations.Any(q => q.CancellationStatusId == 2 && q.CancellationTypeId == 2 && q.UpdatedAt >= request.Request.EndDate.AddDays(1).Date)
                            || (r.StatusId != 2)) && r.ApplicationStatusHistories.Any(s => s.IsActive && !s.IsDeleted && s.CreatedAt >= request.Request.StartDate && s.CreatedAt < request.Request.EndDate.AddDays(1).Date && s.ApplicationStatusId == 18) &&
                                r.ApplicationExtraFees.Any(e => e.IsActive && !e.IsDeleted && e.IsCancelled.Value != true && !e.IsCancellationStatus && e.ExtraFee.Category == 5))
                    .Select(s => new
                    {
                        BranchId = s.BranchApplicationCountry.BranchId,
						ApplicantRelationalId = s.RelationalApplicationId,
                        Name = s.Name,
                        Surname = s.Surname,
                        Id = s.Id,
                        UserName = s.User.Name,
                        UserSurname = s.User.Surname,
                        ApplicationDate = s.ApplicationTime.Date,
                        BirthDate = s.BirthDate,
                        Nationality = s.Nationality.Name,
                        PassportNumber = s.PassportNumber,
                        CargoTracks = s.CargoTracks,
                         ExtraFeeFlag = s.ApplicationExtraFees.FirstOrDefault(q => q.ExtraFee.Category == 5 && q.IsActive).ExtraFee.FlagId.ToString()
					}).AsNoTracking().ToListAsync();

				var discountedCargoFeeList = new string[] { "055ea762-aa7c-51ac-b0ff-de82af0a99e4", "5c0dbc7a-b72d-5e4d-a199-a8f9e4acffe2", "e08a20a6-8800-5c4c-a996-0c03487caa0a", "d71ef1a3-f17e-5ca6-b355-406d5481c6f9" }; //discounted cargo fee ids

				if (cargoTrack.Exists(p => p.ApplicantRelationalId != null && discountedCargoFeeList.Contains(p.ExtraFeeFlag)))
				{
					var nonMainHasCargoList = cargoTrack.Where(p => p.ApplicantRelationalId != null && discountedCargoFeeList.Contains(p.ExtraFeeFlag)).ToList();

					foreach (var item in nonMainHasCargoList)
					{
						var queryNonMainCargo = await _dbContext.Application
						.Include(i => i.ApplicationExtraFees)
					        .ThenInclude(i => i.ExtraFee)
					    .Include(i => i.Nationality)
					    .Include(i => i.User)
					    .Include(i => i.CargoTracks)
					        .ThenInclude(i => i.User)
					    .Include(i => i.BranchApplicationCountry)
					    .Include(i => i.ApplicationStatusHistories)
					    .Include(i => i.ApplicationCancellations)
					   .Where(r => (r.Id == item.ApplicantRelationalId || r.RelationalApplicationId == item.ApplicantRelationalId) &&
								   r.IsActive && !r.IsDeleted && request.Request.BranchIds.Contains(r.BranchApplicationCountry.BranchId) && (r.ApplicationCancellations.Any(q => q.CancellationStatusId == 2 && q.CancellationTypeId == 2 && q.UpdatedAt >= request.Request.EndDate.AddDays(1).Date)
							|| (r.StatusId != 2)) && r.ApplicationStatusHistories.Any(s => s.IsActive && !s.IsDeleted && s.CreatedAt >= request.Request.StartDate && s.CreatedAt < request.Request.EndDate.AddDays(1).Date && s.ApplicationStatusId == 18) &&
                                r.ApplicationExtraFees.Any(e => e.IsActive && !e.IsDeleted && e.IsCancelled.Value != true && !e.IsCancellationStatus && e.ExtraFee.Category == 5 && e.Quantity > 0))
					   .Select(s => new
					   {
						   BranchId = s.BranchApplicationCountry.BranchId,
						   ApplicantRelationalId = s.RelationalApplicationId,
						   Name = s.Name,
						   Surname = s.Surname,
						   Id = s.Id,
						   UserName = s.User.Name,
						   UserSurname = s.User.Surname,
						   ApplicationDate = s.ApplicationTime.Date,
						   BirthDate = s.BirthDate,
						   Nationality = s.Nationality.Name,
						   PassportNumber = s.PassportNumber,
						   CargoTracks = s.CargoTracks,
                           ExtraFeeFlag = s.ApplicationExtraFees.FirstOrDefault(q => q.ExtraFee.Category == 5 && q.IsActive).ExtraFee.FlagId.ToString()
                       }).ToListAsync();

						cargoTrack.AddRange(queryNonMainCargo);
					}
				}

				var joinedQuery = await (from childApp in _dbContext.Application
										   .Include(i => i.User)
                                           .Include(i => i.Nationality)
                                           .Include(i => i.BranchApplicationCountry)
										   .Include(i => i.ApplicationStatusHistories)
                                           .Include(i => i.CargoTracks)
                                                .ThenInclude(i => i.User)
										 join mainApp in _dbContext.Application
						                    .Include(i => i.ApplicationExtraFees)
							                    .ThenInclude(i => i.ExtraFee)
						                    .Include(i => i.User)
						                    .Include(i => i.CargoTracks)
							                    .ThenInclude(i => i.User)
						                    .Include(i => i.BranchApplicationCountry)
										on childApp.RelationalApplicationId equals mainApp.Id
										 where childApp.StatusId == 1 &&
												childApp.IsActive && !childApp.IsDeleted && mainApp.IsActive && !mainApp.IsDeleted &&
												request.Request.BranchIds.Contains(mainApp.BranchApplicationCountry.BranchId) &&
												!childApp.ApplicationExtraFees.Any(e => e.IsActive && !e.IsDeleted && e.IsCancelled.Value != true && !e.IsCancellationStatus && e.ExtraFee.Category == 5 && e.Quantity > 0) &&
												childApp.ApplicationStatusHistories.Any(q => !q.IsDeleted && q.CreatedAt >= request.Request.StartDate.Date && q.CreatedAt < request.Request.EndDate.AddDays(1).Date && q.ApplicationStatusId == 18) &&
												mainApp.ApplicationExtraFees.Any(e => e.IsActive && !e.IsDeleted && e.IsCancelled.Value != true && !e.IsCancellationStatus && e.ExtraFee.Category == 5 && e.Quantity > 0)
										 select new
										 {
											 BranchId = childApp.BranchApplicationCountry.BranchId,
											 ApplicantRelationalId = childApp.RelationalApplicationId,
											 Name = childApp.Name,
											 Surname = childApp.Surname,
											 Id = childApp.Id,
											 UserName = childApp.User.Name,
											 UserSurname = childApp.User.Surname,
											 ApplicationDate = childApp.ApplicationTime.Date,
											 BirthDate = childApp.BirthDate,
											 Nationality = childApp.Nationality.Name,
											 PassportNumber = childApp.PassportNumber,
											 CargoTracks = childApp.CargoTracks,
                                             ExtraFeeFlag = mainApp.ApplicationExtraFees.FirstOrDefault(q => q.ExtraFee.Category == 5 && q.IsActive).ExtraFee.FlagId.ToString()
                                         }).ToListAsync();

				cargoTrack.AddRange(joinedQuery);

				var resultList = cargoTrack.GroupBy(g => g.BranchId)
                    .Select(trackByBranch => new GetCargoTrackReportByStatusResponse.ResultDto
                    {
                        BranchName = branchTranslation.Find(r => r.BranchId == trackByBranch.Key && r.LanguageId == request.Context.LanguageId) != null
                            ? branchTranslation.Find(r => r.BranchId == trackByBranch.Key && r.LanguageId == request.Context.LanguageId)?.Name
                            : branchTranslation.Find(r => r.BranchId == trackByBranch.Key)?.Name,
                        Data = trackByBranch.Select(s => new GetCargoTrackReportByStatusResponse.ResultDto.DataDto
                        {
                            Name = s.Name,
                            Surname = s.Surname,
                            ApplicationNumber = s.Id.ToString().PadLeft(13, '0'),
                            ApplicationCreatedBy = $"{s.UserName} {s.UserSurname}",
                            ApplicationDate = s.ApplicationDate,
                            BirthDate = s.BirthDate,
                            Nationality = s.Nationality,
                            PassportNumber = s.PassportNumber,
                            BarcodeDate = s.CargoTracks.FirstOrDefault(r => r.IsActive)?.CreatedDate.Date,
                            BarcodeCreatedBy = $"{s.CargoTracks.FirstOrDefault(r => r.IsActive)?.User?.Name} {s.CargoTracks.FirstOrDefault(r => r.IsActive)?.User?.Surname}",
                            Status = s.CargoTracks.FirstOrDefault(r => r.IsActive)?.CargoProviderId != null ?
                                s.CargoTracks.FirstOrDefault(r => r.IsActive)?.Description :
                                string.Empty,
                            CargoProvider = s.CargoTracks.FirstOrDefault(r => r.IsActive)?.CargoProviderId != null ? Extensions.EnumExtensions.GetEnumDescription(typeof(Enums.Enums.CargoProviderType), s.CargoTracks.FirstOrDefault(r => r.IsActive)?.CargoProviderId.ToString()) : string.Empty,
                            TrackingNumber = s.CargoTracks.FirstOrDefault(r => r.IsActive)?.CargoTransactionId
                        }).ToList()
                    }).ToList();

                foreach (var branch in request.Request.BranchIds)
                {
                    var branchName = branchTranslation.Find(r =>
                        r.BranchId == branch && r.LanguageId == request.Context.LanguageId) != null
                        ? branchTranslation.Find(r =>
                            r.BranchId == branch && r.LanguageId == request.Context.LanguageId)?.Name
                        : branchTranslation.Find(r => r.BranchId == branch)?.Name;

                    if (resultList.Select(r => r.BranchName).All(s => s != branchName))
                    {
                        resultList.Add(new GetCargoTrackReportByStatusResponse.ResultDto
                        {
                            Data = new List<GetCargoTrackReportByStatusResponse.ResultDto.DataDto>(),
                            BranchName = branchName
                        });
                    }
                }

                return new GetCargoTrackReportByStatusResponse
                {
                    Message = ServiceResources.SUCCESS,
                    Status = GetCargoTrackReportStatus.Successful,
                    ResultList = resultList
                };
            }
            catch (Exception ex)
            {
                Logger.Error(ex, ex.Message);

                return new GetCargoTrackReportByStatusResponse
                {
                    Message = ServiceResources.INTERNAL_SERVER_ERROR,
                    Status = GetCargoTrackReportStatus.InternalServerError
                };
            }
        }

        public async Task<CourierCheckReportResult> GetCourierCheckReportByStatus(BaseReportRequest<ReportRequestByBranches> request)
        {
            var validationResult = _validationService.Validate(typeof(BaseReportValidatorByBranches), request.Request);
            if (!validationResult.IsValid)
                return new CourierCheckReportResult
                {
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    Status = GetCargoTrackReportStatus.InvalidInput,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var extraFees = await _dbContext.ExtraFee
                        .Include(i => i.ExtraFeeTranslations)
                    .Where(p => p.IsActive && !p.IsDeleted)
                .Select(p => new 
                {
                    Flag = p.FlagId,
                    LanguageId = p.ExtraFeeTranslations.FirstOrDefault(q => q.IsActive && !q.IsDeleted && q.LanguageId == request.Context.LanguageId).LanguageId,
                    Name = p.ExtraFeeTranslations.FirstOrDefault(q => q.IsActive && !q.IsDeleted && q.LanguageId == request.Context.LanguageId).Name
                }).AsNoTracking().ToListAsync();           

            var applications = await _dbContext.Application
                .Include(i => i.Nationality)
                .Include(i => i.ApplicationStatusHistories)
                .Include(i => i.ApplicationExtraFees)
                    .ThenInclude(i => i.ExtraFee)
                .Include(i => i.SapApplicationOrders)
                .Include(i => i.BranchApplicationCountry)
                .Include(i => i.User)
                .Include(i => i.Agency)
            .Where(i => i.IsActive && !i.IsDeleted && request.Request.BranchIds.Contains(i.BranchApplicationCountry.BranchId) &&
                i.StatusId != (int)ApplicationStatus.Cancelled && i.ApplicationTime >= request.Request.StartDate.Date && i.ApplicationTime < request.Request.EndDate.AddDays(1).Date)
            .Select(p => new ApplicationResult
            {
                Id = p.Id,
                ApplicationId = p.Id.ToString(),
                ApplicantTypeId = p.ApplicantTypeId,
                PassportNumber = p.PassportNumber,
                ApplicantName = $"{p.Name} {p.Surname}",
                ApplicantNationalityCode = p.Nationality.ISO2,
                ApplicantBirthDay = p.BirthDate,
                ApplicantAddress = p.Address,
                ApplicantPhone1 = p.PhoneNumber1,
                ApplicantPhone2 = p.PhoneNumber2,
                VisaNo = p.SapApplicationOrders.Where(q => q.IsActive).Select(q => q.GroupId).ToList(),
                RelationalApplicationId = p.RelationalApplicationId,
                ApplicationDate = p.ApplicationTime,
                DeliveredDate = p.ApplicationStatusHistories.Any(q => q.ApplicationStatusId == (int)ApplicationStatusType.OutscanToCourrier)
                ? p.ApplicationStatusHistories.FirstOrDefault(q => q.ApplicationStatusId == (int)ApplicationStatusType.OutscanToCourrier).CreatedAt.ToString("dd/MM/yyyy HH:mm") 
                : null,
                Cargo = p.ApplicationExtraFees.Any(e => e.IsActive && !e.IsDeleted && e.IsCancelled.Value != true && !e.IsCancellationStatus && e.ExtraFee.Category == (int)ExtraFeeCategoryType.Cargo) 
                 ? p.ApplicationExtraFees.Where(q => q.ExtraFee.Category == (int)ExtraFeeCategoryType.Cargo && q.IsActive)
                .Select(q => new CargoInfo
                {
                    ExtraFeeFlag = q.ExtraFee.FlagId.ToString(),
                    Price = q.Price,
                    CurrencyId = q.CurrencyId
                }).FirstOrDefault()
                : null,
                Quantity = p.ApplicationExtraFees.Any(e => e.IsActive && !e.IsDeleted && e.IsCancelled.Value != true && !e.IsCancellationStatus && e.ExtraFee.Category == (int)ExtraFeeCategoryType.Cargo) ? 1 : 0, // because the child apps are included, they want to see individual quantity and prices
                ProcessedBy = $"{p.User.Name} {p.User.Surname}",
                AgencyName = p.Agency != null ? $"{p.Agency.Name} ({p.Agency.CityName})" : "-",
                BranchId = p.BranchApplicationCountry.BranchId
            }).AsNoTracking().ToListAsync();

            var discountedCargoFeeList = new string[] { "055ea762-aa7c-51ac-b0ff-de82af0a99e4" /*Courier in City +3*/, "5c0dbc7a-b72d-5e4d-a199-a8f9e4acffe2" /*Out of City + 3 Courier*/, "e08a20a6-8800-5c4c-a996-0c03487caa0a" /*Out of City + 3  Courier VIP*/, "d71ef1a3-f17e-5ca6-b355-406d5481c6f9" /*Courier in City +3 VIP*/}; //discounted cargo fee ids

            var hasMainCargoApplications = applications.Where(p => p.RelationalApplicationId == null && p.Cargo != null && discountedCargoFeeList.Contains(p.Cargo.ExtraFeeFlag)).Select(p => p.Id).ToList();

            if (hasMainCargoApplications.Any())
            {
                var nonMainApplicationIds = applications.Where(p => hasMainCargoApplications.Contains(p.RelationalApplicationId.GetValueOrDefault())).Select(p => p.Id).ToList();

                foreach (var nonMainApplicationId in nonMainApplicationIds)
                {
                    var application = applications.Find(p => p.Id == nonMainApplicationId);

                    if (application == null) continue;

                    application.Cargo = applications.Find(p => p.Id == application.RelationalApplicationId).Cargo;
                    application.Quantity = applications.Find(p => p.Id == application.RelationalApplicationId).Quantity;
                }
            }

            var hasNonMainCargoApplications = applications.Where(p => p.RelationalApplicationId != null && p.Cargo != null && discountedCargoFeeList.Contains(p.Cargo.ExtraFeeFlag)).Select(p => p.RelationalApplicationId).ToList();

            if (hasNonMainCargoApplications.Any())
            {
                var mainApplicationIds = applications.Where(p => hasNonMainCargoApplications.Contains(p.Id)).Select(p => p.Id).ToList();

                foreach (var mainApplicationId in mainApplicationIds)
                {
                    var mainApplication = applications.Find(p => p.Id == mainApplicationId);

                    if (mainApplication == null) continue;
                 
                    mainApplication.Cargo = applications.Find(p => p.RelationalApplicationId ==
                        mainApplication.Id && p.Cargo != null).Cargo;
                    mainApplication.Quantity = applications.Find(p => p.RelationalApplicationId == mainApplication.Id && p.Cargo != null).Quantity;
                }

                var relationalApplicationIds = applications.Where(p => hasNonMainCargoApplications.Contains(p.RelationalApplicationId.GetValueOrDefault())).Select(p => p.Id).ToList();

                foreach (var relationalApplicationId in relationalApplicationIds)
                {
                    var relationalApplication = applications.Find(p => p.Id == relationalApplicationId);

                    if (relationalApplication == null) continue;

                    relationalApplication.Cargo = applications.Find(p => p.RelationalApplicationId ==
                        relationalApplication.RelationalApplicationId && p.Cargo != null).Cargo;
                    relationalApplication.Quantity = applications.Find(p => p.RelationalApplicationId == relationalApplication.RelationalApplicationId && p.Cargo != null).Quantity;
                }
            }

            var branchApplications = new List<CourierCheckReportResult.BranchResult>();

            for (var b = 0; b < request.Request.BranchIds.Count; b++)
            {
                var branchId = request.Request.BranchIds.ElementAt(b);
                var branchResultApplications = new List<ApplicationResult>();

                foreach (var app in applications.Where(p => p.BranchId == branchId).OrderBy(q => q.ApplicationId).Distinct().ToList())
                {
                    var application = new ApplicationResult
                    {
                        ApplicationId = app.Id.ToApplicationNumber(),
                        ApplicantTypeId = app.ApplicantTypeId,
                        RelationalApplicationId = app.RelationalApplicationId,
                        PassportNumber = app.PassportNumber,
                        ApplicantName = app.ApplicantName,
                        ApplicantNationalityCode = app.ApplicantNationalityCode,
                        ApplicantBirthDay = app.ApplicantBirthDay,
                        ApplicantAddress = app.ApplicantAddress,
                        ApplicantPhone1 = app.ApplicantPhone1,
                        ApplicantPhone2 = app.ApplicantPhone2,
                        VisaNo = app.VisaNo,
                        ApplicationDate = app.ApplicationDate,
                        DeliveredDate = app.DeliveredDate,
                        CargoType = extraFees.Exists(t => t.Flag.ToString() == app.Cargo?.ExtraFeeFlag && t.LanguageId == request.Context.LanguageId) 
                            ? extraFees.Find(t => t.Flag.ToString() == app.Cargo?.ExtraFeeFlag && t.LanguageId == request.Context.LanguageId)?.Name 
                            : extraFees.Find(t => t.Flag.ToString() == app.Cargo?.ExtraFeeFlag)?.Name,
                        Price = app.Cargo?.Price ?? 0,
                        Quantity = app.Quantity,
                        CurrencyId = app.Cargo?.CurrencyId ?? 0,
                        ProcessedBy = app.ProcessedBy,
                        AgencyName = app.AgencyName
                    };

                    branchResultApplications.Add(application);
                }

                var groupedApplications = branchResultApplications.GroupBy(p => p.CargoType).ToList();

                var resultApplications = new List<ApplicationResult>();

                foreach (var group in groupedApplications)
                {
                    foreach (var application in group)
                    {
                        var resultApplication = new ApplicationResult
                        {
                            ApplicationId = application.ApplicationId,
                            RelationalApplicationId = application.RelationalApplicationId,
                            ApplicantTypeId = application.ApplicantTypeId,
                            PassportNumber = application.PassportNumber,
                            ApplicantName = application.ApplicantName,
                            ApplicantNationalityCode = application.ApplicantNationalityCode,
                            ApplicantBirthDay = application.ApplicantBirthDay,
                            ApplicantAddress = application.ApplicantAddress,
                            ApplicantPhone1 = application.ApplicantPhone1,
                            ApplicantPhone2 = application.ApplicantPhone2,
                            VisaNo = application.VisaNo,
                            ApplicationDate = application.ApplicationDate,
                            DeliveredDate = application.DeliveredDate,
                            CargoType = application.CargoType,
                            Price = application.Price,
                            Quantity = application.Quantity,
                            CurrencyId = application.CurrencyId,
                            ProcessedBy = application.ProcessedBy,
                            AgencyName = application.AgencyName
                        };

                        resultApplications.Add(resultApplication);
                    }
                }

                var resultBranchData = new CourierCheckReportResult.BranchResult
                {
                    Applications = resultApplications,
                    TotalApplicationCount = resultApplications.Count,
                    TotalCourierSales = resultApplications.Count(p => p.CargoType != null),
                    NonTotalCourierSales = resultApplications.Count(p => p.CargoType == null),
                    BranchId = branchId
                };

                branchApplications.Add(resultBranchData);
            }

            return new CourierCheckReportResult
            {
                Branches = branchApplications
            };
        }
    }
}
