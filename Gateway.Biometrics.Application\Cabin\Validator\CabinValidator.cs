﻿using System;
using System.Linq;
using FluentValidation;
using Gateway.Extensions;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Entity.Entities.Inventory;
using Gateway.Biometrics.Resources;
using Gateway.Validation;
using Gateway.Biometrics.Application.Cabin.DTO;

namespace Gateway.Biometrics.Application.Cabin.Validator
{
    internal class GetCabinValidator : AbstractValidator<GetCabinRequest>
    {
        public GetCabinValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.ResourceId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.ResourceId)));
            });
        }
    }

    internal class CreateCabinValidator : AbstractValidator<CreateCabinRequest>
    {
        public CreateCabinValidator()
        {
            RuleFor(p => p).Custom((item, context) => {

                if (item.Name.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Name)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.OfficeId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.OfficeId)));
            });


            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Status.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Status)));
            });

        }
    }

    internal class UpdateCabinValidator : AbstractValidator<UpdateCabinRequest>
    {
        public UpdateCabinValidator()
        {
            RuleFor(p => p).Custom((item, context) => {

                if (item.Name.IsNullOrWhitespace())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Name)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.OfficeId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.OfficeId)));
            });


            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Status.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Status)));
            });

        }
    }
    internal class DeleteCabinValidator : AbstractValidator<DeleteCabinRequest>
    {
        public DeleteCabinValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.CabinId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.CabinId)));
            });
        }
    }

    internal class GetPaginatedCabinsValidator : AbstractValidator<GetPaginatedCabinsRequest>
    {
        public GetPaginatedCabinsValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (string.IsNullOrWhiteSpace(item.Pagination.OrderBy))
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.Pagination.OrderBy)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageSize.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageSize)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.Pagination.PageNumber.IsNumericAndGreaterOrEqualZero())
                    context.AddFailure(string.Format(ServiceResources.INVALID_INPUT_ERROR, nameof(item.Pagination.PageNumber)));
            });
        }
    }
}