{"AllowedHosts": "*", "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"GatewayCargoDbConnection": "Server= **********; Database=GatewayPortalDbStage1; Username=gw_portal_stg_app_usr;Password=*****************;"}, "Redis": {"Url": "***********", "Port": "6379", "ConnectTimeout": 10000, "ConnectRetry": 3, "DefaultDatabase": 0}}