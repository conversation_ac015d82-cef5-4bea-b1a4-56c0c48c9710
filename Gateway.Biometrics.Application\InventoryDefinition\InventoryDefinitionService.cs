﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Persistence;
using Gateway.Biometrics.Resources;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Gateway.Core.Pagination;
using Gateway.Extensions;
using Gateway.Biometrics.Application.Lookup;
using Gateway.Biometrics.Application.Inventory.Validator;
using Gateway.Biometrics.Application.InventoryDefinition.DTO;
using Gateway.Biometrics.Application.InventoryDefinition.Validator;
using Gateway.Biometrics.Entity.Entities.Inventory;
using AutoMapper;
using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.Cabin.Validator;
using Gateway.Biometrics.Application.InventoryAttribute.DTO;

namespace Gateway.Biometrics.Application.InventoryDefinition
{
    public class InventoryDefinitionService : IInventoryDefinitionService
    {
        private readonly BiometricsDbContext _dbContext;
        private readonly IValidationService _validationService;
        private readonly IMapper _mapper;

        public InventoryDefinitionService(IValidationService validationService, BiometricsDbContext dbContext, IMapper mapper)
        {
            _validationService = validationService;
            _dbContext = dbContext;
            _mapper = mapper;
        }

        public async Task<GetInventoryDefinitionResult> GetInventoryDefinition(GetInventoryDefinitionRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetInventoryDefinitionValidator), request);

            if (!validationResult.IsValid)
                return new GetInventoryDefinitionResult
                {
                    Status = GetInventoryDefinitionStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var existingInventoryDefinition = await _dbContext.InventoryDefinition.Where(p => p.Id == request.ResourceId && !p.IsDeleted)
                .FirstOrDefaultAsync();

            if (existingInventoryDefinition == null)
                return new GetInventoryDefinitionResult
                {
                    Status = GetInventoryDefinitionStatus.NotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };


            var inventoryType = await _dbContext.InventoryType
                .Where(n =>
                    n.Id == existingInventoryDefinition.InventoryTypeId && !n.IsDeleted)
                .FirstOrDefaultAsync();


            if (inventoryType == null)
                return new GetInventoryDefinitionResult
                {
                    Status = GetInventoryDefinitionStatus.InventoryTypeNotFound,
                    Message = ServiceResources.INVENTORY_TYPE_NOT_FOUND
                };

            existingInventoryDefinition.InventoryValueSets =
                existingInventoryDefinition.InventoryValueSets.Where(n => !n.IsDeleted).ToList();
            var inventoryDefinitionGet = _mapper.Map<InventoryDefinitionDto>(existingInventoryDefinition);

            #region All inventory value sets
            
            var inventoryAttributeListAll = await _dbContext.InventoryAttribute
                .Where(p => !p.IsDeleted)
                .OrderBy(o => o.Id)
                .ToListAsync();

            var inventoryValueSetsAll = inventoryAttributeListAll.Select(n=> new InventoryValueSetDto()
            {
                InventoryAttribute = _mapper.Map<InventoryAttributeDto>(n),
                InventoryAttributeId = n.Id
            }).ToList();

            foreach (var ivs in inventoryValueSetsAll)
            {
                var ivsExisting =
                    inventoryDefinitionGet.InventoryValueSets.FirstOrDefault(n =>
                        n.InventoryAttributeId == ivs.InventoryAttributeId);
                if (ivsExisting != null)
                {
                    ivs.Id = ivsExisting.Id;
                    ivs.Value = ivsExisting.Value;
                }
            }

            inventoryDefinitionGet.InventoryValueSets = _mapper.Map<List<InventoryValueSetDto>>(inventoryValueSetsAll);
            #endregion


            return new GetInventoryDefinitionResult
            {
                Status = GetInventoryDefinitionStatus.Successful,
                Message = ServiceResources.RESOURCE_FOUND,
                InventoryDefinition = inventoryDefinitionGet
            };
        }



        public async Task<CreateInventoryDefinitionResult> CreateInventoryDefinition(CreateInventoryDefinitionRequest request)
        {
            var validationResult = _validationService.Validate(typeof(CreateInventoryDefinitionValidator), request);

            if (!validationResult.IsValid)
                return new CreateInventoryDefinitionResult
                {
                    Status = CreateInventoryDefinitionStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };


            var inventoryTypeCount = _dbContext.InventoryType.Count(t => t.Id == request.InventoryTypeId);

            if (inventoryTypeCount > 1)
            {
                return new CreateInventoryDefinitionResult
                {
                    Status = CreateInventoryDefinitionStatus.InventoryTypeNotFound,
                    Message = ServiceResources.INVENTORY_TYPE_NOT_FOUND
                };
            }

            var newInventoryDefinition = new Entity.Entities.Inventory.InventoryDefinition
            {
                InventoryTypeId = request.InventoryTypeId,
                Description = request.Description,
                //InventoryValueSets = request.InventoryValueSets
            };

            await _dbContext.InventoryDefinition.AddAsync(newInventoryDefinition);
            
            await _dbContext.SaveChangesAsync();

            foreach (var requestInventoryValueSet in request.InventoryValueSets)
            {
                var newInventoryValueSet = new InventoryValueSet()
                {
                    InventoryAttributeId = requestInventoryValueSet.InventoryAttributeId,
                    InventoryDefinitionId = newInventoryDefinition.Id,
                    Value = requestInventoryValueSet.Value
                };
                await _dbContext.InventoryValueSet.AddAsync(newInventoryValueSet);
            }

            await _dbContext.SaveChangesAsync();

            return new CreateInventoryDefinitionResult
            {
                Status = CreateInventoryDefinitionStatus.Successful,
                Message = ServiceResources.RESOURCE_CREATED,
                Id = newInventoryDefinition.Id
            };
        }

        public async Task<UpdateInventoryDefinitionResult> UpdateInventoryDefinition(UpdateInventoryDefinitionRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateInventoryDefinitionValidator), request);

            if (!validationResult.IsValid)
                return new UpdateInventoryDefinitionResult
                {
                    Status = UpdateInventoryDefinitionStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var inventoryDefinitionExisting = await _dbContext.InventoryDefinition
                .Where(q => q.Id == request.InventoryDefinitionId).FirstOrDefaultAsync();

            if (inventoryDefinitionExisting == null)
                return new UpdateInventoryDefinitionResult
                {
                    Status = UpdateInventoryDefinitionStatus.InventoryDefinitionNotFound,
                    Message = ServiceResources.INVENTORY_DEFINITION_NOT_FOUND,
                };


            var inventoryTypeCount = _dbContext.InventoryType.Count(t => t.Id == request.InventoryTypeId);

            if (inventoryTypeCount > 1)
            {
                return new UpdateInventoryDefinitionResult
                {
                    Status = UpdateInventoryDefinitionStatus.InventoryTypeNotFound,
                    Message = ServiceResources.INVENTORY_TYPE_NOT_FOUND
                };
            }

            inventoryDefinitionExisting.InventoryTypeId = request.InventoryTypeId;
            inventoryDefinitionExisting.Description = request.Description;


            inventoryDefinitionExisting.IsDeleted = false;
            inventoryDefinitionExisting.UpdatedAt = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);

            _dbContext.InventoryDefinition.Update(inventoryDefinitionExisting);

            await _dbContext.SaveChangesAsync();

            foreach (var inventoryValueSet in inventoryDefinitionExisting.InventoryValueSets.Where(n=> !n.IsDeleted))
            {
                inventoryValueSet.IsDeleted = true;
                inventoryValueSet.DeletedAt = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
                _dbContext.InventoryValueSet.Update(inventoryValueSet);
            }

            foreach (var inventoryValueSetRequest in request.InventoryValueSets)
            {
                var inventoryValueSetNew = new InventoryValueSet()
                {
                    InventoryAttributeId = inventoryValueSetRequest.InventoryAttributeId,
                    InventoryDefinitionId = inventoryDefinitionExisting.Id,
                    Value = inventoryValueSetRequest.Value
                };

                await _dbContext.InventoryValueSet.AddAsync(inventoryValueSetNew);
            }

            await _dbContext.SaveChangesAsync();

            return new UpdateInventoryDefinitionResult
            {
                Status = UpdateInventoryDefinitionStatus.Successful,
                Message = ServiceResources.RESOURCE_UPDATED,
                Id = inventoryDefinitionExisting.Id
            };
        }

        public async Task<DeleteInventoryDefinitionResult> DeleteInventoryDefinition(DeleteInventoryDefinitionRequest request)
        {
            var validationResult = _validationService.Validate(typeof(DeleteInventoryDefinitionValidator), request);

            if (!validationResult.IsValid)
                return new DeleteInventoryDefinitionResult
                {
                    Status = DeleteInventoryDefinitionStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var inventoryDefinitionExisting = await _dbContext.InventoryDefinition
                .Where(p => !p.IsDeleted && p.Id == request.InventoryDefinitionId).FirstOrDefaultAsync();

            if (inventoryDefinitionExisting == null)
                return new DeleteInventoryDefinitionResult
                {
                    Status = DeleteInventoryDefinitionStatus.ResourceNotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };

            inventoryDefinitionExisting.DeletedAt = DateTime.Now;
            inventoryDefinitionExisting.IsDeleted = true;

            _dbContext.InventoryDefinition.Update(inventoryDefinitionExisting);

            foreach (var inventoryValueSet in inventoryDefinitionExisting.InventoryValueSets)
            {
                inventoryValueSet.DeletedAt = DateTime.Now;
                inventoryValueSet.IsDeleted = true;
                _dbContext.InventoryValueSet.Update(inventoryValueSet);
            }
            
            await _dbContext.SaveChangesAsync();

            return new DeleteInventoryDefinitionResult
            {
                Status = DeleteInventoryDefinitionStatus.Successful,
                Message = ServiceResources.RESOURCE_DELETED
            };
        }

    
        public async Task<GetPaginatedInventoryDefinitionsResult> GetPaginatedInventoryDefinitions(GetPaginatedInventoryDefinitionsRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetPaginatedInventoryDefinitionsValidator), request);

            if (!validationResult.IsValid)
                return new GetPaginatedInventoryDefinitionsResult
                {
                    Status = GetPaginatedInventoryDefinitionsStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages,
                };

            var queryInventoryDefinitions = _dbContext.InventoryDefinition
                .Where(p => !p.IsDeleted);

            if (request.FilterInventoryTypeId.HasValue)
            {
                queryInventoryDefinitions =
                    queryInventoryDefinitions.Where(n => n.InventoryTypeId == request.FilterInventoryTypeId);
            }

            var listInventoryDefinitions = await queryInventoryDefinitions.ToListAsync();

            if (listInventoryDefinitions == null || listInventoryDefinitions.Count == 0)
                return new GetPaginatedInventoryDefinitionsResult
                {
                    Status = GetPaginatedInventoryDefinitionsStatus.ResourceNotFound,
                    Message = ServiceResources.INVENTORY_NOT_FOUND
                };

            var result = new GetPaginatedInventoryDefinitionsResult
            {
                InventoryDefinitions = listInventoryDefinitions.Select(n => _mapper.Map<InventoryDefinitionDto>(n))
                    .ToList()
            };

            var paginationResult = PagedResultsFactory.CreatePagedResult(
                result.InventoryDefinitions.AsQueryable(), request.Pagination.PageNumber, request.Pagination.PageSize,
                request.Pagination.OrderBy, request.Pagination.Ascending);

            return paginationResult == null
                ? new GetPaginatedInventoryDefinitionsResult
                {
                    InventoryDefinitions = null, Status = GetPaginatedInventoryDefinitionsStatus.ResourceNotFound,
                    Message = ServiceResources.INVALID_INPUT_ERROR
                }
                : new GetPaginatedInventoryDefinitionsResult
                {
                    InventoryDefinitions = paginationResult.Results, 
                    Status = GetPaginatedInventoryDefinitionsStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    TotalNumberOfPages = paginationResult.TotalNumberOfPages,
                    TotalNumberOfRecords = paginationResult.TotalNumberOfRecords,
                };
        }

      
        private static LookupValue GetLookupValue(int enumType, int? value)
        {
            return EnumExtensions.GetEnumAsDictionary(LookupTypeFactory.GetInstance(enumType))
                .Select(x => new LookupValue { Id = x.Key.ToString(), DisplayValue = x.Value })
                .FirstOrDefault(p => p.Id == value.ToString());
        }
    }
}