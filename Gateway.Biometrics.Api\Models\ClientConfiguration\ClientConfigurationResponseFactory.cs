﻿using Gateway.Biometrics.Application.Cabin.DTO;
using Gateway.Biometrics.Application.ClientConfiguration.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.Biometrics.Api.Models.ClientConfiguration
{
    public static class ClientConfigurationResponseFactory
    {
        public static ObjectResult GetClientConfigurationByHostName(GetClientConfigurationByHostNameResult result)
        {
            switch (result.Status)
            {
                case GetClientConfigurationByNameStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.ClientConfiguration
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case GetClientConfigurationByNameStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case GetClientConfigurationByNameStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }


        public static ObjectResult GetClientConfigurationResponse(GetClientConfigurationResult result)
        {
            switch (result.Status)
            {
                case GetClientConfigurationStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.ClientConfiguration,
                    })
                    { StatusCode = HttpStatusCodes.Created };
                case GetClientConfigurationStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };

                case GetClientConfigurationStatus.NotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.CLIENT_CONFIGURATION_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.CLIENT_CONFIGURATION_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case GetClientConfigurationStatus.OfficeNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.OFFICE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.OFFICE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }


        public static ObjectResult CreateClientConfigurationResponse(CreateClientConfigurationResult result)
        {
            switch (result.Status)
            {
                case CreateClientConfigurationStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_CREATED),
                        Message = result.Message,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.Created };
                case CreateClientConfigurationStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case CreateClientConfigurationStatus.OfficeNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.OFFICE_NOT_FOUND,
                            Code = Resource.GetKey(ServiceResources.OFFICE_NOT_FOUND),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                case CreateClientConfigurationStatus.CabinNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.CABIN_NOT_FOUND,
                            Code = Resource.GetKey(ServiceResources.CABIN_NOT_FOUND),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                case CreateClientConfigurationStatus.CountryNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.COUNTRY_NOT_FOUND,
                            Code = Resource.GetKey(ServiceResources.COUNTRY_NOT_FOUND),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                case CreateClientConfigurationStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult UpdateClientConfigurationResponse(UpdateClientConfigurationResult result)
        {
            switch (result.Status)
            {
                case UpdateClientConfigurationStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_UPDATED),
                        Message = result.Message,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case UpdateClientConfigurationStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case UpdateClientConfigurationStatus.ClientConfigurationNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.CLIENT_CONFIGURATION_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.CLIENT_CONFIGURATION_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateClientConfigurationStatus.OfficeNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.OFFICE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.OFFICE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateClientConfigurationStatus.CabinNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.CABIN_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.CABIN_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateClientConfigurationStatus.CountryNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.COUNTRY_NOT_FOUND,
                            Code = Resource.GetKey(ServiceResources.COUNTRY_NOT_FOUND),
                            Message = result.Message
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                case UpdateClientConfigurationStatus.ResourceExists:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_ALREADY_REGISTERED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceExist };
                case UpdateClientConfigurationStatus.NeurotecLicenseNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.NEUROTEC_LICENSE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.NEUROTEC_LICENSE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult DeleteClientConfigurationResponse(DeleteClientConfigurationResult result)
        {
            switch (result.Status)
            {
                case DeleteClientConfigurationStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_DELETED),
                        Message = result.Message,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case DeleteClientConfigurationStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case DeleteClientConfigurationStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult GetPaginatedClientConfigurationsResponse(GetPaginatedClientConfigurationsResult result)
        {
            switch (result.Status)
            {
                case GetPaginatedClientConfigurationsStatus.Successful:
                    return new ObjectResult(new BasePaginationApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.ClientConfigurations,
                        TotalNumberOfRecords = result.TotalNumberOfRecords,
                        TotalNumberOfPages = result.TotalNumberOfPages
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case GetPaginatedClientConfigurationsStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case GetPaginatedClientConfigurationsStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

    }
}
