﻿using System;
using System.Collections.Generic;
using System.Text;
using Gateway.Biometrics.Core;

namespace Gateway.Biometrics.Application.Inventory.DTO
{
    public class UpdateInventoryResult : BaseServiceResult<UpdateInventoryStatus>
    {
        public int Id { get; set; }
    }

    public enum UpdateInventoryStatus
    {
        Successful,
        ResourceExists,
        InvalidInput,
        InventoryNotFound,
        BranchNotFound,
        InventoryDefinitionNotFound
    }
}
