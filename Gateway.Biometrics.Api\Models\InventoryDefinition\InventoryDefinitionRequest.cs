﻿using System.Collections.Generic;
using System;
using Gateway.Biometrics.Application.InventoryDefinition.DTO;
using Gateway.Core.Pagination;

namespace Gateway.Biometrics.Api.Models.InventoryDefinition
{
    public class BaseInventoryDefinitionRequestModel
    {
        public int InventoryTypeId { get; set; }
        public string Description { get; set; }
        public List<InventoryValueSetDto> InventoryValueSets { get; set; }

        public BaseInventoryDefinitionRequestModel()
        {
        }
    }
    
    public class CreateInventoryDefinitionRequestModel : BaseInventoryDefinitionRequestModel
    {
    }

    public class UpdateInventoryDefinitionRequestModel : BaseInventoryDefinitionRequestModel
    {
    }

    public class DeleteInventoryDefinitionRequestModel
    {
        public int InventoryDefinitionId { get; set; }
    }
    
    public class GetPaginatedInventoryDefinitionsRequestModel
    {
        public int? FilterInventoryTypeId { get; set; }
        public PaginationRequest Pagination { get; set; }
    }
}