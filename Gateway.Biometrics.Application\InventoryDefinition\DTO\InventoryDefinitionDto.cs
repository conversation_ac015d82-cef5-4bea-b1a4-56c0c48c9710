﻿using Gateway.Biometrics.Application.Inventory.DTO;
using Gateway.Biometrics.Entity.Entities.Inventory;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Gateway.Biometrics.Application.InventoryDefinition.DTO
{
    public class InventoryDefinitionDto
    {
        public int Id { get; set; }
        public int InventoryTypeId { get; set; }
        public string Description { get; set; }
        public virtual InventoryTypeDto InventoryType { get; set; }
        public virtual List<InventoryValueSetDto> InventoryValueSets { get; set; }
    }
}
