﻿using System.Threading.Tasks;
using Gateway.Cargo.Application.Report.Dto.Request;
using Gateway.Cargo.Application.Report.Dto.Result;

namespace Gateway.Cargo.Application.Report
{
    public interface IReportService
    {
        public Task<GetCargoTrackReportByStatusResponse> GetCargoTrackReportByStatus(BaseReportRequest<ReportRequestByBranches> request);
        public Task<CourierCheckReportResult> GetCourierCheckReportByStatus(BaseReportRequest<ReportRequestByBranches> request);
    }
}
