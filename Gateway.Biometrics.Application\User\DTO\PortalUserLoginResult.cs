﻿namespace Gateway.Biometrics.Application.User.DTO
{
    public class PortalUserLoginResult : BaseServiceResult<PortalUserLoginStatus>
    {
        public BiometricsUserDto User { get; set; }
    }
    
      
    public enum PortalUserLoginStatus
    {
        Successful,
        InvalidInput,
        ResourceNotFound,
        UserNotFound,
        UserHaveNotBranch,
        BranchesDoNotMatch,
        HostNameNotFound,
        UserNotAuthorized
    }
}
