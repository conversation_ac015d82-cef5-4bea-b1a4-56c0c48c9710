﻿using Gateway.Biometrics.Entity.Entities.Inventory;
using Gateway.Biometrics.Entity.Enum;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gateway.Biometrics.Entity.Entities.Category
{
    public class GenericAttribute : BaseEntity
    {
        public int? ParentId { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string Options { get; set; }

        public GenericAttributeTypeEnum GenericAttributeTypeEnumId { get; set; }
    }
}
