﻿using System.Collections.Generic;

namespace Gateway.Biometrics.Entity.Entities.Cabin
{
    public class Cabin : BaseEntity
    {
        public string Name { get; set; }
        public int OfficeId { get; set; }
        public int Status { get; set; }
        public bool IsBiometricCabin { get; set; }
        public virtual Office.Office Office { get; set; }
        public virtual IEnumerable<Inventory.Inventory> Inventories { get; set; }
    }
}
