﻿using System.Threading.Tasks;
using AutoMapper;
using Gateway.External.Api.Models;
using Gateway.External.Api.Models.Slot;
using Gateway.External.Application.Slot;
using Gateway.External.Application.Slot.Dto;
using Gateway.External.Core.Context;
using Gateway.External.Resources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Gateway.External.Api.Controllers
{
    [Authorize]
    [Route("api")]
    [ApiController]
    public class SlotController : Controller
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private readonly ISlotService _slotService;

        #region ctor

        public SlotController(IContext context, IMapper mapper, ISlotService slotService)
        {
            _context = context;
            _mapper = mapper;
            _slotService = slotService;
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// Gets slots with given request
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Gets slots with given request", 
            Description = "Gets slots with given request")]
        [HttpPost]
        [Route("slots")]
        public async Task<IActionResult> GetSlots(GetSlotsRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetSlotsRequest>(request);
            serviceRequest.Context = _context;

            var result = await _slotService.GetSlots(serviceRequest);

            return SlotResponseFactory.GetSlotsResponse(result);
        }

        #endregion
	}
}