﻿using Gateway.Biometrics.Application.User.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;
using Nest;

namespace Gateway.Biometrics.Api.Models.User
{
    public static class UserResponseFactory
    {

        public static ObjectResult PortalUserLoginResponse(PortalUserLoginResult result)
        {
            switch (result.Status)
            {
                case PortalUserLoginStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.User
                    })
                    { StatusCode = HttpStatusCodes.Created };
                case PortalUserLoginStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case PortalUserLoginStatus.ResourceNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.RESOURCE_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case PortalUserLoginStatus.UserNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.USER_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.USER_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case PortalUserLoginStatus.UserNotAuthorized:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.USER_NOT_AUTHORIZED,
                        Code = Resource.GetKey(ServiceResources.USER_NOT_AUTHORIZED),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.Unauthorized };
                case PortalUserLoginStatus.UserHaveNotBranch:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.USER_HAVE_NOT_BRANCH,
                        Code = Resource.GetKey(ServiceResources.USER_HAVE_NOT_BRANCH),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.Unauthorized };
                case PortalUserLoginStatus.BranchesDoNotMatch:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.BRANCHES_DO_NOT_MATCH,
                        Code = Resource.GetKey(ServiceResources.BRANCHES_DO_NOT_MATCH),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.Unauthorized };
                case PortalUserLoginStatus.HostNameNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.HOST_NAME_NOT_FOUND,
                        Code = Resource.GetKey(ServiceResources.HOST_NAME_NOT_FOUND),
                        Message = result.Message
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult IsServerReachableResponce()
        {
            return new ObjectResult(new BaseApiResponse
            {
                Status = ServiceResources.SUCCESS,
                Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                Message = null,                
            })
            { StatusCode = HttpStatusCodes.Ok };
        }


    }
}