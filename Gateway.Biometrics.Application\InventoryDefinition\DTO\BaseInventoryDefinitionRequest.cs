﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Gateway.Biometrics.Entity.Entities.Inventory;

namespace Gateway.Biometrics.Application.InventoryDefinition.DTO
{
    public class BaseInventoryDefinitionRequest : BaseServiceRequest
    {
        [Required]
        public int InventoryTypeId { get; set; }
        public string Description { get; set; }
        
        public List<InventoryValueSet> InventoryValueSets { get; set; }
        public BaseInventoryDefinitionRequest()
        {

        }
    }


}

