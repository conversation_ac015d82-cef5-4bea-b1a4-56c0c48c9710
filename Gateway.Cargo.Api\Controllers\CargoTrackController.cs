﻿using AutoMapper;
using Gateway.Cargo.Api.Models.Request;
using Gateway.Cargo.Application.CargoTrack;
using Gateway.Cargo.Application.CargoTrack.Dto.Request;
using Gateway.Cargo.Application.Shipment;
using Gateway.Cargo.Application.Status;
using Gateway.Cargo.Core.Context;
using Gateway.Cargo.Dto.Request;
using Gateway.Cargo.Resources;
using Gateway.Core.Responses;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using UpdateStatusRequest = Gateway.Cargo.Application.Status.Dto.Request.UpdateStatusRequest;

namespace Gateway.Cargo.Api.Controllers
{
    [Route("api")]
    [ApiController]
    public class CargoTrackController : Controller
    {
        private readonly IShipmentService _shipmentService;
        private readonly ICargoTrackService _cargoTrackService;
        private readonly ICargoStatusService _cargoStatusService;
        private readonly IContext _context;
        private readonly IMapper _mapper;

        public CargoTrackController(IContext context, IMapper mapper, IShipmentService shipmentService, ICargoTrackService cargoTrackService, ICargoStatusService cargoStatusService)
        {
            _mapper = mapper;
            _context = context;
            _shipmentService = shipmentService;
            _cargoTrackService = cargoTrackService;
            _cargoStatusService = cargoStatusService;
        }

        [HttpGet]
        [Route("Cargo/ListCargoToCheckStatus/{cargoProviderId?}")]
        public async Task<IActionResult> ListCargoToCheckStatus(int? cargoProviderId)
        {
            var result = await _cargoTrackService.ListCargoToCheckStatus(cargoProviderId);

            return BaseResponseFactory.CreateResponse(result, result.CargoTrackList);
        }

        [HttpPost]
        [Route("Cargo/Shipment")]
        public async Task<IActionResult> Shipment(ShipmentRequestModel requestModel)
        {
            if (requestModel == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST) ?? string.Empty,
                    ServiceResources.INVALID_REQUEST));

            var request = _mapper.Map<ShipmentRequest>(requestModel);
            request.Context = _context;

            var result = await _shipmentService.Shipment(request, requestModel.ApplicationId);

            return BaseResponseFactory.CreateResponse(result, result.ShipmentResult);
        }

        [HttpGet]
        [Route("Cargo/Shipments/LabelRecovery/{applicationId?}")]
        public async Task<IActionResult> LabelRecovery(int applicationId)
        {
            if (applicationId == 0)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST) ?? string.Empty,
                    ServiceResources.INVALID_REQUEST));

            var request = new LabelRecoveryServiceRequest
            {
                ApplicationId = applicationId,
                Context = _context
            };

            request.Context = _context;

            var result = await _shipmentService.LabelRecovery(request);

            return BaseResponseFactory.CreateResponse(result, result.ShipmentResult);
        }

        [HttpGet]
        [Route("Cargo/Track/{applicationId?}")]
        public async Task<IActionResult> CargoTracking(int applicationId)
        {
            if (applicationId == 0)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST) ?? string.Empty,
                    ServiceResources.INVALID_REQUEST));

            var request = new TrackingRequest
            {
                ApplicationId = applicationId,
                Context = _context
            };

            var result = await _cargoTrackService.Track(request);

            return BaseResponseFactory.CreateResponse(result, result.CargoTrackingResult);
        }

        [HttpGet]
        [Route("Cargo/Area/{governorateId?}")]
        public async Task<IActionResult> GetCargoArea(string governorateId)
        {
            var request = new GetAreaRequest
            {
                GovernorateId = governorateId,
                Context = _context
            };

            var result = await _cargoTrackService.GetArea(request);

            return BaseResponseFactory.CreateResponse(result, result.AreaList);
        }

        [HttpGet]
        [Route("Cargo/Governorate")]
        public async Task<IActionResult> GetCargoGovernorate()
        {
            var request = new GetGovernorateRequest
            {
                Context = _context
            };

            var result = await _cargoTrackService.GetGovernorate(request);

            return BaseResponseFactory.CreateResponse(result, result.GovernorateList);
        }

        [HttpPatch]
        [Route("Cargo/UpdateStatus")]
        public async Task<IActionResult> UpdateCargoStatus(UpdateStatusRequestModel requestModel)
        {
            if (requestModel == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST) ?? string.Empty,
                    ServiceResources.INVALID_REQUEST));


            var request = _mapper.Map<UpdateStatusRequest>(requestModel);
            request.Context = _context;

            var result = await _cargoStatusService.UpdateStatus(request);

            return BaseResponseFactory.CreateResponse(result, result.Data);
        }
    }
}
