﻿using AutoMapper;
using Gateway.Biometrics.Api.Models;
using Gateway.Biometrics.Core.Context;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;
using Gateway.Biometrics.Api.Models.Office;
using Gateway.Biometrics.Application.Office;
using Gateway.Biometrics.Application.Office.DTO;
using Gateway.Extensions;
using Gateway.Biometrics.Api.Models.Cabin;
using Gateway.Biometrics.Application.Cabin.DTO;

namespace Gateway.Biometrics.Api.Controllers
{
    //[Authorize]
    [Route("api")]
    [ApiController]
    public class OfficeController : Controller
    {
        private readonly IContext _context;
        private readonly IMapper _mapper;
        private readonly IOfficeService _officeService;
        
        #region ctor

        public OfficeController(IContext context, IMapper mapper, IOfficeService officeService
        )
        {
            _context = context;
            _mapper = mapper;
            _officeService = officeService;
        }

        #endregion

        #region Public Methods


        /// <summary>
        /// Get Office by id
        /// </summary>
        /// <param name="resourceId"></param>
        [HttpGet]
        [Route("Offices/get/{resourceId?}")]
        public async Task<IActionResult> GetOffice(int resourceId)
        {
            var serviceRequest = new GetOfficeRequest()
            {
                Context = _context,
                ResourceId = resourceId
            };

            var result = await _officeService.GetOffice(serviceRequest);

            return OfficeResponseFactory.GetOfficeResponse(result);
        }


        /// <summary>
        /// Creates a new office
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Creates a new office", 
            Description = "Creates a new inventory")]
        [HttpPost]
        [Route("Offices/create")]
        public async Task<IActionResult> CreateOffice(CreateOfficeRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<CreateOfficeRequest>(request);
            serviceRequest.Context = _context;

            var result = await _officeService.CreateOffice(serviceRequest);

            return OfficeResponseFactory.CreateOfficeResponse(result);
        }

        /// <summary>
        /// Delete an existing office
        /// </summary>
        /// <param name="resourceId"></param>  
        [SwaggerOperation(Summary = "Delete an existing office", 
            Description = "Delete an existing office")]
        [HttpDelete]
        [Route("Offices/delete/{resourceId?}")]
        public async Task<IActionResult> DeleteOffice(int resourceId)
        {
            if (!resourceId.IsNumericAndGreaterThenZero())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = new DeleteOfficeRequest
            {
                OfficeId = resourceId,
                Context = _context
            };

            var result = await _officeService.DeleteOffice(serviceRequest);

            return OfficeResponseFactory.DeleteOfficeResponse(result);
        }



        /// <summary>
        /// Get paginated list of offices
        /// </summary>
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Get paginated list of Offices", 
            Description = "Get paginated list of Offices")]
        [HttpPost]
        [Route("Offices/search")]
        public async Task<IActionResult> GetPaginatedOffices(GetPaginatedOfficesRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetPaginatedOfficesRequest>(request);
            serviceRequest.Context = _context;

            var result = await _officeService.GetPaginatedOffices(serviceRequest);

            return OfficeResponseFactory.GetPaginatedOfficesResponse(result);
        }

        /// <summary>
        /// Update selected office
        /// </summary>
        /// <param name="resourceId"></param>  
        /// <param name="request"></param>  
        [SwaggerOperation(Summary = "Update an existing office",
            Description = "Update an existed office")]
        [HttpPut]
        [Route("Offices/update/{resourceId?}")]
        public async Task<IActionResult> UpdateOffice(int resourceId, UpdateOfficeRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<UpdateOfficeRequest>(request);
            serviceRequest.OfficeId = resourceId;
            serviceRequest.Context = _context;

            var result = await _officeService.UpdateOffice(serviceRequest);

            return OfficeResponseFactory.UpdateOfficeResponse(result);
        }

        #endregion
    }
}