﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Application.Appeal.DTO
{
    public class AppealDto
    {
        public int Id { get; set; }        

        public string PassportNumber { get; set; }

        public string ReferenceNumber { get; set; }

        public string Name { get; set; }

        public string Surname { get; set; }

        public int Gender { get; set; }

        public string MotherName { get; set; }

        public string FatherName { get; set; }

        public DateTime BirthDate { get; set; }

        public string MaidenName { get; set; }

        public int AppealCountryId { get; set; }

        public string AppealCountryName { get; set; }      

        public int AppealCityId { get; set; }

        public string AppealCityName { get; set; }

        public int AppealOfficeId { get; set; }

        public string AppealOfficeName { get; set; }

        public int AppealCabinId { get; set; }

        public string AppealCabinName { get; set; }

        public int Status { get; set; }

        public DateTime TakenDate { get; set; }

        public string HostName { get; set; }

        public DateTime CreatedAt { get; set; }    
        
        public DateTime? UpdatedAt { get; set; }

        public List<AppealMetaDataDto> MetaDatas { get; set; }
    }
}
