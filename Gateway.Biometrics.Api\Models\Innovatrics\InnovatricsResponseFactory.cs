﻿using Gateway.Biometrics.Application.Innovatrics.DTO;
using Gateway.Biometrics.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.Biometrics.Api.Models.Innovatrics
{
    public static class InnovatricsResponseFactory
    {
        public static ObjectResult AnalyseFaceResponse(AnalyseFaceResult result)
        {
            switch (result.Status)
            {
                case AnalyseFaceStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.IFaceData
                    })
                    { StatusCode = HttpStatusCodes.Created };
                    
                case AnalyseFaceStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult AnalyseFace2Response(AnalyseFaceResult result)
        {
            switch (result.Status)
            {
                case AnalyseFaceStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.Created };

                case AnalyseFaceStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult AnalyseFaceDisResponse(AnalyseFaceDisResult result)
        {
            switch (result.Status)
            {
                case AnalyseFaceDisResultStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.Created };

                case AnalyseFaceDisResultStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case AnalyseFaceDisResultStatus.CreateFaceError:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.CREATE_FACE_ERROR,
                        Code = Resource.GetKey(ServiceResources.CREATE_FACE_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED,
                        Data = result
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

    }
}
