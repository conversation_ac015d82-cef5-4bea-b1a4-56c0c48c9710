<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Innovatrics.IFace</name>
    </assembly>
    <members>
        <member name="T:Innovatrics.IFace.AgeGenderSpeedAccuracyMode">
            <summary>
            Parameter defining age and gender evaluation mode. It represents trade-off between age and gender evaluation speed and precision of the age and gender predictions. <para/><i>Value type</i>: String, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.AgeGenderSpeedAccuracyMode.Accurate">
            <summary>
            Precision of age and gender predictions is the best. However the performance of the age and gender evaluation is not as good as when <c>fast</c> mode is used.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.AgeGenderSpeedAccuracyMode.Fast">
            <summary>
            The evaluation of the age and gender is the fastest. However precision of these predictions is not as good as when <c>accurate</c> mode is used.
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.ConditionString">
            <summary>
            Default / Recommended conditions for ent
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.ConditionString.IFaceConditionsIcaoAll">
            <summary>
            Default value of reliability dependencies for checking of all ICAO attributes Can be used as parameter of <c><see cref="M:Innovatrics.IFace.Face.EvaluateConditions(System.String,Innovatrics.IFace.FaceHandler)"/></c>
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.ConditionString.IFaceConditionsIcaoTiny">
            <summary>
            Default value of reliability dependencies for checking of ICAO attributes that can be evaluated in IFace Tiny edition Can be used as parameter of <c><see cref="M:Innovatrics.IFace.Face.EvaluateConditions(System.String,Innovatrics.IFace.FaceHandler)"/></c>
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.ConditionString.IFaceConditionsPassiveLiveness">
            <summary>
            Default value of reliability dependencies for passive liveness estimation Can be used as parameter of <c><see cref="M:Innovatrics.IFace.Face.EvaluateConditions(System.String,Innovatrics.IFace.FaceHandler)"/></c> - conditionString <c><see cref="F:Innovatrics.IFace.ConditionString.IFaceConditionsPassiveLiveness"/></c>
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.ConditionString.IFaceConditionsTemplateExtraction">
            <summary>
            Default value of reliability dependencies for template extraction Can be used as parameter of <c><see cref="M:Innovatrics.IFace.Face.EvaluateConditions(System.String,Innovatrics.IFace.FaceHandler)"/></c> - conditionString <c><see cref="F:Innovatrics.IFace.ConditionString.IFaceConditionsTemplateExtraction"/></c>
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.ConditionString.IFaceConditionsAge">
            <summary>
            Default value of reliability dependencies for age estimation Can be used as parameter of <c><see cref="M:Innovatrics.IFace.Face.EvaluateConditions(System.String,Innovatrics.IFace.FaceHandler)"/></c> - conditionString <c><see cref="F:Innovatrics.IFace.ConditionString.IFaceConditionsAge"/></c>
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.ConditionString.IFaceConditionsGender">
            <summary>
            Default value of reliability dependencies for gender estimation Can be used as parameter of <c><see cref="M:Innovatrics.IFace.Face.EvaluateConditions(System.String,Innovatrics.IFace.FaceHandler)"/></c> - conditionString <c><see cref="F:Innovatrics.IFace.ConditionString.IFaceConditionsGender"/></c>
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.ConditionString.IFaceConditionsGlassStatus">
            <summary>
            Default value of reliability dependencies for glass status Can be used as parameter of <c><see cref="M:Innovatrics.IFace.Face.EvaluateConditions(System.String,Innovatrics.IFace.FaceHandler)"/></c> - conditionString <c><see cref="F:Innovatrics.IFace.ConditionString.IFaceConditionsGlassStatus"/></c>
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.ConditionString.IFaceConditionsSegmentationMask">
            <summary>
            Default value of reliability dependencies for segmentation mask estimation Can be used as parameter of <c><see cref="M:Innovatrics.IFace.Face.EvaluateConditions(System.String,Innovatrics.IFace.FaceHandler)"/></c> - conditionString <c><see cref="F:Innovatrics.IFace.ConditionString.IFaceConditionsSegmentationMask"/></c>
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.ConditionString.IFaceConditionsCrop">
            <summary>
            Default value of reliability dependencies for face cropping rectangle estimation Can be used as parameter of <c><see cref="M:Innovatrics.IFace.Face.EvaluateConditions(System.String,Innovatrics.IFace.FaceHandler)"/></c> - conditionString <c><see cref="F:Innovatrics.IFace.ConditionString.IFaceConditionsCrop"/></c>
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.ConditionString.IFaceConditionsFaceSize">
            <summary>
            Default value of reliability dependencies for eye distance estimation Can be used as parameter of <c><see cref="M:Innovatrics.IFace.Face.EvaluateConditions(System.String,Innovatrics.IFace.FaceHandler)"/></c> - conditionString <c><see cref="F:Innovatrics.IFace.ConditionString.IFaceConditionsFaceSize"/></c>
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.FaceAttributeDependenciesStatus">
            <summary>
            Possible results of face attribute dependencies evaluation. Each face attribute can have its own dependencies (it is dependent on some other face attributes). Once these dependencies are fulfilled then the result of the face attribute evaluation can be taken as valid. This enum is deprecated and will be removed in iface 4.0 release
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeDependenciesStatus.NotOk">
            <summary>
            Face attribute dependencies are not fulfilled, so value of the face attribute can't be trusted.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeDependenciesStatus.Ok">
            <summary>
            Face attribute dependencies are fulfilled, so value of the face attribute can be trusted
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.FaceAttributeId">
            <summary>
            Face attribute IDs for various face processing operations - ICAO features (e.g. mouth status, eyes_status), face recognition (age, gender, verification template). Functions <c><see cref="M:Innovatrics.IFace.Face.TryGetFaceAttribute(System.Single@,Innovatrics.IFace.FaceAttributeId,Innovatrics.IFace.IFaceExceptionCode[],Innovatrics.IFace.FaceHandler)"/></c>, <c><see cref="M:Innovatrics.IFace.Face.TryGetFaceAttribute(System.Single@,Innovatrics.IFace.FaceAttributeId,Innovatrics.IFace.IFaceExceptionCode[],Innovatrics.IFace.FaceHandler)"/>Raw</c>, <c><see cref="M:Innovatrics.IFace.Face.TryGetFaceAttribute(System.Single@,Innovatrics.IFace.FaceAttributeId,Innovatrics.IFace.IFaceExceptionCode[],Innovatrics.IFace.FaceHandler)"/>DependenciesStatus</c> <c><see cref="M:Innovatrics.IFace.Face.TryGetFaceAttribute(System.Single@,Innovatrics.IFace.FaceAttributeId,Innovatrics.IFace.IFaceExceptionCode[],Innovatrics.IFace.FaceHandler)"/>RangeStatus</c> and face attribute conditions parameters are related to evaluation of these features.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.Sharpness">
            <summary>
            Face attribute for evaluating whether an area of face image is not blurred. The attribute can be taken as an ICAO feature. Sharpness values are from range &lt;-10000,10000&gt;.  Values near -10000 indicates 'very blurred', values near 10000 indicates 'very sharp'. The decision threshold is around 0.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.Brightness">
            <summary>
            Face attribute for evaluating whether an area of face is correctly exposed. The attribute can be taken as an ICAO feature. Brightness values are from range &lt;-10000,10000&gt;. Values near -10000 indicates 'too dark', values near 10000 indicates 'too light', values around 0 indicates OK. The decision thresholds are around -5000 and 5000.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.Contrast">
            <summary>
            Face attribute for evaluating whether an area of face is contrast enough. The attribute can be taken as an ICAO feature. Contrast values are from range &lt;-10000,10000&gt;. Values near -10000 indicates 'very low contrast', values near 10000 indicates 'very high contrast', values around 0 indicates OK. The decision thresholds are around -5000 and 5000.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.UniqueIntensityLevels">
            <summary>
            Face attribute for evaluating whether an area of face has appropriate number of unique intensity levels. The attribute can be taken as an ICAO feature. Unique intensity levels values are from range &lt;-10000,10000&gt;. Values near -10000 indicates 'very few unique intensity levels', values near 10000 indicates 'enough unique intensity levels'. The decision threshold is around 0.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.Shadow">
            <summary>
            Face attribute for evaluating whether an area of face is not overshadowed. The attribute can be taken as an ICAO feature. Shadow values are from range &lt;-10000,10000&gt;. Values near -10000 indicates 'very strong global shadows present', values near 10000 indicates 'no global shadows present'. The decision threshold is around 0.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.NoseShadow">
            <summary>
            Face attribute for evaluating whether eyes or a nose don't cast sharp shadows. The attribute can be taken as an ICAO feature. Nose shadow values are from range &lt;-10000,10000&gt; Values near -10000 indicates 'very strong local (eyes/nose) shadows present', values near 10000 indicates 'no local shadows present'. The decision threshold is around 0.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.Specularity">
            <summary>
            Face attribute for evaluating whether spotlights aren't present on face. The attribute can be taken as an ICAO feature. Specularity values are from range &lt;-10000,10000&gt; Values near -10000 indicates 'very strong specularity present', values near 10000 indicates 'no specularity present'. The decision threshold is around 0.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.EyeGaze">
            <summary>
            Face attribute for evaluating whether a gaze-direction is frontal. The attribute can be taken as an ICAO feature. Eye gaze values are from range &lt;-10000,10000&gt;. Values near -10000 indicates 'sideway ahead eyes gaze', values near 10000 indicates 'straight ahead eyes gaze'. The decision threshold is around 0.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.EyeStatusR">
            <summary>
            Face attribute for evaluating right eye status. The attribute can be taken as an ICAO feature. Right eye values are from range &lt;-10000,10000&gt;. Values near -10000 indicates 'closed, narrowed or bulged eye', values near 10000 indicates 'normally opened eye'. The decision threshold is around 0.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.EyeStatusL">
            <summary>
            Face attribute for evaluating left eye status. The attribute can be taken as an ICAO feature. Left eye values are from range &lt;-10000,10000&gt;. Values near -10000 indicates 'closed, narrowed or bulged eye', values near 10000 indicates 'normally opened eye'. The decision threshold is around 0.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.GlassStatus">
            <summary>
            Face attribute for evaluating glasses presence. Glasses values are from range &lt;-10000,10000&gt;. Values near -10000 indicates 'no glasses present', values near 10000 indicates 'glasses present'. The decision threshold is around 0.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.HeavyFrame">
            <summary>
            Face attribute for evaluating whether glasses with heavy frames are not present. The attribute can be taken as an ICAO feature. Heavy frame glasses values are from range &lt;-10000,10000&gt;. Values near -10000 indicates 'no heavy frame glasses present', values near 10000 indicates 'heavy frame glasses present'. The decision threshold is around 0.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.MouthStatus">
            <summary>
            Face attribute for evaluating mouth status. The attribute can be taken as an ICAO feature. Mouth status values are from range &lt;-10000,10000&gt;. Values near -10000 indicates 'open mouth, smile showing teeth or round lips present', values near 10000 indicates 'mouth with no expression'. The decision threshold is around 0.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.BackgroundUniformity">
            <summary>
            Face attribute for evaluating whether background is uniform. The attribute can be taken as an ICAO feature. Background uniformity values are from range &lt;-10000,10000&gt;. Values near -10000 indicates 'very un-uniform background present', values near 10000 indicates 'uniform background present'. The decision threshold is around 0.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.Age">
            <summary>
            Face attribute for evaluating age of subject using the face.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.RedEyeR">
            <summary>
            Face attribute for evaluating whether red-eye effect is not present on right eye. The attribute can be taken as an ICAO feature. Right red-eye effect values are from range &lt;-10000,10000&gt;. Values near -10000 indicates 'no red-eye effect present', values near 10000 indicates 'red-eye effect present'. The decision threshold is around 0.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.RedEyeL">
            <summary>
            Face attribute for evaluating whether red-eye effect is not present on left eye. The attribute can be taken as an ICAO feature. Left red-eye effect values are from range &lt;-10000,10000&gt;. Values near -10000 indicates 'no red-eye effect present', values near 10000 indicates 'red-eye effect present'. The decision threshold is around 0.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.Roll">
            <summary>
            Face attribute for evaluating whether head roll is in range. The attribute can be taken as an ICAO feature. Roll rotation values are from range &lt;-10000,10000&gt;. Values near -10000 indicates 'too left rotated', values near 10000 indicates 'too right rotated', values around 0 indicates OK. The decision thresholds are around -5000 and 5000.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.Yaw">
            <summary>
            Face attribute for evaluating whether head yaw is in range. The attribute can be taken as an ICAO feature. Yaw rotation values are from range &lt;-10000,10000&gt; Values near -10000 indicates 'too left rotated', values near 10000 indicates 'too right rotated', values around 0 indicates OK. The decision thresholds are around -5000 and 5000.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.Pitch">
            <summary>
            Face attribute for evaluating whether head pitch is in range. The attribute can be taken as an ICAO feature. Pitch rotation values are from range &lt;-10000,10000&gt; Values near -10000 indicates 'pitch too down', values near 10000 indicates 'pitch too up', values around 0 indicates OK. The decision thresholds are around -5000 and 5000.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.Gender">
            <summary>
            Face attribute for evaluating gender of subject. Gender values are from range &lt;-10000, 10000&gt;. Values near -10000 indicates 'male', values near 10000 indicates 'female'. The decision threshold is around 0.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.SegmentationMask">
            <summary>
            Face attribute for evaluating whether correct image head-shoulder segmentation can be done.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.Crop">
            <summary>
            Face attribute for evaluating whether correct face image cropping can be done.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.Template">
            <summary>
            Face attribute for evaluating whether face template can be extracted.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.EyeDistance">
            <summary>
            Face attribute for evaluating distance between eyes in pixels. The attribute can be taken as an ICAO feature.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.FaceConfidence">
            <summary>
            Face attribute for evaluating confidence score of the face related to face detection. The attribute can be taken as an ICAO feature. Face confidence values are from range &lt;0,10000&gt;. The higher the value of the attribute the better quality of the face. The decision thresholds are around 600, but it depends on the face image quality / camera angle etc.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.FaceVerificationConfidence">
            <summary>
            Face attribute for evaluating suitability of face for matching, so called template verification confidence.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.FaceRelativeArea">
            <summary>
            Area of face relative to image size
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.FaceRelativeAreaInImage">
            <summary>
            Area of face visible in image relative to total area of face. This value implies the percentage of face area outside the image.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.RollAngle">
            <summary>
            Face attribute representing angle rotation of head towards camera reference frame around Z-axis as per DIN9300
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.PitchAngle">
            <summary>
            Face attribute representing angle rotation of head towards camera reference frame around X-axis as per DIN9300
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.YawAngle">
            <summary>
            Face attribute representing angle rotation of head towards camera reference frame around Y-axis as per DIN9300
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.FaceSize">
            <summary>
            Face attribute representing face size - the maximum of eye distance and eye-mouth distance.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.PassiveLiveness">
            <summary>
            Face attribute for evaluating the liveness from single image (passive liveness).
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.BrightnessRaw">
            <summary>
            Face attribute for evaluating raw value of brightness
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.ContrastRaw">
            <summary>
            Face attribute for evaluating raw value of contrast
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.ShadowRaw">
            <summary>
            Face attribute for evaluating raw value of shadow
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.SharpnessRaw">
            <summary>
            Face attribute for evaluating raw value of sharpness
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.UniqueIntensityLevelsRaw">
            <summary>
            Face attribute for evaluating raw value of unique intensity levels
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.Mask">
            <summary>
            Face attribute for evaluating face mask (nose and mouth coverage) presence
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeId.LastItem">
            <summary>
            Auxiliary attribute defining last item of the <see cref="T:Innovatrics.IFace.FaceAttributeId"/> enum
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.FaceAttributeRangeStatus">
            <summary>
            Possible results of face attribute reliability range evaluation. Reliability range evaluation tells whether face attribute value is below, in, or above reliability range defined in face attribute condition. Reliability range evaluation is not dependent on face attribute dependencies evaluation. However the final result of reliability evaluation (ICAO compliance) depends on reliability range evaluation as well as on feature attribute dependencies evaluation. This enum is deprecated and will be removed in iface 4.0 release
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeRangeStatus.TooLow">
            <summary>
            Face attribute value below reliability range
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeRangeStatus.InRange">
            <summary>
            Face attribute value in reliability range
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceAttributeRangeStatus.TooHigh">
            <summary>
            Face attribute value above reliability range
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.FaceCropMethod">
            <summary>
            Face can be cropped using one of listed methods This enum is deprecated in favor of future enum <c>IFACE_CropMethod</c> and will be removed in iface 4.0 release
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceCropMethod.TokenFrontal">
            <summary>
            Token Frontal Image cropping method defined in ISO/IEC 19794-5 standard
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceCropMethod.FullFrontal">
            <summary>
            Full Frontal Image cropping method defined in ISO/IEC 19794-5 standard
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceCropMethod.FullFrontalExtended">
            <summary>
            Full Frontal Image cropping method defined in ISO/IEC 19794-5 standard with canvas size center-enlarged by factor specified in parameter <see cref="F:Innovatrics.IFace.Parameter.FaceCropFullFrontalExtendedScale"/> in each direction.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceCropMethod.TokenNotFrontal">
            <summary>
            Image cropping method similar to `Token` but works quite well even on non-frontal images
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceCropMethod.TokenFrontalExtended">
            <summary>
            Token Frontal Image cropping method defined in ISO/IEC 19794-5 standard with canvas size center-enlarged by factor specified in parameter <see cref="F:Innovatrics.IFace.Parameter.FaceCropTokenFrontalExtendedScale"/> in each direction.
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.FaceCropMethodValues">
            <summary>
            Parameter name for IFACE_GetCropRectangle used with face entity. Parameter determines crop method.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceCropMethodValues.TokenFrontal">
            <summary>
            Token Frontal Image cropping method defined in ISO/IEC 19794-5 standard
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceCropMethodValues.FullFrontal">
            <summary>
            Full Frontal Image cropping method defined in ISO/IEC 19794-5 standard
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceCropMethodValues.FullFrontalExtended">
            <summary>
            Full Frontal Image cropping method defined in ISO/IEC 19794-5 standard with canvas size center-enlarged by factor specified in parameter <see cref="F:Innovatrics.IFace.Parameter.FaceCropFullFrontalExtendedScale"/> in each direction.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceCropMethodValues.TokenNotFrontal">
            <summary>
            Image cropping method similar to `Token` but works quite well even on non-frontal images
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceCropMethodValues.TokenFrontalExtended">
            <summary>
            Token Frontal Image cropping method defined in ISO/IEC 19794-5 standard with canvas size center-enlarged by factor specified in parameter <see cref="F:Innovatrics.IFace.Parameter.FaceCropTokenFrontalExtendedScale"/> in each direction.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceDetectionSpeedAccuracyMode.AccurateServer">
            <summary>
            Accurate server mode (available only in server edition) with highest accuracy available in IFace. Very robust neural network based face detector - partially occluded, blury, profile, rotated faces or faces with sunglasses are detected. Speed of this face detection on CPU is the slowest comparing to other modes. This mode can be fully GPU accelerated.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceDetectionSpeedAccuracyMode.Accurate">
            <summary>
            Neural network face detector and facial features detector with very high accuracy. It is slower than <c>balanced</c> mode. This mode can be fully GPU accelerated.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceDetectionSpeedAccuracyMode.Balanced">
            <summary>
            Neural network face detector and facial features detector with good accuracy. It is slower than <c>fast</c> mode. This mode can be fully GPU accelerated.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceDetectionSpeedAccuracyMode.Fast">
            <summary>
            The fastest face detector in combination with the fastest face validators and facial features detectors are used when <i>fast</i> mode is used. Some faces that are partially occluded faces or faces with sunglasses may be missed. However the speed performance of the face detection is much better as when other modes are used. This mode can be only partially GPU accelerated.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceDetectionSpeedAccuracyMode.Fast_e1">
            <summary>
            Deprecated mode. The mode will be removed in iface 4.0 release (now, please use fast). Current behavior of fast_e1 is similar to fast.
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.FaceFeatureId">
            <summary>
            Each facial feature has its own ID. Names of the features are based on anatomical position of the feature on a face (not position as seen on image) E.g. right eye is the persons right eye.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.Unknown">
            <summary>
            Unknown facial feature
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.RightEyeOuterCorner">
            <summary>
            Right eye outer corner
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.RightEyeCentre">
            <summary>
            Right eye center
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.RightEyeInnerCorner">
            <summary>
            Right eye inner corner
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.LeftEyeInnerCorner">
            <summary>
            Left eye inner corner
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.LeftEyeCentre">
            <summary>
            Left eye center
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.LeftEyeOuterCorner">
            <summary>
            Left eye outer corner
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.NoseRoot">
            <summary>
            Nose root
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.NoseRightBottom">
            <summary>
            Nose right bottom
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.NoseTip">
            <summary>
            Nose tip
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.NoseLeftBottom">
            <summary>
            Nose left bottom
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.NoseBottom">
            <summary>
            Nose bottom
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.MouthRightCorner">
            <summary>
            Right corner of mouth
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.MouthCenter">
            <summary>
            Center of the mouth
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.MouthLeftCorner">
            <summary>
            Left corner of mouth
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.MouthUpperEdge">
            <summary>
            Center point on outer edge of upper lip
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.MouthLowerEdge">
            <summary>
            Center point on outer edge of lower lip
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.RightEyeBrowOuterEnd">
            <summary>
            Outer end of right eye brow
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.RightEyeBrowInnerEnd">
            <summary>
            Inner end of right eye brow
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.LeftEyeBrowInnerEnd">
            <summary>
            Inner end of left eye brow
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.LeftEyeBrowOuterEnd">
            <summary>
            Outer end of left eye brow
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.FaceRightEdge">
            <summary>
            Right edge of face
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.FaceChinTip">
            <summary>
            Tip of chin
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeatureId.FaceLeftEdge">
            <summary>
            Left edge of face
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.FaceTemplateExtractionSpeedAccuracyMode">
            <summary>
            Parameter defining face template extraction mode. It represents trade-off between face template creation speed and face template quality. <para/><i>Value type</i>: String, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceTemplateExtractionSpeedAccuracyMode.AccurateServer">
            <summary>
            Face templates of even better quality then templates generated by <i>accurate</i> mode. Mode used in FRVT benchmarks. However the performance of the face template creation is not as good as when <c>accurate</c> mode is used.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceTemplateExtractionSpeedAccuracyMode.Accurate">
            <summary>
            Face templates suitable for verification/identification of very high accuracy are created when <i>accurate</i> mode is used. However the performance of the face template creation is not as good as when <c>balanced</c> or <c>fast</c> mode is used.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceTemplateExtractionSpeedAccuracyMode.Balanced">
            <summary>
            Face templates suitable for verification/identification of high accuracy are created when <i>balanced</i> mode is used. The performance of the face template creation is somewhere in between of <c>accurate</c> and <c>fast</c> modes.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceTemplateExtractionSpeedAccuracyMode.Fast">
            <summary>
            Face templates suitable for verification of fairly good accuracy are created when <i>fast</i> mode is used. The performance of face template creation is very fast. Suitable for mobile/embedded devices.
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.GlobalParameter">
            <summary>
            Value used for entity parameter in <c><see cref="M:Innovatrics.IFace.IVisualObjectHandler.SetParam(System.String,System.String)"/></c>, <c><see cref="M:Innovatrics.IFace.IFace.GetParamInternal(System.IntPtr,System.String)"/></c> and <c><see cref="M:Innovatrics.IFace.IVisualObjectHandler.GetParam(System.String)"/></c> functions calls when global parameters are set. This can be done only before calling <c><see cref="M:Innovatrics.IFace.IFace.Init"/>.</c>
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.GlobalParameter.ThreadNum">
            <summary>
            Parameter defining maximum number of parallel threads used within one entity. <para/><i>Note: Valid range of values &lt;1,64&gt;</i> <para/><i>Value type</i>: Integer, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.GlobalParameter.ThreadManagementMode">
            <summary>
            Parameter defining multi-threading mode (trade-off between speed and memory usage) <para/><i>Value type</i>: String, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.GlobalParameter.GpuEnabled">
            <summary>
            Parameter defining if the CUDA GPU acceleration is enabled. <para/><i>Note: works only with special CUDA GPU optimized version of IFace</i> <para/><i>Value type</i>: Boolean, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.GlobalParameter.GpuDeviceId">
            <summary>
            Parameter defining the device id which will be used for GPU CUDA computations <para/><i>Note: works only if CUDA GPU optimization enabled and with special CUDA GPU optimized version of IFace</i> <para/><i>Value type</i>: Integer, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.GlobalParameter.MinValidImageSize">
            <summary>
            Parameter specifying minimal valid size (width or height) of image accepted by IFace. <para/><i>Value type</i>: Integer, Read-Only
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.GlobalParameter.GetListOfAllParams">
            <summary>
            Parameter containing all available parameters names. <para/><i>Value type</i>: String, Read-Only
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.IFaceExceptionCode">
            <summary>
            Enum specifying error code return by IFace functions
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.OK">
            <summary>
            No error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.Generic">
            <summary>
            Generic IFace error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.MemoryGeneric">
            <summary>
            Generic memory error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.InitGeneric">
            <summary>
            Generic initialization error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamGeneric">
            <summary>
            Generic invalid parameter error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.IoGeneric">
            <summary>
            Generic IO error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.LicenseIntegrationGeneric">
            <summary>
            Generic License error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.AlgorithmGeneric">
            <summary>
            Generic IFace algorithm error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.VerificationGeneric">
            <summary>
            Generic IFace verification error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.TrackingGeneric">
            <summary>
            Generic IFace tracking error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.LivenessGeneric">
            <summary>
            Generic IFace liveness error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.OtherGeneric">
            <summary>
            Generic other error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.MemoryReadFromNull">
            <summary>
            Read from NULL pointer
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.InitEntity">
            <summary>
            IFace entity initialization error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.InitSolver">
            <summary>
            Solver initialization error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.InitCascade">
            <summary>
            Cascade Solver initialization error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.InitNumFaces">
            <summary>
            Wrong number of face entities per face handler entity (min. 1, max. 64)
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.InitObjectHandler">
            <summary>
            Object handler initialization error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamFaceHandler">
            <summary>
            Invalid face handler entity error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamFace">
            <summary>
            Invalid face entity error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamFeature">
            <summary>
            Invalid face features or eye-distance
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamImageFormat">
            <summary>
            Unsupported image format error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamFaceSize">
            <summary>
            Invalid face size error (too big or too small face-size)
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamUser">
            <summary>
            Invalid user parameter error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamNorm">
            <summary>
            Data normalization error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamOutOfImage">
            <summary>
            Falling outside the image boundaries
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamIndexOut">
            <summary>
            Index out of bounds
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamBufferSize">
            <summary>
            Buffer size allocated is not sufficient
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamReadOnly">
            <summary>
            Can't set read-only parameter
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamNoScore">
            <summary>
            Can't get score or ICAO compliance range status from face attribute (e.g. crop, age, segmentation mask etc.)
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamConditionSyntax">
            <summary>
            Syntax error in face attribute condition parameter
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamGpuDeviceNotAvailable">
            <summary>
            Can't enable selected CUDA device
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamInvalidParamValue">
            <summary>
            Invalid parameter value
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamNotFound">
            <summary>
            Parameter not found
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamEntity">
            <summary>
            Invalid entity
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.ParamTrackingMask">
            <summary>
            Tracking mask is not valid (e.g. only zero values)
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.IoLoadFile">
            <summary>
            Can't read file
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.IoSaveExists">
            <summary>
            Saving to file which already exists
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.IoSaveEmpty">
            <summary>
            Saving empty data
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.IoRename">
            <summary>
            Error when renaming file
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.IoSaveFails">
            <summary>
            Saving failed
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.LicenseAlreadyInitialized">
            <summary>
            License was already initialized
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.LicenseAlreadyUninitialized">
            <summary>
            License was already uninitialized
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.AlgorithmUninitialized">
            <summary>
            Uninitialized variable error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.AlgorithmWrongParam">
            <summary>
            Wrong input parameter
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.AlgorithmNotAvailable">
            <summary>
            Functionality is not implemented yet or is disabled. Please check <b>IFace editions features and platforms</b> section in IFace SDK documentation.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.AlgorithmImpossibleToSolve">
            <summary>
            Algorithm is not possible to solve
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.VerificationVersion">
            <summary>
            Face verification template of this version is not supported
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.VerificationIncompatible">
            <summary>
            Incompatible version of face verification templates
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.VerificationCorrupted">
            <summary>
            Face verification template is corrupted
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.TrackingFaceNotAvailable">
            <summary>
            Face entity can't be extracted from the object entity This error code will be renamed begining IFace version 4.0 to IFACE_ERR_TRACKING_ENTITY_NOT_AVAILABLE
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.TrackingInconsistentImageDimensions">
            <summary>
            The dimensions of the current tracking image don't match with the dimensions of the previous images or the tracking mask
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.LivenessDotPositionRedundantDefinition">
            <summary>
            Multiple dot position were defined, but corresponding frames were not set (<c><see cref="M:Innovatrics.IFace.IVisualObjectHandler.SetLivenessDotPosition(System.Int32,System.Int32,System.Int64)"/></c> was not followed by <c><see cref="M:Innovatrics.IFace.IVisualObjectHandler.TrackObjects(System.Drawing.Image,System.Int64,Innovatrics.IFace.VisualObject[])"/></c> call)
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.LivenessDotPositionNotValid">
            <summary>
            Invalid screen dot position was set
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.LivenessNoFace">
            <summary>
            No face detected
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.LivenessTooManyFaces">
            <summary>
            Too many faces detected
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.OtherImgProc">
            <summary>
            Other image processing error
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.IFaceExceptionCode.OtherDatProc">
            <summary>
            Other data processing error
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.enums.ImageSaveType">
            <summary>
            Image can be save to memory with listed formats
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.enums.ImageSaveType.Bmp">
            <summary>
            Windows bitmap
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.enums.ImageSaveType.Jpg">
            <summary>
            JPEG file
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.enums.ImageSaveType.Png">
            <summary>
            Portable Network Graphics
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.enums.ImageSaveType.Tiff">
            <summary>
            TIFF file
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.enums.TrackMotionOptimization">
            <summary>
            Parameter defining how video motion detection in video influences object (face) detection in object tracking. Motion in video can define areas where objects move and object detection can be performed only within these areas. This parameter has influence on tracking performance. <para/><i>Value type</i>: String, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.enums.TrackMotionOptimization.Disabled">
            <summary>
            Motion optimization is disabled
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.enums.TrackMotionOptimization.HistoryShort">
            <summary>
            Motion is detected only within very short video history.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.enums.TrackMotionOptimization.HistoryLongAccurate">
            <summary>
            Motion is detected within longer video frames history. Bigger rectangular areas around motion detected regions are used for object detection.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.enums.TrackMotionOptimization.HistoryLongFast">
            <summary>
            Motion is detected within longer video frames history. Just motion detected regions are used for object detection.
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.enums.TrackSpeedAccuracyMode">
            <summary>
            Parameter defining face tracking accuracy mode, which is a trade-off between speed and accuracy. <para/><i>Value type</i>: String, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.enums.TrackSpeedAccuracyMode.Accurate">
            <summary>
            The most precise face tracking method However the performance of the face tracking is not as good as when <c>fast</c> mode is used.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.enums.TrackSpeedAccuracyMode.Balanced">
            <summary>
            The performance and speed is somewhere between <c>fast</c> and <c>accurate</c>
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.enums.TrackSpeedAccuracyMode.Fast">
            <summary>
            The fastest face tracking methods are used when this mode is selected
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.enums.TrackTrackingMode">
            <summary>
            Parameter defining the tracking mode for switching between the use-cases of the tracking algorithm, affecting the behavior of the related API functions. <para/><i>Value type</i>: String, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.enums.TrackTrackingMode.ObjectTracking">
            <summary>
            Moving objects (faces) are detected and subsequently tracked on a scene.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.enums.TrackTrackingMode.LivenessDot">
            <summary>
            Face liveness detection based on dot (and the related eye) movement.
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.LivenessState">
            <summary>
            Possible states of face liveness check
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.LivenessState.NotStarted">
            <summary>
            Liveness data collection hasn't started yet.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.LivenessState.FinishedNotEnough">
            <summary>
            Liveness data collection has been interrupted before enough data was collected.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.LivenessState.FinishedEnough">
            <summary>
            Liveness data collection has been interrupted but enough data was collected.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.LivenessState.InprogressNotEnough">
            <summary>
            Liveness data collection is in progress, but there is still not enough data collected for liveness score evaluation.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.LivenessState.InprogressEnough">
            <summary>
            Liveness data collection is in progress, there is enough data collected for liveness score evaluation and the collection continues.
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.LoggerSeverityLevel">
            <summary>
            IFace can be used with logging turned on by <see cref="M:Innovatrics.IFace.IFace.SetLogger(Innovatrics.IFace.LoggerSeverityLevel,System.String)"/> function. Various logging levels can be set.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.LoggerSeverityLevel.Debug">
            <summary>
            Messages with levels FATAL, ERROR, WARNING, INFO are logged as well as API entries and exits
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.LoggerSeverityLevel.Info">
            <summary>
            Messages with levels FATAL, ERROR, WARNING, INFO are logged
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.LoggerSeverityLevel.Warning">
            <summary>
            Messages with levels FATAL, ERROR, WARNING are logged
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.LoggerSeverityLevel.Error">
            <summary>
            Messages with levels FATAL, ERROR are logged
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.LoggerSeverityLevel.Fatal">
            <summary>
            Only messages with levels FATAL are logged
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.Parameter">
            <summary>
            Parameters that can be set via FaceHandler.SetParam
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "F:Innovatrics.IFace.Parameter.FaceDetSpeedAccuracyMode" -->
        <!-- Badly formed XML comment ignored for member "F:Innovatrics.IFace.Parameter.FaceDetConfidenceThreshold" -->
        <member name="F:Innovatrics.IFace.Parameter.MinValidFaceSize">
            <summary>
            Parameter specifying minimal valid size of face which is detectable by IFace in current detection mode. Face size is given by maximum of eyes distance and eyes to mouth distance in image. It is measured in pixels. <para/><i>Value type</i>: Integer, Read-Only
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceDetMaxImageSize">
            <summary>
            Parameter affecting 'balanced'/'accurate'/'accurate_server' face detection mode. It defines maximal image size of image entering to internal solver. If you have limited resources (e.g. memory or GPU memory), you will probably have to set this parameter to lower value. The value of this param also affects minFaceSize that can be set-up. Simply said, if you set value of this parameter to higher number, you will be able to set the minFaceSize to smaller value and you will be able to detect smaller faces. The constrain for possible minFaceSize can be formulated as follows: `minFaceSize >= 10 * max(in_img.width, in_img.height) / <see cref="F:Innovatrics.IFace.Parameter.FaceDetMaxImageSize"/> <para/><i>Value type</i>: Integer, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceTmplExtSpeedAccuracyMode">
            <summary>
            Parameter defining face template extraction mode. It represents trade-off between face template creation speed and face template quality. <para/><i>Value type</i>: String, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.AgeGenderSpeedAccuracyMode">
            <summary>
            Parameter defining age and gender evaluation mode. It represents trade-off between age and gender evaluation speed and precision of the age and gender predictions. <para/><i>Value type</i>: String, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.TrackSpeedAccuracyMode">
            <summary>
            Parameter defining face tracking accuracy mode, which is a trade-off between speed and accuracy. <para/><i>Value type</i>: String, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.BackgroundColor">
            <summary>
            Parameter defining color which is used to fill in parts of cropped image that fall outside the original source image boundaries. Valid value is hexadecimal code string e.g. "RRGGBB". <para/><i>Value type</i>: String, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.GetListOfAllParams">
            <summary>
            Parameter containing all available parameters names. <para/><i>Value type</i>: String, Read-Only
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceCropFullFrontalExtendedScale">
            <summary>
            Parameter defining amount of background image area enlargement of fully-frontal cropped facial image. <para/><i>Value type</i>: Integer, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceCropTokenFrontalExtendedScale">
            <summary>
            Parameter defining amount of background image area enlargement of token-frontal cropped facial image. <para/><i>Value type</i>: Integer, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.SegmentationMattingType">
            <summary>
            Parameter defining type of matting used after head shoulder segmentation. <para/><i>Value type</i>: String, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondSharpness">
            <summary>
            Parameter defining reliability condition and dependencies for facial sharpness
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondBrightness">
            <summary>
            Parameter defining reliability condition and dependencies for facial brightness
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondContrast">
            <summary>
            Parameter defining reliability condition and dependencies for facial contrast
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondUniqueIntensityLevels">
            <summary>
            Parameter defining reliability condition and dependencies for facial unique intensity levels
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondShadow">
            <summary>
            Parameter defining reliability condition and dependencies for facial shadow
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondNoseShadow">
            <summary>
            Parameter defining reliability condition and dependencies for facial sharp shadows
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondSpecularity">
            <summary>
            Parameter defining reliability condition and dependencies for facial specularity
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondEyeGaze">
            <summary>
            Parameter defining reliability condition and dependencies for eye gaze
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondEyeStatusR">
            <summary>
            Parameter defining reliability condition and dependencies for right eye statue
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondEyeStatusL">
            <summary>
            Parameter defining reliability condition and dependencies for left eye status
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondGlassStatus">
            <summary>
            Parameter defining reliability condition and dependencies for glass status
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondHeavyFrame">
            <summary>
            Parameter defining reliability condition and dependencies for heavy frame
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondMouthStatus">
            <summary>
            Parameter defining reliability condition and dependencies for mouth status
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondBackgroundUniformity">
            <summary>
            Parameter defining reliability condition and dependencies for background uniformity
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondAge">
            <summary>
            Parameter defining reliability dependencies for person age estimation
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondRedEyeR">
            <summary>
            Parameter defining reliability condition and dependencies for red-eye effect on right eye
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondRedEyeL">
            <summary>
            Parameter defining reliability condition and dependencies for red-eye effect on left eye
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondRoll">
            <summary>
            Parameter defining reliability condition and dependencies for head orientation - roll
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondYaw">
            <summary>
            Parameter defining reliability condition and dependencies for head orientation - yaw
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondPitch">
            <summary>
            Parameter defining reliability condition and dependencies for head orientation - pitch
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondRollAngle">
            <summary>
            Parameter defining reliability condition and dependencies for angle rotation of head towards camera referrence frame around Z-axis as per DIN9300
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondYawAngle">
            <summary>
            Parameter defining reliability condition and dependencies for angle rotation of head towards camera referrence frame around Y-axis as per DIN9300
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondPitchAngle">
            <summary>
            Parameter defining reliability condition and dependencies for angle rotation of head towards camera referrence frame around X-axis as per DIN9300
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondGender">
            <summary>
            Parameter defining reliability dependencies for gender estimation
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondSegmentationMask">
            <summary>
            Parameter defining reliability dependencies for segmentation mask estimation
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondCrop">
            <summary>
            Parameter defining reliability dependencies for face cropping rectangle estimation
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondTemplate">
            <summary>
            Parameter defining reliability dependencies for recognition template creation
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondEyeDistance">
            <summary>
            Parameter defining reliability dependencies for eye distance estimation
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondFaceConfidence">
            <summary>
            Parameter defining reliability dependencies for face detection
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondFaceSize">
            <summary>
            Parameter defining reliability dependencies for face size estimation
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondPassiveLiveness">
            <summary>
            Parameter defining reliability dependencies for score of face passive liveness
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondBrightnessRaw">
            <summary>
            Parameter defining reliability dependencies for raw value of brightness
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondContrastRaw">
            <summary>
            Parameter defining reliability dependencies for raw value of contrast
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondShadowRaw">
            <summary>
            Parameter defining reliability dependencies for raw value of shadow
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondSharpnessRaw">
            <summary>
            Parameter defining reliability dependencies for raw value of sharpness
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceAttributeCondUniqueIntensityLevelsRaw">
            <summary>
            Parameter defining reliability dependencies for raw value of unique intensity levels
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.TrackMinFaceSize">
            <summary>
            Parameter defining minimal face size of faces detected in discovery frames. <para/><i>Value type</i>: Integer, Read-Write This constant is deprecated in favor of future constant <c>IFACE_PARAMETER_TRACK_MIN_OBJECT_SIZE</c> and will be removed in iface 4.0 release.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.TrackMaxFaceSize">
            <summary>
            Parameter defining maximal face size of faces detected in discovery frames. <para/><i>Value type</i>: Integer, Read-Write This constant is deprecated in favor of future constant <c>IFACE_PARAMETER_TRACK_MAX_OBJECT_SIZE</c> and will be removed in iface 4.0 release.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.TrackFaceDiscoveryFrequenceMs">
            <summary>
            Parameter defining how often often discovery (full frame face detection) frames appear (in milliseconds). This parameter has influence on tracking performance - the more often discovery frames appears the more the slower but more accurate is the tracking. This constant is deprecated in favor of future constant <c>IFACE_PARAMETER_TRACK_DISCOVERY_FREQUENCE_MS</c> and will be removed in iface 4.0 release. <para/><i>Value type</i>: Integer, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.TrackDeepTrack">
            <summary>
            Parameter defining whether face entity is obtainable from tracked object in every video frame ('true') or not ('false'). Please note that both eyes must be visible (trackable) if deep tracking should work. This parameter has influence on tracking performance - if set to 'false' then tracking is faster. <para/><i>Value type</i>: Boolean, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.TrackMotionOptimization">
            <summary>
            Parameter defining how video motion detection in video influences object (face) detection in object tracking. Motion in video can define areas where objects move and object detection can be performed only within these areas. This parameter has influence on tracking performance. <para/><i>Value type</i>: String, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.TrackTrackingMode">
            <summary>
            Parameter defining the tracking mode for switching between the use-cases of the tracking algorithm, affecting the behavior of the related API functions. <para/><i>Value type</i>: String, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.TrackMinDotPositionCount">
            <summary>
            Parameter defining the minimum number of valid dot positions needed for a dot-based liveness evaluation. <para/><i>Value type</i>: Integer over the interval [4;7], Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.CountConfidenceThreshold">
            <summary>
            Parameter defining threshold value for face confidence. Objects with lesser value are ignored (not counted). <para/><i>Value type</i>: Integer, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.CountRedetectTimeDelta">
            <summary>
            Parameter defining the maximum time difference (in milliseconds) between the disappearance of trajectory A and the appearance of trajectory B, for A and B to be considered as disjoint trajectories of the same object - i.e. represents a trade-off between speed and accuracy regarding unique object estimation. <para/><i>Value type</i>: Integer, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.CountMatchThreshold">
            <summary>
            Parameter defining matching confidence (similarity) threshold for trajectory merging based on face template. <para/><i>Value type</i>: Integer, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.CountTemplateMerge">
            <summary>
            Parameter defining whether the object counting algorithm can use trajectory merging based on face template. <para/><i>Value type</i>: Boolean, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.CountRequireMovement">
            <summary>
            Parameter defining whether the object counting algorithm should consider stationary objects as a valid target to count. <para/><i>Value type</i>: Boolean, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.MinValidPedestrianSize">
            <summary>
            Parameter specifying minimal valid size of pedestrian which is detectable by IFace in current detection mode. \Pedestrian size is specified as maximum of width and height of bounding box of pedestrian. It is measured in pixels. <para/><i>Value type</i>: Integer, Read-Only
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.PedestrianDetConfidenceThreshold">
            <summary>
            Parameter defining minimal detections score for pedestrian. Internal detections with lower score will not be returned from SDK <para/><i>Value type</i>: Integer, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.PedestrianDetMaxImageSize">
            <summary>
            Parameter affecting pedestrian dnn detection mode. It defines maximal image size of image entering to internal solver. If you have limited resources (e.g. memory or GPU memory), you will probably have to set this parameter to lower value. The value of this param also affects minPedestrianSize that can be set-up. Simply said, if you set this value to higher number, you will be able to set the minPedestrianSize to smaller value and you will be able to detect smaller pedestrians. The constrain for possible minPedestrianSize can be formulated as follows: `minPedestrianSize >= 10 * max(in_img.width, in_img.height) / <see cref="F:Innovatrics.IFace.Parameter.PedestrianDetMaxImageSize"/> <para/><i>Value type</i>: Integer, Read-Write
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.FaceCropMethod">
            <summary>
            Parameter name for IFACE_GetCropRectangle used with face entity. Parameter determines crop method.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Parameter.MinBgRatio">
            <summary>
            Specifies minimal background percentage of the whole image which needs to be available for background uniformity solver
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.SegmentationImageType">
            <summary>
            Resulting Segmentation image types
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.SegmentationImageType.Mask">
            <summary>
            Only segmentation mask (one channel image).
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.SegmentationImageType.Masked">
            <summary>
            Segmentation mask applied to image. Masked parts (background) are filled with color specified in <see cref="F:Innovatrics.IFace.Parameter.BackgroundColor"/> (three channel image).
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.SegmentationImageType.MaskedAlpha">
            <summary>
            Segmentation mask applied to image. Masked parts (background) are transparent (four channel image).
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.SegmentationMatting.Off">
            <summary>
            Defines no matting after head shoulder segmentation to soften mask edges
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.SegmentationMatting.Global">
            <summary>
            Global matting is used after head shoulder segmentation to soften mask edges
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.ThreadManagementMode.Single">
            <summary>
            Parallel processing is disabled and just single thread is used inside IFace SDK even when it is called in parallel with more threads. Defined number of threads <c><see cref="F:Innovatrics.IFace.GlobalParameter.ThreadNum"/></c> is ignored. This mode is the least memory consumptive mode.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.ThreadManagementMode.MaxParallel">
            <summary>
            The highest level of parallelism can be achieved when this mode is set. Defined number of threads <c><see cref="F:Innovatrics.IFace.GlobalParameter.ThreadNum"/></c> can be used in parallel within each entity. The performance of this mode should be the highest however the memory requirements are the highest as well. Due to parallel processing the memory is allocated with each face handler entity.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.ThreadManagementMode.MinMemory">
            <summary>
            Defined number of threads <c><see cref="F:Innovatrics.IFace.GlobalParameter.ThreadNum"/></c> used in parallel are shared between all face handlers. However when just one face handler entity is active then it can use <c><see cref="F:Innovatrics.IFace.GlobalParameter.ThreadNum"/></c> threads in parallel. No memory Due to parallel processing is allocated with each created face handler entity.
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.TrackedObjectFaceType">
            <summary>
            Possible types of face, that can be obtained from tracked object This enum is deprecated in favor of future enum <c>IFACE_TrackedEntitySelectionType</c> and will be removed in iface 4.0 release
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.TrackedObjectFaceType.Last">
            <summary>
            Face from the last frame. Parameter <c><see cref="F:Innovatrics.IFace.Parameter.TrackDeepTrack"/></c> must be set to 'true' if this face type has to be obtained.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.TrackedObjectFaceType.LastDiscovery">
            <summary>
            Face from the last discovery frame.
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.TrackedObjectFaceType.BestDiscovery">
            <summary>
            Face with the highest face confidence from all discovery frames processed during object tracking.
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.TrackedObjectState">
            <summary>
            Possible statuses of the object during tracking
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.TrackedObjectState.Clean">
            <summary>
            Object doesn't contain any tracking info
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.TrackedObjectState.Tracked">
            <summary>
            Object is currently successfully tracked
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.TrackedObjectState.Suspend">
            <summary>
            Object tracking is currently suspended (object has been lost), but it can be tracked again in future frames if it appears again
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.TrackedObjectState.Covered">
            <summary>
            Object is currently covered by another tracked object, but it can be tracked again in future frames if it appears again
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.TrackedObjectState.Lost">
            <summary>
            Object is lost from the scene and the tracking is stopped
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.TrackedObjectType">
            <summary>
            Possible types of the tracked object
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.TrackedObjectType.Face">
            <summary>
            Tracked object initialized by face detection
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.TrackedObjectType.GeneralRect">
            <summary>
            Tracked object initialized by general rectangle position and size
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.Face">
            <summary>
            Class representing face detected by method <see>
                    <cref>Innovatrics.IFace.FaceHandler.DetectFaces</cref>
                </see>
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.Face.serializeLock">
            <summary>
            Read-write lock for thread-safe template serialization.
            </summary>
        </member>
        <member name="P:Innovatrics.IFace.Face.DefaultFaceHandler">
            <summary>
            Gets or sets the default instance context.
            </summary>
            <value>
            The default instance context.
            </value>
        </member>
        <member name="M:Innovatrics.IFace.Face.#ctor(Innovatrics.IFace.FaceHandler)">
            <summary>
            Creates empty face entity. After face detection and processing with face related API
            </summary>
        </member>
        <member name="M:Innovatrics.IFace.Face.Serialize(Innovatrics.IFace.FaceHandler)">
            <summary>
            Serializes face entity content to byte array.
            </summary>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
            <returns>
            Serialized face entity data
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.Face.Deserialize(System.Byte[],Innovatrics.IFace.FaceHandler)">
            <summary>
            Deserialize face entity content from byte array.
            </summary>
            <param name="serializedFace">
            Pointer to memory filled with serialized face entity data
            </param>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
            <returns>
            Deserialized face entity
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.Face.GetBasicInfo(Innovatrics.IFace.FaceHandler)">
            <summary>
            Retrieves basic info about face (eyes position and face score).
            </summary>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
            <returns>
            Basic info about face (eyes position and face score)
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.Face.GetFeatures(Innovatrics.IFace.FaceFeatureId[])">
            <summary>
            Retrieves given facial features (position and score) using specified face. Position is in float precision.
            </summary>
            <param name="requestedFeatures">
            Array of requested features
            </param>
            <returns>
            Array of requested features
            </returns>
            <remarks>
            Scores of face features can be null as some features don't include confidence score
            </remarks>
        </member>
        <member name="M:Innovatrics.IFace.Face.GetFeatures(Innovatrics.IFace.FaceFeatureId[],Innovatrics.IFace.FaceHandler)">
            <summary>
            Retrieves given facial features (position and score) using specified face. Position is in float precision.
            </summary>
            <param name="requestedFeatures">
            Array of requested features
            </param>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
            <returns>
            Array of requested features
            </returns>
            <remarks>
            Scores of face features can be null as some features don't include confidence score
            </remarks>
        </member>
        <member name="M:Innovatrics.IFace.Face.SetFeatures(System.Drawing.Image,Innovatrics.IFace.FaceFeatureId[],System.Drawing.PointF[],Innovatrics.IFace.FaceHandler)">
            <summary>
            Sets the face features into face entity. Only selected facial features are set.
            </summary>
            <remarks>
            Auxiliary function which can be used for accuracy tests. Warning: The interface of this method can be changed in IFace 4.0
            </remarks>
            <param name="image">
            Image data
            </param>
            <param name="features">
            Array of selected facial features
            </param>
            <param name="positions">
            Positions of selected facial features
            </param>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
        </member>
        <member name="M:Innovatrics.IFace.Face.SetFeaturesWithConfidence(System.Drawing.Image,Innovatrics.IFace.FaceFeatureId[],System.Drawing.PointF[],System.Single[],Innovatrics.IFace.FaceHandler)">
            <summary>
            Sets the face features into face entity. Only selected facial features are set.
            </summary>
            <remarks>
            Auxiliary function which can be used for accuracy tests. Warning: The interface of this method can be changed in IFace 4.0. This function is deprecated in favor of <c><see cref="M:Innovatrics.IFace.Face.SetFeatures(System.Drawing.Image,Innovatrics.IFace.FaceFeatureId[],System.Drawing.PointF[],Innovatrics.IFace.FaceHandler)"/></c> (after interface change) and will be removed in iface 4.0 release.
            </remarks>
            <param name="image">
            Image data
            </param>
            <param name="features">
            Array of selected facial features
            </param>
            <param name="positions">
            Positions of selected facial features
            </param>
            <param name="confs">
            Confidences for points
            </param>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
        </member>
        <member name="M:Innovatrics.IFace.Face.GetAttribute(Innovatrics.IFace.FaceAttributeId,Innovatrics.IFace.FaceHandler)">
            <summary>
            Returns value of desired facial attribute. Attributes producing numeric score values (e.g. mouth status, eyes status etc.) can be calculated using this API function. Moreover some other attributes like age, eye-distance, gender etc. can be evaluated using this API function. Complete set of available attributes are enumerated in <see cref="T:Innovatrics.IFace.FaceAttributeId"/>. Attributes not returning numeric values can't be passed into <see cref="M:Innovatrics.IFace.Face.TryGetFaceAttribute(System.Single@,Innovatrics.IFace.FaceAttributeId,Innovatrics.IFace.IFaceExceptionCode[],Innovatrics.IFace.FaceHandler)"/> (e.g. segmentation mask). This API function can be used for ICAO compliance evaluation of some of face attributes.
            </summary>
            <param name="fattrId">
            Defines which facial attribute is evaluated. Possible values are enumerated in <see cref="T:Innovatrics.IFace.FaceAttributeId"/>.
            </param>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
            <returns>
            Value of desired facial attribute
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.Face.TryGetFaceAttribute(System.Single@,Innovatrics.IFace.FaceAttributeId,Innovatrics.IFace.IFaceExceptionCode[],Innovatrics.IFace.FaceHandler)">
            <summary>
            Returns value of desired facial attribute. Attributes producing numeric score values (e.g. mouth status, eyes status etc.) can be calculated using this API function. Moreover some other attributes like age, eye-distance, gender etc. can be evaluated using this API function. Complete set of available attributes are enumerated in <see cref="T:Innovatrics.IFace.FaceAttributeId"/>. Attributes not returning numeric values can't be passed into <see cref="M:Innovatrics.IFace.Face.TryGetFaceAttribute(System.Single@,Innovatrics.IFace.FaceAttributeId,Innovatrics.IFace.IFaceExceptionCode[],Innovatrics.IFace.FaceHandler)"/> (e.g. segmentation mask). This API function can be used for ICAO compliance evaluation of some of face attributes.
            </summary>
            <remarks>
            Returns null if one of possible errors is detected
            </remarks>
            <param name="fattrValue">
            Value of desired facial attribute
            </param>
            <param name="fattrId">
            Defines which facial attribute is evaluated. Possible values are enumerated in <see cref="T:Innovatrics.IFace.FaceAttributeId"/>.
            </param>
            <param name="possibleErrors">
            List of errors which don't throw exception
            </param>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
        </member>
        <member name="M:Innovatrics.IFace.Face.GetAttributeRaw(Innovatrics.IFace.FaceAttributeId,Innovatrics.IFace.FaceHandler)">
            <summary>
            Returns raw (unnormalized) value of desired facial attribute. Complete set of available attributes are enumerated in <see cref="T:Innovatrics.IFace.FaceAttributeId"/> (only numeric attributes can be passed into <see cref="M:Innovatrics.IFace.Face.TryGetFaceAttributeRaw(System.Single@,Innovatrics.IFace.FaceAttributeId,Innovatrics.IFace.IFaceExceptionCode[],Innovatrics.IFace.FaceHandler)"/>) This function is deprecated in favor of <c><see cref="M:Innovatrics.IFace.Face.EvaluateConditions(System.String,Innovatrics.IFace.FaceHandler)"/></c> and will be removed in iface 4.0 release
            </summary>
            <param name="fattrId">
            Defines which facial attribute is evaluated. Possible values are enumerated in <see cref="T:Innovatrics.IFace.FaceAttributeId"/>.
            </param>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
            <returns>
            Raw (unnormalized) value of desired facial attribute
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.Face.TryGetFaceAttributeRaw(System.Single@,Innovatrics.IFace.FaceAttributeId,Innovatrics.IFace.IFaceExceptionCode[],Innovatrics.IFace.FaceHandler)">
            <summary>
            Returns raw (unnormalized) value of desired facial attribute. Complete set of available attributes are enumerated in <see cref="T:Innovatrics.IFace.FaceAttributeId"/> (only numeric attributes can be passed into <see cref="M:Innovatrics.IFace.Face.TryGetFaceAttributeRaw(System.Single@,Innovatrics.IFace.FaceAttributeId,Innovatrics.IFace.IFaceExceptionCode[],Innovatrics.IFace.FaceHandler)"/>) This function is deprecated in favor of <c><see cref="M:Innovatrics.IFace.Face.EvaluateConditions(System.String,Innovatrics.IFace.FaceHandler)"/></c> and will be removed in iface 4.0 release
            </summary>
            <remarks>
            Returns null if one of possible errors is detected
            </remarks>
            <param name="fattrValueRaw">
            Raw (unnormalized) value of desired facial attribute
            </param>
            <param name="fattrId">
            Defines which facial attribute is evaluated. Possible values are enumerated in <see cref="T:Innovatrics.IFace.FaceAttributeId"/>.
            </param>
            <param name="possibleErrors">
            List of errors which don't throw exception
            </param>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
        </member>
        <member name="M:Innovatrics.IFace.Face.GetAttributeDependenciesStatus(Innovatrics.IFace.FaceAttributeId,Innovatrics.IFace.FaceHandler)">
            <summary>
            Retrieves fulfillment of dependencies which are necessary for valid face attribute evaluation (desired attribute is depending on them). If dependencies of desired feature are not fulfilled then validity of face attribute is not guaranteed. This API function can be used for ICAO compliance evaluation of some of face attributes. This function is deprecated in favor of <c><see cref="M:Innovatrics.IFace.Face.EvaluateConditions(System.String,Innovatrics.IFace.FaceHandler)"/></c> and will be removed in iface 4.0 release
            </summary>
            <param name="fattrId">
            Defines which face attribute is evaluated. Possible values are enumerated in <see cref="T:Innovatrics.IFace.FaceAttributeId"/>.
            </param>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
            <returns>
            Fulfillment of face attribute dependencies (desired attribute is depending on them). Possible values are enumerated in <see cref="T:Innovatrics.IFace.FaceAttributeDependenciesStatus"/>.
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.Face.EvaluateConditions(System.String,Innovatrics.IFace.FaceHandler)">
            <summary>
            Retrieves information about fulfillment of conditions which are described in the input <c>conditionsString</c>. If dependencies of desired feature are not fulfilled then validity of face attribute is not guaranteed. All attributes are parsed and evaluated even if some attributes are out of range. This API function can be used for ICAO compliance evaluation of some of face attributes.
            </summary>
            <param name="conditionsString">
            Null-terminated string containing conditons to be evaluated. See <c><see cref="F:Innovatrics.IFace.ConditionString.IFaceConditionsIcaoAll"/></c>.
            </param>
            <param name="entityHandler">
            Pointer to entity handler. Only <c>FaceHandler</c> is supported type. If <c>entityHandler == NULL</c>, then conditions are parsed from <c>conditionsString</c> but current values of attributes are not evaluated. In this case <see cref="F:Innovatrics.IFace.IFaceExceptionCode.OK"/> is returned.
            </param>
            <returns>
            Array of structs IFACE_ConditionsInfo containing the fulfillments of face attribute dependencies (desired attribute is depending on them). The array has to have size of <c>numConditions</c>. If <c>conditionsInfo</c> is set to NULL, only <c>numConditions</c> is returned.
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.Face.EvaluateConditionsFailFast(System.String,Innovatrics.IFace.FaceHandler)">
            <summary>
            Retrieves information about fulfillment of conditions which are described in the input <c>conditionsString</c>. If dependencies of desired feature are not fulfilled then validity of face attribute is not guaranteed. All attributes are parsed from <c>conditionsString</c> but current values are evaluated only up to first out-of-range attribute. This API function can be used for ICAO compliance evaluation of some of face attributes.
            </summary>
            <param name="conditionsString">
            Null-terminated string containing conditons to be evaluated. See <c><see cref="F:Innovatrics.IFace.ConditionString.IFaceConditionsIcaoAll"/></c>.
            </param>
            <param name="entityHandler">
            Pointer to entity handler. Only <c>FaceHandler</c> is supported type. If <c>entityHandler == NULL</c>, then conditions are parsed from <c>conditionsString</c> but current values of attributes are not evaluated. In this case <see cref="F:Innovatrics.IFace.IFaceExceptionCode.OK"/> is returned.
            </param>
            <returns>
            Array of structs IFACE_ConditionsInfo containing the fulfillments of face attribute dependencies (desired attribute is depending on them). The array has to have size of <c>numConditions</c>. If <c>conditionsInfo</c> is set to NULL, only <c>numConditions</c> is returned.
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.Face.GetAttributeRangeStatus(Innovatrics.IFace.FaceAttributeId,Innovatrics.IFace.FaceHandler)">
            <summary>
            Retrieves reliability range status of desired face attribute. Only features producing numeric values can be evaluated - see documentation of <see cref="T:Innovatrics.IFace.FaceAttributeId"/>. This API function can be used for ICAO compliance evaluation of some of face attributes.
            </summary>
            <param name="fattrId">
            Defines which face attribute is evaluated. Possible values are enumerated in <see cref="T:Innovatrics.IFace.FaceAttributeId"/>.
            </param>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
            <returns>
            Range compliance status of desired face attribute. Possible values are enumerated in <see cref="T:Innovatrics.IFace.FaceAttributeRangeStatus"/>.
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.Face.GetSegmentation(Innovatrics.IFace.FaceCropMethod,Innovatrics.IFace.SegmentationImageType,Innovatrics.IFace.FaceHandler)">
            <summary>
            Retrieves face image with background / foreground segmented. The segmentation image is cropped according to the selected cropping method <c><see cref="T:Innovatrics.IFace.FaceCropMethod"/></c>. The types of segmentation can be selected from <c><see cref="T:Innovatrics.IFace.SegmentationImageType"/></c>. The segmentation of hairstyle can be tuned by setting matting to <c><see cref="F:Innovatrics.IFace.Parameter.SegmentationMattingType"/></c> parameter.
            </summary>
            <param name="cropMethod">
            Face cropping method according to the <see cref="T:Innovatrics.IFace.FaceCropMethod"/> enum
            </param>
            <param name="segImageType">
            Type of output segmentation image type according to the <see cref="T:Innovatrics.IFace.SegmentationImageType"/> enum
            </param>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
            <returns>
            Face image with background / foreground segmented
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.Face.GetCropRectangle(Innovatrics.IFace.FaceCropMethod,Innovatrics.IFace.FaceHandler)">
            <summary>
            Retrieves face cropping rectangle according to the selected cropping method.
            </summary>
            <param name="cropMethod">
            Face cropping method according to the <see cref="T:Innovatrics.IFace.FaceCropMethod"/> enum
            </param>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
            <returns>
            Array of points of crop quadrilateral corners in following order: top-left, top-right, bottom-left, bottom-right
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.Face.GetCropImage(Innovatrics.IFace.FaceCropMethod,Innovatrics.IFace.FaceHandler)">
            <summary>
            Retrieves face image cropped according to the selected cropping method.
            </summary>
            <param name="cropMethod">
            Face cropping method according to the <see cref="T:Innovatrics.IFace.FaceCropMethod"/> enum
            </param>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
            <returns>
            Cropped image of the face
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.Face.CreateTemplate(Innovatrics.IFace.FaceHandler)">
            <summary>
            Retrieves face verification template.
            </summary>
            <param name="faceHandler">
            Pointer to face handler entity
            </param>
            <returns>
            Template data that can be used in function <see cref="M:Innovatrics.IFace.IFaceHandler.MatchTemplate(System.Byte[],System.Byte[])"/>
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.Face.Clone">
            <summary>
            Function that clone entities. Only face or pedestrian entity can be cloned.
            </summary>
            <faceHandler>
            Pointer to face handler entity
            </faceHandler>
            <returns>
            Copy of the face
            </returns>
        </member>
        <member name="T:Innovatrics.IFace.FaceBasicInfo">
            <summary>
            Readonly structure wrapping basic face features return by <see cref="M:Innovatrics.IFace.Face.GetBasicInfo(Innovatrics.IFace.FaceHandler)"/> property
            Structure contains face position, face score and face id
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceBasicInfo.RightEye">
            <summary>
            Coordinates of right eye
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceBasicInfo.LeftEye">
            <summary>
            Coordinates of left eye
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "F:Innovatrics.IFace.FaceBasicInfo.Score" -->
        <member name="T:Innovatrics.IFace.FaceFeature">
            <summary>
            Readonly structure describing face features returned by <see cref="M:Innovatrics.IFace.Face.GetFeatures(Innovatrics.IFace.FaceFeatureId[])"/>
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeature.FeatureID">
            <summary>
            Face feature id
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeature.Pos">
            <summary>
            Coordinates of the face feature
            </summary>
        </member>
        <member name="F:Innovatrics.IFace.FaceFeature.Score">
            <summary>
            Score (confidence) of the feature. It can be null if the score is not available
            </summary>
        </member>
        <member name="M:Innovatrics.IFace.FaceFeature.ToString">
            <summary>
            Converts the value of this instance to its equivalent string representation.
            </summary>
            <returns>
            The string representation of the value of this instance.
            </returns>
        </member>
        <member name="T:Innovatrics.IFace.FaceHandler">
            <summary>
            Class representing instance context of the IFace detection library
            Instance context specifies how many faces will be found by <see>
                    <cref>Innovatrics.IFace.FaceHandler.DetectFaces</cref>
                </see>
            </summary>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.#ctor">
            <summary>
            Creates and initializes face handler entity. Face handler entity contains data and parameters related to face processing (face detection, face matching, etc.).
            </summary>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.GetParam(System.String)">
            <summary>
            Gets user parameter value.
            </summary>
            <param name="parameterName">
            Parameter name
            </param>
            <returns>
            Parameter value
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.SetParam(System.String,System.String)">
            <param name="parameterName">
            Parameter name
            </param>
            <param name="parameterValue">
            Parameter value
            </param>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.DetectFaces(System.Drawing.Image,System.Single,System.Single,System.Int32)">
            <param name="image">
            Image data used for detection
            </param>
            <param name="minFaceSize">
            Defines minimal size of detected faces. In case of (0., 1> range, the value of minFaceSize is taken with respect to maximum of image dimensions. Otherwise it is considered as face size in pixels. Minimal valid value can be retrieved from parameter <see cref="F:Innovatrics.IFace.Parameter.MinValidFaceSize"/>.
            </param>
            <param name="maxFaceSize">
            Defines maximal size of detected faces. In case of (0., 1> range, the value of minFaceSize is taken with respect to maximum of image dimensions. Otherwise it is considered as face size in pixels.
            </param>
            <param name="maxFaceCount">
            Maximum count of faces to be detected
            </param>
            <returns>
            In: array of pointers to face entities, out: same face entities, filled with detected faces
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.DetectFaceForced(System.Drawing.Image,System.Single,System.Single)">
            <param name="image">
            Image data used for detection
            </param>
            <param name="minFaceSize">
            Defines minimal size of detected faces. In case of (0., 1> range, the value of minFaceSize is taken with respect to maximum of image dimensions. Otherwise it is considered as face size in pixels. Minimal valid value can be retrieved from parameter <see cref="F:Innovatrics.IFace.Parameter.MinValidFaceSize"/>.
            </param>
            <param name="maxFaceSize">
            Defines maximal size of detected faces. In case of (0., 1> range, the value of minFaceSize is taken with respect to maximum of image dimensions. Otherwise it is considered as face size in pixels.
            </param>
            <returns>
            In: pointer to face entity that will be filled with detected face
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.DetectFaces(System.String,System.Single,System.Single,System.Int32)">
            <param name="filePath">
            Path to image file
            </param>
            <param name="minFaceSize">
            Defines minimal size of detected faces. In case of (0., 1> range, the value of minFaceSize is taken with respect to maximum of image dimensions. Otherwise it is considered as face size in pixels. Minimal valid value can be retrieved from parameter <see cref="F:Innovatrics.IFace.Parameter.MinValidFaceSize"/>.
            </param>
            <param name="maxFaceSize">
            Defines maximal size of detected faces. In case of (0., 1> range, the value of minFaceSize is taken with respect to maximum of image dimensions. Otherwise it is considered as face size in pixels.
            </param>
            <param name="maxFaceCount">
            Maximum count of faces to be detected
            </param>
            <returns>
            In: array of pointers to face entities, out: same face entities, filled with detected faces
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.DetectFaceForced(System.String,System.Single,System.Single)">
            <param name="filePath">
            Path to image file
            </param>
            <param name="minFaceSize">
            Defines minimal size of detected faces. In case of (0., 1> range, the value of minFaceSize is taken with respect to maximum of image dimensions. Otherwise it is considered as face size in pixels. Minimal valid value can be retrieved from parameter <see cref="F:Innovatrics.IFace.Parameter.MinValidFaceSize"/>.
            </param>
            <param name="maxFaceSize">
            Defines maximal size of detected faces. In case of (0., 1> range, the value of minFaceSize is taken with respect to maximum of image dimensions. Otherwise it is considered as face size in pixels.
            </param>
            <returns>
            In: pointer to face entity that will be filled with detected face
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.DetectFaces(Innovatrics.IFace.IFaceRawImage,System.Single,System.Single,System.Int32)">
            <param name="image">
            Image data used for detection
            </param>
            <param name="minFaceSize">
            Defines minimal size of detected faces. In case of (0., 1> range, the value of minFaceSize is taken with respect to maximum of image dimensions. Otherwise it is considered as face size in pixels. Minimal valid value can be retrieved from parameter <see cref="F:Innovatrics.IFace.Parameter.MinValidFaceSize"/>.
            </param>
            <param name="maxFaceSize">
            Defines maximal size of detected faces. In case of (0., 1> range, the value of minFaceSize is taken with respect to maximum of image dimensions. Otherwise it is considered as face size in pixels.
            </param>
            <param name="maxFaceCount">
            Maximum count of faces to be detected
            </param>
            <returns>
            In: array of pointers to face entities, out: same face entities, filled with detected faces
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.DetectFaceForced(Innovatrics.IFace.IFaceRawImage,System.Single,System.Single)">
            <param name="image">
            Image data used for detection
            </param>
            <param name="minFaceSize">
            Defines minimal size of detected faces. In case of (0., 1> range, the value of minFaceSize is taken with respect to maximum of image dimensions. Otherwise it is considered as face size in pixels. Minimal valid value can be retrieved from parameter <see cref="F:Innovatrics.IFace.Parameter.MinValidFaceSize"/>.
            </param>
            <param name="maxFaceSize">
            Defines maximal size of detected faces. In case of (0., 1> range, the value of minFaceSize is taken with respect to maximum of image dimensions. Otherwise it is considered as face size in pixels.
            </param>
            <returns>
            In: pointer to face entity that will be filled with detected face
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.DetectFacesAtPositions(System.Drawing.Image,System.Drawing.PointF[],System.Drawing.PointF[])">
            <param name="image">
            Image data used for detection
            </param>
            <param name="rightEyes">
            Coordinates of right eyes
            </param>
            <param name="leftEyes">
            Coordinates of right eyes
            </param>
            <returns>
            In: array of pointers to face entities, out: same entities, filled with detected faces. Detected faces are confidence sorted using descending order, so the face with highest confidence has the highest score.
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.DetectFacesAtPositions(System.String,System.Drawing.PointF[],System.Drawing.PointF[])">
            <param name="filePath">
            path to image file
            </param>
            <param name="rightEyes">
            Coordinates of right eyes
            </param>
            <param name="leftEyes">
            Coordinates of right eyes
            </param>
            <returns>
            In: array of pointers to face entities, out: same entities, filled with detected faces. Detected faces are confidence sorted using descending order, so the face with highest confidence has the highest score.
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.DetectFacesAtPositions(Innovatrics.IFace.IFaceRawImage,System.Drawing.PointF[],System.Drawing.PointF[])">
            <param name="image">
            Image data used for detection
            </param>
            <param name="rightEyes">
            Coordinates of right eyes
            </param>
            <param name="leftEyes">
            Coordinates of right eyes
            </param>
            <returns>
            In: array of pointers to face entities, out: same entities, filled with detected faces. Detected faces are confidence sorted using descending order, so the face with highest confidence has the highest score.
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.DetectFacesOfArea(System.Drawing.Image,System.Single,System.Int32)">
            <param name="image">
            Image data used for detection
            </param>
            <param name="minFaceArea">
            Defines minimal area (relative to image size) of detected faces.
            </param>
            <param name="maxFaceCount">
            Maximum count of faces to be detected
            </param>
            <returns>
            In: array of pointers to face entities, out: same face entities, filled with detected faces
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.DetectFacesOfArea(System.String,System.Single,System.Int32)">
            <param name="filePath">
            Path to image file
            </param>
            <param name="minFaceArea">
            Defines minimal area (relative to image size) of detected faces.
            </param>
            <param name="maxFaceCount">
            Maximum count of faces to be detected
            </param>
            <returns>
            In: array of pointers to face entities, out: same face entities, filled with detected faces
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.DetectFacesOfArea(Innovatrics.IFace.IFaceRawImage,System.Single,System.Int32)">
            <param name="image">
            Image data used for detection
            </param>
            <param name="minFaceArea">
            Defines minimal area (relative to image size) of detected faces.
            </param>
            <param name="maxFaceCount">
            Maximum count of faces to be detected
            </param>
            <returns>
            In: array of pointers to face entities, out: same face entities, filled with detected faces
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.CreateTemplateBatch(Innovatrics.IFace.Face[])">
            <param name="faces">
            Array of face entities
            </param>
            <returns>
            Array of face templates
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.MatchTemplate(System.Byte[],System.Byte[])">
            <summary>
            Compares the similarity of two face templates and calculates their matching score. It is implementation of one to one matching known as verification.
            </summary>
            <param name="faceTemplate1">
            Face verification template created by <see cref="M:Innovatrics.IFace.Face.CreateTemplate(Innovatrics.IFace.FaceHandler)"/>
            </param>
            <param name="faceTemplate2">
            Face verification template created by <see cref="M:Innovatrics.IFace.Face.CreateTemplate(Innovatrics.IFace.FaceHandler)"/>
            </param>
            <returns>
            Matching score range is &lt;0, 100&gt;. Its values can be interpreted as follows: 1) Low values of the score, i.e. range &lt;0, 60&gt;, are normalized using FAR values and this formula score_L=-10*log(FAR). It means that score 30 is related to FAR=1:1000=10^-3, score 50 is related to FAR=1:100000=10^-5 (evaluated on our large testing non-matching pairs dataset). 2) High values of the score, i.e. range &lt;80, 100&gt;, are normalized using FRR values and this formula score_H=100/3*(FRR + 2). It means that score 80 is related to FRR=0.4, score 90 is related to FRR=0.7 (evaluated on our large testing matching pairs dataset). 3) Scores values in range (60, 80) are weighted average of score_L and score_H. This normalization help the users to select the score threshold according their needs. If it is too low e.g. score_thold = 30, then the chance of false accepted non-matching faces is quite high (FAR=10^-3). When it is too high e.g. score_thold = 90, then the chance of false rejected matching faces is quite high (FRR=0.7).
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.GetPassiveLiveness(System.Drawing.Image)">
            IFACE_GetPassiveLiveness image returns=score
            <param name="image">
            Image data used for detection
            </param>
            <returns>
            Impostor score in range &lt;-10000, 10000&gt;. Lower values indicates that the image is an impostor.
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.GetPassiveLiveness(System.String)">
            IFACE_GetPassiveLiveness filePath returns=score
            <param name="filePath">
            path to image file
            </param>
            <returns>
            Impostor score in range &lt;-10000, 10000&gt;. Lower values indicates that the image is an impostor.
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.GetPassiveLiveness(Innovatrics.IFace.IFaceRawImage)">
            IFACE_GetPassiveLiveness image returns=score
            <param name="image">
            Image data used for detection
            </param>
            <returns>
            Impostor score in range &lt;-10000, 10000&gt;. Lower values indicates that the image is an impostor.
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.GetTemplateInfo(System.Byte[])">
            <summary>
            Retrieves information about verification template
            </summary>
            <param name="faceTemplate">
            Face verification template
            </param>
            <returns>
            Information about verification template
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.FaceHandler.AverageTemplates(System.Byte[][])">
            <summary>
            Averages templates given as input
            </summary>
            <param name="inTemplateArray">
            Array of templates to be averaged
            </param>
            <returns>
            Averaged template
            </returns>
        </member>
        <member name="T:Innovatrics.IFace.IFace">
            <summary>
            The base class of the the IFace detection library.
            Use this class for the library initialization and termination, setting logger and global parameters, etc.
            </summary>
        </member>
        <member name="P:Innovatrics.IFace.IFace.Instance">
            <summary>
            Returns instance of IFace class
            </summary>
        </member>
        <member name="M:Innovatrics.IFace.IFace.Init">
            <summary>
            Initializes IFace SDK and checks validity of IFace SDK software license from license file.
            </summary>
        </member>
        <member name="M:Innovatrics.IFace.IFace.InitWithLicense(System.Byte[])">
            <summary>
            Initializes IFACE SDK and checks validity of IFace SDK software license from given license data string.
            </summary>
            <param name="licenseData">
            License data
            </param>
        </member>
        <member name="M:Innovatrics.IFace.IFace.Terminate">
            <summary>
            Terminates IFace and releases all used resources.
            </summary>
        </member>
        <member name="M:Innovatrics.IFace.IFace.GetVersion">
            <summary>
            Returns the library version.
            </summary>
            <returns>
            Version of the library
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.IFace.GetProductString">
            <summary>
            Returns product name and version formatted as string (e.g., IFace SDK v3.2.4.0)
            </summary>
            <returns>
            Product name and version formatted as string (e.g., IFace SDK v3.2.4.0)
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.IFace.GetHardwareId">
            <summary>
            Returns hardware ID of device for licensing purposes.
            </summary>
            <returns>
            String containing hardware ID
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.IFace.GetLicenseValue(System.String)">
            <summary>
            Returns value of given key from actually used license.
            </summary>
            <returns>
            String containing value of given license key
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.IFace.SetLogger(Innovatrics.IFace.LoggerSeverityLevel,System.String)">
            <summary>
            Initializes and turns on IFace logger. Log messages related to IFace processing having different severity levels are written into defined file or to stdout.
            </summary>
            <param name="severityLevel">
            Logging severity level. Only events >= severityLevel are logged.
            </param>
            <param name="dstPath">
            String containing path to the log file
            </param>
        </member>
        <member name="M:Innovatrics.IFace.IFace.GetParam(System.String)">
            <summary>
            Gets user parameter value.
            </summary>
            <param name="parameterName">
            Parameter name
            </param>
            <returns>
            Parameter value
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.IFace.SetParam(System.String,System.String)">
            <param name="parameterName">
            Parameter name
            </param>
            <param name="parameterValue">
            Parameter value
            </param>
        </member>
        <member name="M:Innovatrics.IFace.IFace.GetParamInternal(System.IntPtr,System.String)">
            <summary>
            Gets user parameter size.
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.IFaceEntity">
            <summary>
            Base class which holds native IFace objects
            </summary>
            <seealso cref="T:System.IDisposable" />
        </member>
        <member name="M:Innovatrics.IFace.IFaceEntity.Dispose">
            <summary>
            Releases all types of data entities (face, faceHandler, objectHandler, object, etc).
            </summary>
        </member>
        <member name="M:Innovatrics.IFace.IFaceEntity.op_Implicit(Innovatrics.IFace.IFaceEntity)~System.IntPtr">
            <summary>
            Performs an implicit conversion from <see cref="T:Innovatrics.IFace.IFaceEntity"/> to <see cref="T:System.IntPtr"/>.
            </summary>
            <param name="c">The c.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="T:Innovatrics.IFace.IFaceException">
            <summary>
            Exception thrown by IFace library functions
            </summary>
        </member>
        <member name="P:Innovatrics.IFace.IFaceException.Message">
            <summary>
            Returns error message describing given error code
            </summary>
        </member>
        <member name="P:Innovatrics.IFace.IFaceException.Code">
            <summary>
            Error code returned from IFace functions
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.TemplateInfo">
            <summary>
            Support class wrapping information about verification template returned by <see cref="M:Innovatrics.IFace.FaceHandler.GetTemplateInfo(System.Byte[])"/>
            </summary>
        </member>
        <member name="P:Innovatrics.IFace.TemplateInfo.Version">
            <summary>
            Version of the verification template
            </summary>
        </member>
        <member name="P:Innovatrics.IFace.TemplateInfo.Quality">
            <summary>
            Quality of the verification template, 0 - undefined, 1 - min quality, 255 - max quality
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.TrajectoryElement">
            <summary>
            Support class wrapping timestamp of the bounding boxes of the object returned by <see cref="M:Innovatrics.IFace.VisualObject.GetTrajectory(System.Int32,Innovatrics.IFace.VisualObjectHandler)"/>
            </summary>
        </member>
        <member name="P:Innovatrics.IFace.TrajectoryElement.Time">
            <summary>
            Timestamp of the bounding boxes of the object
            </summary>
        </member>
        <member name="P:Innovatrics.IFace.TrajectoryElement.BoundingBox">
            <summary>
            BoundingBox boxes of the object
            </summary>
        </member>
        <member name="T:Innovatrics.IFace.VisualObject">
            <summary>
            Holds data about tracked object
            </summary>
            <seealso cref="T:Innovatrics.IFace.IFaceEntity" />
        </member>
        <member name="P:Innovatrics.IFace.VisualObject.DefaultObjectHandler">
            <summary>
            Gets the default object handler.
            </summary>
            <value>
            The default object handler.
            </value>
        </member>
        <member name="M:Innovatrics.IFace.VisualObject.#ctor(Innovatrics.IFace.VisualObjectHandler)">
            <summary>
            Creates empty object entity.
            </summary>
        </member>
        <member name="M:Innovatrics.IFace.VisualObject.Clean(Innovatrics.IFace.VisualObjectHandler)">
            <summary>
            Cleans object entity. The function deletes all internal object data (e.g. tracking information).
            </summary>
            <param name="objectHandler">
            Pointer to objectHandler entity
            </param>
        </member>
        <member name="M:Innovatrics.IFace.VisualObject.GetId(Innovatrics.IFace.VisualObjectHandler)">
            <summary>
            Returns object id from given object entity.
            </summary>
            <param name="objectHandler">
            Pointer to objectHandler entity
            </param>
            <returns>
            Id of the object
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.VisualObject.GetBoundingBox(Innovatrics.IFace.VisualObjectHandler)">
            <summary>
            Returns object bounding box from given object entity.
            </summary>
            <param name="objectHandler">
            Pointer to objectHandler entity
            </param>
            <returns>
            Bounding box of the object
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.VisualObject.GetTrajectory(System.Int32,Innovatrics.IFace.VisualObjectHandler)">
            <summary>
            Returns object bounding box trajectory from given object entity.
            </summary>
            <param name="trajectoryLength">
            In: number of wanted bounding boxes from the tracking history (most recent), out: number of returned bounding boxes as output. If 0 or negative value is given, the length of the full history is returned.
            </param>
            <param name="objectHandler">
            Pointer to objectHandler entity
            </param>
            <returns>
            Bounding boxes of the objects
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.VisualObject.GetState(Innovatrics.IFace.VisualObjectHandler)">
            <summary>
            Returns object state from given object entity.
            </summary>
            <param name="objectHandler">
            Pointer to objectHandler entity
            </param>
            <returns>
            State of the object (see details in <c><see cref="T:Innovatrics.IFace.TrackedObjectState"/></c>)
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.VisualObject.GetType(Innovatrics.IFace.VisualObjectHandler)">
            <summary>
            Returns object type from given object entity.
            </summary>
            <param name="objectHandler">
            Pointer to objectHandler entity
            </param>
            <returns>
            Type of the object (see details in <c>IFace_TrackedObjectType</c>)
            </returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Innovatrics.IFace.VisualObject.GetScore(Innovatrics.IFace.VisualObjectHandler)" -->
        <member name="M:Innovatrics.IFace.VisualObject.GetFace(Innovatrics.IFace.TrackedObjectFaceType,Innovatrics.IFace.VisualObjectHandler)">
            <summary>
            Function returns particular type of face entity related to given object entity.
            </summary>
            <remarks>
            If face is not detected, result will be null
            </remarks>
            <param name="typeOfFace">
            Type of wanted face entity (see <c><see cref="T:Innovatrics.IFace.TrackedObjectFaceType"/></c>)
            </param>
            <param name="objectHandler">
            Pointer to objectHandler entity
            </param>
            <returns>
            Face entity stored within object entity copied to given preallocated face entity
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.VisualObject.GetTimeAppearance(Innovatrics.IFace.VisualObjectHandler)">
            <summary>
            Returns the object tracking time range.
            </summary>
            <param name="objectHandler">
            Pointer to objectHandler entity
            </param>
            <returns>
            Time of appearance (tracking start) in ms
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.VisualObject.GetTimeLost(Innovatrics.IFace.VisualObjectHandler)">
            <summary>
            Returns the object tracking time range.
            </summary>
            <param name="objectHandler">
            Pointer to objectHandler entity
            </param>
            <returns>
            Time of disappearance (tracking end) in ms
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.VisualObject.Serialize">
            <summary>
            Serializes entity content to byte array. Face and object entities are supported.
            </summary>
            <returns>
            In: pointer to an allocated array that will be filled with serialized entity data, out: filled data
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.VisualObject.Deserialize(System.Byte[],Innovatrics.IFace.VisualObjectHandler)">
            <summary>
            Deserialize entity content from byte array. Face and object entities are supported.
            </summary>
            <param name="serializedEntity">
            Pointer to memory filled with serialized entity data
            </param>
            <param name="objectHandler">
            Pointer to objectHandler entity
            </param>
            <returns>
            In: pointer to a entity, out: pointer to the entity, filled with serialized entity data
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.VisualObject.GetLivenessScore(Innovatrics.IFace.VisualObjectHandler)">
            <summary>
            Returns the liveness score of the object if the liveness evaluation was successful (see details in <c><see cref="T:Innovatrics.IFace.LivenessState"/></c> and <c><see cref="M:Innovatrics.IFace.VisualObject.GetLivenessState(Innovatrics.IFace.VisualObjectHandler)"/></c>).
            </summary>
            <param name="objectHandler">
            Pointer to objectHandler entity
            </param>
            <returns>
            Liveness score of the object
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.VisualObject.GetLivenessState(Innovatrics.IFace.VisualObjectHandler)">
            <summary>
            Returns the liveness state of the object.
            </summary>
            <param name="objectHandler">
            Pointer to objectHandler entity
            </param>
            <returns>
            Liveness state of the object (see details in <c><see cref="T:Innovatrics.IFace.LivenessState"/></c>)
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.VisualObject.GetLivenessFramesCount(Innovatrics.IFace.VisualObjectHandler)">
            <summary>
            Returns number of registered frames of the object.
            </summary>
            <param name="objectHandler">
            Pointer to objectHandler entity
            </param>
            <returns>
            Number of registered frames of the object.
            </returns>
        </member>
        <member name="T:Innovatrics.IFace.VisualObjectHandler">
            <summary>
            VisualObjectHandler provides methods of TrackingApi
            </summary>
            <seealso cref="T:Innovatrics.IFace.IFaceHandler" />
            <seealso cref="T:Innovatrics.IFace.IFaceEntity" />
        </member>
        <member name="M:Innovatrics.IFace.VisualObjectHandler.#ctor(Innovatrics.IFace.FaceHandler)">
            <summary>
            Creates and initializes object handler entity.
            </summary>
        </member>
        <member name="M:Innovatrics.IFace.VisualObjectHandler.GetParam(System.String)">
            <summary>
            Gets user parameter value.
            </summary>
            <param name="parameterName">
            Parameter name
            </param>
            <returns>
            Parameter value
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.VisualObjectHandler.SetParam(System.String,System.String)">
            <param name="parameterName">
            Parameter name
            </param>
            <param name="parameterValue">
            Parameter value
            </param>
        </member>
        <member name="M:Innovatrics.IFace.VisualObjectHandler.TrackObjects(System.Drawing.Image,System.Int64,Innovatrics.IFace.VisualObject[])">
            <summary>
            Performs tracking of objects in video sequence. Just one video frame (<c>rawImage</c>, supplemented by time info <c>timeStampMs</c>) is processed in one call of <see cref="M:Innovatrics.IFace.IVisualObjectHandler.TrackObjects(System.Drawing.Image,System.Int64,Innovatrics.IFace.VisualObject[])"/> function. The state of tracked objects is stored in <c>objects</c> array. Objects of detection handler entity type (e.g. face handler entity for face objects) set to object handler entity (in IFACE_SetDetector call) are detected and tracked.
            </summary>
            <param name="rawImage">
            Pointer to raw image data, each pixel has 3 components (bytes) in BGR order
            </param>
            <param name="timeStampMs">
            Number of ms from the video first frame
            </param>
            <param name="objects">
            In: array of object entity pointers to be filled, out: tracked object entities
            </param>
        </member>
        <member name="M:Innovatrics.IFace.VisualObjectHandler.SetTrackingObjects(Innovatrics.IFace.VisualObject[],System.Drawing.Rectangle[])">
            <summary>
            Sets objects for tracking. Objects are defined by position (<c>xs</c>, <c>ys</c>) and size (<c>widths</c>, <c>heights</c>).
            </summary>
            <param name="objects">
            Array of pointers to object entities
            </param>
            <param name="objectBoundingBoxes">
            coordinates of objects bounding box
            </param>
        </member>
        <member name="M:Innovatrics.IFace.VisualObjectHandler.SetTrackingAreaMask(System.Drawing.Image)">
            <summary>
            Sets important areas of scene for tracking using given mask. The value (0, 0, 0) indicates unimportant image parts that are not searched during object tracking. Mask has to contain at least one unmasked, connected area in which a valid face can be identified and tracked (approximately it's pixel count should be above the second power of the minimum eye distance).
            </summary>
            <param name="rawImage">
            Pointer to raw image data, each pixel has a 3-channel component
            </param>
        </member>
        <member name="M:Innovatrics.IFace.VisualObjectHandler.EstimateUniqueObjects(Innovatrics.IFace.VisualObject[])">
            <summary>
            Estimates, merges and returns unique objects representing valid faces based on an array of objects.
            </summary>
            <param name="objects">
            Array of object entity pointers from which the unique objects are estimated
            </param>
            <returns>
            In: array of pointers to object entities, out: same entities filled with the unique object data
            </returns>
        </member>
        <member name="M:Innovatrics.IFace.VisualObjectHandler.SetLivenessDotPosition(System.Int32,System.Int32,System.Int64)">
            <param name="dotPositionX">
            Position of the dot on the screen along the X axis (0 is the leftmost side). Should be from the range &lt;0,100&gt;, however only values from ranges &lt;0,10&gt; and &lt;90,100&gt; are valid for X position.
            </param>
            <param name="dotPositionY">
            Position of the dot on the screen along the Y axis (0 is the topmost side). Should be from the range &lt;0,100&gt;, however only values from ranges &lt;0,20&gt; and &lt;80,100&gt; are valid for Y position.
            </param>
            <param name="timeStampMs">
            Number of milliseconds from the first frame of the video
            </param>
        </member>
    </members>
</doc>
